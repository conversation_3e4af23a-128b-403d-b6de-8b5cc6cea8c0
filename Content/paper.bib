@article{Beaucage_2019,
  title={Validation of an OpenSim full-body model with detailed lumbar spine for estimating lower lumbar spine loads during symmetric and asymmetric lifting tasks},
  author={<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON>},
  journal={Computer methods in biomechanics and biomedical engineering},
  DOI = {10.1080/10255842.2018.1564819},
  volume={22},
  number={5},
  pages={451--464},
  year={2019},
  publisher={<PERSON> \& Francis}
}

@article{Brad<PERSON>_2000,
  author = {<PERSON>, G.},
  citeulike-article-id = {2236121},
  journal = {<PERSON><PERSON>'s Journal of Software Tools},
  keywords = {bibtex-import},
  posted-at = {2008-01-15 19:21:54},
  priority = {4},
  title = {The OpenCV Library},
  year = {2000}
}

@article{Butterworth_1930,
  title={On the theory of filter amplifiers},
  author={<PERSON><PERSON><PERSON>, <PERSON>},
  journal={Wireless Engineer},
  volume={7},
  number={6},
  pages={536--541},
  year={1930}
}

@article{Cao_2019,
  title={OpenPose: realtime multi-person 2D pose estimation using Part Affinity Fields},
  author={<PERSON>, <PERSON> and <PERSON>, Gines and <PERSON>, <PERSON> and <PERSON>, Shih-En and Sheikh, Yaser},
  journal={IEEE transactions on pattern analysis and machine intelligence},
  volume={43},
  number={1},
  pages={172--186},
  year={2019},
  URL = {https://arxiv.org/abs/1611.08050},
  DOI = {10.1109/TPAMI.2019.2929257},
  publisher={IEEE}
}

@article{Cleveland_1981,
  title={LOWESS: A program for smoothing scatterplots by robust locally weighted regression},
  author={Cleveland, William S},
  DOI={10.2307/2683591},
  journal={American Statistician},
  volume={35},
  number={1},
  pages={54},
  year={1981}
}

@article{Colyer_2018,
  title={A review of the evolution of vision-based motion analysis and the integration of advanced computer vision methods towards developing a markerless system},
  author={Colyer, Steffi L and Evans, Murray and Cosker, Darren P and Salo, Aki IT},
  journal={Sports medicine-open},
  DOI={10.1186/s40798-018-0139-y},
  volume={4},
  number={1},
  pages={1--15},
  year={2018},
  publisher={SpringerOpen}
}

@article{Delp_2007,
  title={OpenSim: open-source software to create and analyze dynamic simulations of movement},
  author={Delp, Scott L and Anderson, Frank C and Arnold, Allison S and Loan, Peter and Habib, Ayman and John, Chand T and Guendelman, Eran and Thelen, Darryl G},
  journal={IEEE transactions on biomedical engineering},
  volume={54},
  number={11},
  pages={1940--1950},
  year={2007},
  URL = {https://ieeexplore.ieee.org/abstract/document/4352056},
  DOI = {10.1109/TBME.2007.901024},
  publisher={IEEE}
}

@inproceedings{Fang_2017,
  title={{RMPE}: Regional Multi-person Pose Estimation},
  author={Fang, Hao-Shu and Xie, Shuqin and Tai, Yu-Wing and Lu, Cewu},
  booktitle={ICCV},
  year={2017},
  URL = {https://ieeexplore.ieee.org/document/8237518},
  DOI = {10.1109/ICCV.2017.256}
}

@article{Hartley_1997,
  title={Triangulation},
  author={Hartley, Richard I and Sturm, Peter},
  journal={Computer vision and image understanding},
  DOI={10.1006/cviu.1997.0547},
  volume={68},
  number={2},
  pages={146--157},
  year={1997},
  publisher={Elsevier}
}

@misc{Hidalgo_2019,
  author = {Hidalgo, Ginés},
  title = {OpenPose Experimental Models},
  year = {2019},
  publisher = {GitHub},
  journal = {GitHub repository},
  url = {https://github.com/CMU-Perceptual-Computing-Lab/openpose_train/tree/master/experimental_models#body_25b-model---option-2-recommended}
}

@misc{Hidalgo_2021,
  author = {Hidalgo, Ginés},
  title = {OpenPose 3D reconstruction module},
  year = {2021},
  publisher = {GitHub},
  journal = {GitHub repository},
  url = {https://github.com/CMU-Perceptual-Computing-Lab/openpose/blob/master/doc/advanced/3d_reconstruction_module.md}
}

@article{Kanko_2021,
  title={Concurrent assessment of gait kinematics using marker-based and markerless motion capture},
  author={Kanko, Robert M and Laende, Elise K and Davis, Elysia M and Selbie, W Scott and Deluzio, Kevin J},
  journal={Journal of biomechanics},
  volume={127},
  pages={110665},
  year={2021},
  URL = {https://doi.org/10.1016/j.jbiomech.2021.110665},
  DOI = {10.1016/j.jbiomech.2021.110665},
  publisher={Elsevier}
}

@article{Karashchuk_2021,
  title={Anipose: a toolkit for robust markerless 3D pose estimation},
  author={Karashchuk, Pierre and Rupp, Katie L and Dickinson, Evyn S and Walling-Bell, Sarah and Sanders, Elischa and Azim, Eiman and Brunton, Bingni W and Tuthill, John C},
  journal={Cell reports},
  volume={36},
  number={13},
  pages={109730},
  year={2021},
  URL = {https://doi.org/10.1016/j.celrep.2021.109730},
  DOI = {10.1016/j.celrep.2021.109730},
  publisher={Elsevier}
}

@article{Mathis_2018,
  title={DeepLabCut: markerless pose estimation of user-defined body parts with deep learning},
  author={Mathis, Alexander and Mamidanna, Pranav and Cury, Kevin M and Abe, Taiga and Murthy, Venkatesh N and Mathis, Mackenzie Weygandt and Bethge, Matthias},
  journal={Nature neuroscience},
  volume={21},
  number={9},
  pages={1281--1289},
  year={2018},
  URL = {https://www.nature.com/articles/s41593-018-0209-y},
  DOI = {10.1038/s41593-018-0209-y},
  publisher={Nature Publishing Group}
}

 @misc{Matthis_2022, 
  type={Python},
  title={FreeMoCap: A free, open source markerless motion capture system},
  rights={AGPL-3.0}, 
  url={https://github.com/freemocap/freemocap}, 
  author={Matthis, Jonathan Samir and Cherian, Aaron}, 
  year={2022}, 
  publisher = {GitHub},
  journal = {GitHub repository},
}

@article{Needham_2021,
  title={The accuracy of several pose estimation methods for 3D joint centre localisation},
  author={Needham, Laurie and Evans, Murray and Cosker, Darren P and Wade, Logan and McGuigan, Polly M and Bilzon, James L and Colyer, Steffi L},
  journal={Scientific reports},
  DOI={10.1038/s41598-021-00212-x},
  volume={11},
  number={1},
  pages={1--11},
  year={2021},
  publisher={Nature Publishing Group}
}

@article{Pagnon_2021,
  title={Pose2Sim: An End-to-End Workflow for 3D Markerless Sports Kinematics—Part 1: Robustness},
  author={Pagnon, David and Domalain, Mathieu and Reveret, Lionel},
  journal={Sensors},
  volume={21},
  number={19},
  year={2021},
  URL = {https://www.mdpi.com/1424-8220/21/19/6530},
  DOI = {10.3390/s21196530},
  publisher={Multidisciplinary Digital Publishing Institute}
}

@article{Pagnon_2022,
  title = {Pose2Sim: An End-to-End Workflow for 3D Markerless Sports Kinematics—Part 2: Accuracy},
  author = {Pagnon, David and Domalain, Mathieu and Reveret, Lionel},
  journal = {Sensors},
  volume={22},
  number={7},
  year={2022},
  URL = {https://www.mdpi.com/1424-8220/22/7/2712},
  DOI = {10.3390/s22072712},
  publisher={Multidisciplinary Digital Publishing Institute}
}

@article{Rajagopal_2016,
  title={Full-body musculoskeletal model for muscle-driven simulation of human gait},
  author={Rajagopal, Apoorva and Dembia, Christopher L and DeMers, Matthew S and Delp, Denny D and Hicks, Jennifer L and Delp, Scott L},
  journal={IEEE transactions on biomedical engineering},
  DOI={10.1109/tbme.2016.2586891},
  volume={63},
  number={10},
  pages={2068--2079},
  year={2016},
  publisher={IEEE}
}

@article{Seth_2018,
  DOI = {10.1371/journal.pcbi.1006223},
  author = {Seth, Ajay AND Hicks, Jennifer L. AND Uchida, Thomas K. AND Habib, Ayman AND Dembia, Christopher L. AND Dunne, James J. AND Ong, Carmichael F. AND DeMers, Matthew S. AND Rajagopal, Apoorva AND Millard, Matthew AND Hamner, Samuel R. AND Arnold, Edith M. AND Yong, Jennifer R. AND Lakshmikanth, Shrinidhi K. AND Sherman, Michael A. AND Ku, Joy P. AND Delp, Scott L.},
  journal = {PLOS Computational Biology},
  publisher = {Public Library of Science},
  title = {OpenSim: Simulating musculoskeletal dynamics and neuromuscular control to study human and animal movement},
  year = {2018},
  month = {07},
  volume = {14},
  url = {https://doi.org/10.1371/journal.pcbi.1006223},
  pages = {1-20},
  number = {7},
}

@article{Sheshadri_2020, 
  DOI={10.21105/joss.01849}, 
  url = {https://doi.org/10.21105/joss.01849}, 
  year = {2020}, 
  publisher = {The Open Journal}, 
  volume = {5}, 
  number = {45}, 
  pages = {1849}, 
  author = {Sheshadri, Swathi and Dann, Benjamin and Hueser, Timo and Scherberger, Hansjoerg}, 
  title = {3D reconstruction toolbox for behavior tracked with multiple cameras}, 
  journal = {Journal of Open Source Software} 
}

 @article{Uhlrich_2022,
  title={OpenCap: 3D human movement dynamics from smartphone videos},
  url={https://www.biorxiv.org/content/10.1101/2022.07.07.499061v1}, 
  DOI={10.1101/2022.07.07.499061}, 
  publisher={bioRxiv}, 
  author={Uhlrich, Scott D. and Falisse, Antoine and Kidziński, Łukasz and Muccini, Julie and Ko, Michael and Chaudhari, Akshay S. and Hicks, Jennifer L. and Delp, Scott L.}, 
  year={2022}, 
  month={Jul}, 
  pages={2022.07.07.499061}
}

@article{Zeni_2008,
  title={Two simple methods for determining gait events during treadmill and overground walking using kinematic data},
  author={Zeni Jr, JA and Richards, JG and Higginson, JS2384115},
  journal={Gait \& posture},
  volume={27},
  number={4},
  pages={710--714},
  year={2008},
  URL={https://doi.org/10.1016/j.gaitpost.2007.07.007},
  DOI={10.1016/j.gaitpost.2007.07.007},
  publisher={Elsevier}
}

@article{Zhang_2000,
  title={A flexible new technique for camera calibration},
  author={Zhang, Zhengyou},
  journal={IEEE Transactions on pattern analysis and machine intelligence},
  DOI={10.1109/34.888718},
  volume={22},
  number={11},
  pages={1330--1334},
  year={2000},
  publisher={IEEE}
}

@article{Zheng_2022,
  title={Deep learning-based human pose estimation: A survey},
  author={Zheng, Ce and Wu, Wenhan and Yang, Taojiannan and Zhu, Sijie and Chen, Chen and Liu, Ruixu and Shen, Ju and Kehtarnavaz, Nasser and Shah, Mubarak},
  journal={arXiv},
  year={2022},
  URL={https://doi.org/10.48550/arXiv.2012.13392},
  DOI={10.48550/arXiv.2012.13392},
}
