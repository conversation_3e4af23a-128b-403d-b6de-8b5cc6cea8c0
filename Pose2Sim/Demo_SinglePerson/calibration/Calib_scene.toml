[int_cam01_img]
name = "int_cam01_img"
size = [ 1088.0, 1920.0]
matrix = [ [ 1671.5012042021037, 0.0, 564.6403499852015], [ 0.0, 1671.3866928599075, 933.0074897877159], [ 0.0, 0.0, 1.0]]
distortions = [ -0.05310342256182765, 0.1510044676505964, -0.00011989435671705446, 0.0006503403122568441]
rotation = [ 1.678784562972848, 1.031972654261743, -0.3910235872501538]
translation = [ 0.2609476905995074, 0.9641586618892432, 2.932963441842366]
fisheye = false

[int_cam02_img]
name = "int_cam02_img"
size = [ 1088.0, 1920.0]
matrix = [ [ 1675.7080607483433, 0.0, 555.7798754387691], [ 0.0, 1675.4079150009259, 937.7259328417255], [ 0.0, 0.0, 1.0]]
distortions = [ -0.05205993968544501, 0.14917865330192143, 1.6726989091268645e-05, -0.0006545682909903758]
rotation = [ 1.3578601662680128, 1.5796573692170839, -1.1822007045082419]
translation = [ -0.16526980322189794, 0.8084087327474073, 3.2044946004620805]
fisheye = false

[int_cam03_img]
name = "int_cam03_img"
size = [ 1088.0, 1920.0]
matrix = [ [ 1678.7524092974595, 0.0, 556.0712835099544], [ 0.0, 1678.1875775424717, 928.197809795709], [ 0.0, 0.0, 1.0]]
distortions = [ -0.031226100230109667, 0.009201607173964147, -0.0008794344390757962, -2.031259635506541e-05]
rotation = [ 0.7860494371239335, -2.186387571217796, 1.4336044493611793]
translation = [ -0.8504307571967306, 0.41363741965310824, 4.297890768705867]
fisheye = false

[int_cam04_img]
name = "int_cam04_img"
size = [ 1088.0, 1920.0]
matrix = [ [ 1690.8621773209618, 0.0, 535.7642414457736], [ 0.0, 1689.2410845215763, 933.0004533663628], [ 0.0, 0.0, 1.0]]
distortions = [ -0.04718570631606589, 0.10068388229636667, 0.002421622328944551, -0.0010513343762849723]
rotation = [ 1.4051886526650297, -1.3909627084708258, 0.4436653908561921]
translation = [ 0.5111616284766533, 0.11254959801153527, 4.410359951259428]
fisheye = false

[metadata]
adjusted = false
error = 0.0
