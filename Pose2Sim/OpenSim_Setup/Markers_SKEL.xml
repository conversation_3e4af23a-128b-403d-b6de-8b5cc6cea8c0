<?xml version="1.0" encoding="UTF-8" ?>
<OpenSimDocument Version="40500">
	<!-- BSM/SMPL SKEL markers for Model_Pose2Sim.osim.-->
	<MarkerSet name="markerset">
		<objects>
			<Marker name="BLTI">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.040716888018669996 -0.27075624669713572 -0.010954890491470219</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="<PERSON><PERSON>">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.044873110518149559 -0.27247002555278421 0.0066618946758649922</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="C7">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.074521189684532543 0.3856703898472274 0.0031813856047155156</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="CHIN">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/head</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.095059558443786246 0.47709300000129795 0.0022816099913465066</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="CLAV">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.055768947916159418 0.3581936025529926 -0.00039751817684692981</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="FBBT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.012650107688354359 -0.30123722391209512 -0.022631806935135636</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="FLTI">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.013295970904445004 -0.19167620271615279 0.029961199227575622</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="FRBT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.02738150948803892 -0.32109990034820635 0.023774370876438428</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="FRTI">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.012317855652660846 -0.19544287171424068 -0.024143213160708315</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LAKI">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>1.3283810815506529e-06 -0.3975109670679397 0.030979161197767766</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LANK">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.003261357067575796 -0.41234520591713086 -0.048497124188215446</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LBAC">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.0945989 0.2586559683689193 -0.091451018451185251</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LBHD">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/head</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.049045417100000002 0.53803460910000001 -0.046163323700000002</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LBWT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/pelvis</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.16597706776547685 0.04344884075147383 -0.035376427699999997</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LELB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/ulna_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.040158636648352085 -0.0023236848644869307 -0.0060009072211909941</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LELS">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0 -0.28420000001120066 0.042753818842211015</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LELSO">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.015437151049091376 -0.28553200000955226 -0.034974390156691189</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LFBB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.060595110185786456 -0.30983570559460472 -0.0053473635085838633</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LFBT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.090180074299999996 -0.179271814 0.00022214930000000001</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LFFB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.069759421394806481 -0.35756588130022449 0.0026087644073298625</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LFFT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.047060635000000003 -0.1647846077 0.044699793799999998</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LFHD">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/head</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.065110267400000005 0.586982275 -0.060305826700000002</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LFIN">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.013964651503174989 -0.084352356512349749 0.0058398516926367805</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LFLB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.014556778217168203 -0.32238852518998912 -0.056459073210368242</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LFLT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.0133129502 -0.15829962349999999 -0.087803531000000004</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LFMB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.013160843904555691 -0.31051068116442321 0.056024854575762735</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LFMT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.0129932821 -0.158642371 0.081508101400000005</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LFPI">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/toes_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0132 -0.0067190000000000001 -0.051954</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LFRM">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/ulna_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.058949750540094159 -0.09656185148136838 -0.018438526329154686</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LFWT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/pelvis</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0060000000000000001 0.014151858963826498 -0.1158530047</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LHAP">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0056814466944708538 -0.10122994615996538 0.033276266378065571</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LHBA">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.046800526222073854 -0.16574154541267475 0.021800876905762621</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LHEB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.014687 -0.023699000000000001 0.010602</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LHEE">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.013293853979739718 0.01602225045808332 0.0045696725186606633</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LHFR">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.027814736215251386 -0.15238153446064381 0.016123682171162404</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LHME">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.023131977000516085 -0.14464869935583807 0.00098725689550969867</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LHPI">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0012299304742150947 -0.071623478197119908 0.044043276128287213</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LHTH">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.012082328955297129 -0.046743295100581873 -0.056878360119709884</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LHTO">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.00099897760000000006 -0.088551745295289058 -0.034957193953115015</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LKNE">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.0062944523000000004 -0.40743083000000002 -0.045640236899999999</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LKNI">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.0011692119 -0.4080589584 0.0465546643</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LMT5">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.20507421719999999 -0.0017752675 -0.0105199783</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LSCA">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.089942300000000003 0.36458411553359216 -0.076523599999999997</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LSHN">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.047578392955780528 -0.10726081131606641 -0.061883988960047757</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LSHO">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.018539199999999999 0.40812773794927715 -0.14923</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LTIA">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.048261308716922927 -0.082370646060121316 -0.0081620358602870167</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LTIB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.029728228659415056 -0.21642813415446632 -0.012664039452638566</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LTIC">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.024271829038645585 -0.33336984997437713 -0.0075140266206905199</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="LTOE">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/toes_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.065004000000000006 7.6000000000000004e-05 0.011990000000000001</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LTOP">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.14022899999999999 -0.0080219999999999996 -0.054751000000000001</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LTOS">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.174425 0.001805 0.041132000000000002</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LUMB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.079944600000000005 0.134934 0</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LUMC">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/lumbar1</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.070367100000000002 0.0116385 0</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LWRA">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/radius_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.013233205156614494 -0.22413690126493618 -0.045661867911345765</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LWRB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/ulna_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.038991528199107402 -0.23345354398820012 -0.021649555469394753</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="NOSE">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/head</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.095577368821059883 0.56705464100106462 0.0014871217929023552</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RAKI">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0015267389529387495 -0.39154399869282541 -0.031134167379082128</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RANK">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.018222126917264044 -0.41694075286308613 0.046907526053498828</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RBAC">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.094598870583268505 0.25515029484134105 0.089960885577467967</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RBHD">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/head</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.048382569700000003 0.53939846650000001 0.044864508900000002</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RBWT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/pelvis</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.16398843571432059 0.039853295905567222 0.044460406299999998</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RELB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/ulna_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.037784999999999999 0.0023240000000000001 0.0018990000000000001</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RELS">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0 -0.28420000000906082 -0.044172342106097093</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RELSO">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.013638533236292346 -0.28553200000792722 0.03389711713815103</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RFBB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.059032734285299891 -0.30682143907561343 -0.0054271228911222848</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RFBT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.083163382699999996 -0.1732923687 -0.018782584799999998</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RFFB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.072354839599999984 -0.36065902368944031 0.0072941551096005735</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RFFT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.061013773100000002 -0.17225784159999999 -0.033155063800000002</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RFHD">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/head</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.064413058600000003 0.58621170700000003 0.061317611299999998</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RFIN">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.0042949433445918948 -0.085915733906678532 0.001617945253985074</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RFLB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.0071713156753199572 -0.31524725957657135 0.053554960914904803</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RFLT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0010080613000000001 -0.15655275430000001 0.088553331299999996</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RFMB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0050615792028781883 -0.31385820386553848 -0.064047041791747875</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RFMT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0038371904 -0.16414764039999999 -0.078241712599999999</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RFPI">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/toes_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0132 -0.0067190000000000001 0.051954</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RFRM">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/ulna_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.044628697603972499 -0.085312131005712902 0.01082737393316033</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RFWT">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/pelvis</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0060000000000000001 0.015279264126829957 0.1158530047</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RHAP">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0178783894 -0.1214275816 -0.028290384299999999</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RHBA">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.036116845308263576 -0.18715054130595252 -0.0065233243760979892</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RHEB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.014687 -0.023699000000000001 -0.010602</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RHEE">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.013294123583031656 0.017900345284878261 0.00061929763296403274</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RHFR">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.039827400250085125 -0.19765057522138901 -0.0070454367281109426</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RHME">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.043296145404970045 -0.15326119324666057 -0.010130960657824909</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RHPI">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0039193028787793318 -0.072530502985495487 -0.038756722861344926</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RHTH">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0053822277831633136 -0.046835030960500956 0.057911593983236316</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RHTO">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0033158562999999999 -0.15248297320000001 0.032565353399999997</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RIBL">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.07812519353835129 0.12154211963769149 -0.14699399214964848</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RIBR">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.092070600000000002 0.121542 0.128414</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RITL">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.061864500000000003 0.33013087573653349 -0.058625007601138597</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RITR">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.061864500000000003 0.33013100000000001 0.058624999999999997</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RKNE">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0025471192000000001 -0.41002520619999999 0.049998745499999997</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RKNI">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.0041299077999999998 -0.40238560249999999 -0.045284801</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RMT5">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.2057972428 -0.0023267875 0.0042648473999999997</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RSCA">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.089942300000000003 0.36305952649281698 0.076523581954041589</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RSHN">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.024008247908892694 -0.19820570840180185 0.070658020001919591</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RSHO">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.018539204319982901 0.4059920186695396 0.14923005221420216</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RTIA">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.034805930269412297 -0.081649495751499268 -0.0017548734833628234</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RTIB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.029727 -0.20083000000000001 0.012664</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RTIC">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.024273153280934944 -0.34890532418351305 0.0032270301961430502</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
			<Marker name="RTOE">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/toes_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.065004000000000006 7.6000000000000004e-05 -0.011990000000000001</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RTOP">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.14022899999999999 -0.0080219999999999996 0.054751000000000001</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RTOS">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.174425 0.001805 -0.041132000000000002</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RWRA">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/radius_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.0087954793654759743 -0.23290898754291783 0.044611808240884204</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RWRB">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/ulna_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.038419883248906772 -0.24086765888719122 0.020573119722938366</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="THD">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/head</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.014101215199999999 0.65772077659999995 0.00088179630000000001</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
		</objects>
		<groups />
	</MarkerSet>
</OpenSimDocument>
