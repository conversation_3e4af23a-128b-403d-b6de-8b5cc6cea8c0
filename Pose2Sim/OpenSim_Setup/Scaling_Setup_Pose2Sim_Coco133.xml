<?xml version="1.0" encoding="UTF-8" ?>
<OpenSimDocument Version="40000">
	<ScaleTool name="Pose2Sim_scaled">
		<!--Mass of the subject in kg.  Subject-specific model generated by scaling step will have this total mass.-->
		<mass>69</mass>
		<!--Height of the subject in mm.  For informational purposes only (not used by scaling).-->
		<height>-1</height>
		<!--Age of the subject in years.  For informational purposes only (not used by scaling).-->
		<age>-1</age>
		<!--Notes for the subject.-->
		<notes>Unassigned</notes>
		<!--Specifies the name of the unscaled model (.osim) and the marker set.-->
		<GenericModelMaker>
			<!--Model file (.osim) for the unscaled model.-->
			<model_file>Unassigned</model_file>
			<!--Set of model markers used to scale the model. Scaling is done based on distances between model markers compared to the same distances between the corresponding experimental markers.-->
			<marker_set_file>Unassigned</marker_set_file>
		</GenericModelMaker>
		<!--Specifies parameters for scaling the model.-->
		<ModelScaler>
			<!--Whether or not to use the model scaler during scale-->
			<apply>true</apply>
			<!--Specifies the scaling method and order. Valid options are 'measurements', 'manualScale', singly or both in any sequence.-->
			<scaling_order> measurements manualScale</scaling_order>
			<!--Specifies the measurements by which body segments are to be scaled.-->
			<MeasurementSet>
				<objects>
					<Measurement name="torso">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LShoulder LHip</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RShoulder RHip</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="lumbar5">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="lumbar4">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="lumbar3">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="lumbar2">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="lumbar1">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="torso">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="head">
                                    <!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
                                </BodyScale>
								<BodyScale name="sacrum">
                                    <!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
                                </BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="arm">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LShoulder LElbow</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RShoulder RElbow</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="humerus_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="humerus_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="forearm">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LElbow LWrist</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RElbow RWrist</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="ulna_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="radius_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="hand_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="ulna_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="radius_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="hand_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="thigh">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LHip LKnee</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RHip RKnee</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="femur_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="patella_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="femur_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="patella_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="shank">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LKnee LAnkle</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RKnee RAnkle</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="tibia_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="tibia_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="foot">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> LHeel LBigToe</markers>
								</MarkerPair>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RHeel RBigToe</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="talus_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="calcn_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="toes_r">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="talus_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="calcn_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="toes_l">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
					<Measurement name="pelvis">
						<!--Flag to turn on and off scaling for this measurement.-->
						<apply>true</apply>
						<!--Set of marker pairs used to determine the scale factors.-->
						<MarkerPairSet>
							<objects>
								<MarkerPair>
									<!--Names of two markers, the distance between which is used to compute a body scale factor.-->
									<markers> RHip LHip</markers>
								</MarkerPair>
							</objects>
							<groups />
						</MarkerPairSet>
						<!--Set of bodies to be scaled by this measurement.-->
						<BodyScaleSet>
							<objects>
								<BodyScale name="pelvis">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
								<BodyScale name="sacrum">
									<!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
									<axes> X Y Z</axes>
								</BodyScale>
							</objects>
							<groups />
						</BodyScaleSet>
					</Measurement>
                    <Measurement name="head">
                        <!--Flag to turn on and off scaling for this measurement.-->
                        <apply>true</apply>
                        <!--Set of marker pairs used to determine the scale factors.-->
                        <MarkerPairSet>
                            <objects>
                                <MarkerPair>
                                    <!--Names of two markers, the distance between which is used to compute a body scale factor.-->
                                    <markers> Nose REye</markers>
                                </MarkerPair>
                                <MarkerPair>
                                    <!--Names of two markers, the distance between which is used to compute a body scale factor.-->
                                    <markers> Nose LEye</markers>
                                </MarkerPair>
                            </objects>
                            <groups />
                        </MarkerPairSet>
                        <!--Set of bodies to be scaled by this measurement.-->
                        <BodyScaleSet>
                            <objects>
                                <BodyScale name="head">
                                    <!--Axes (X Y Z) along which to scale a body. For example, 'X Y Z' scales along all three axes, and 'Y' scales just along the Y axis.-->
                                    <axes> X Y Z</axes>
                                </BodyScale>
                            </objects>
                            <groups />
                        </BodyScaleSet>
                    </Measurement>
				</objects>
				<groups />
			</MeasurementSet>
			<!--Scale factors to be used for manual scaling.-->
			<ScaleSet>
				<objects>
					<Scale>
						<scales> 0.95 0.95 0.95</scales>
						<segment>pelvis</segment>
						<apply>false</apply>
					</Scale>
					<Scale>
						<scales> 0.85 0.85 0.85</scales>
						<segment>patella_r</segment>
						<apply>false</apply>
					</Scale>
					<Scale>
						<scales> 0.85 0.85 0.85</scales>
						<segment>talus_r</segment>
						<apply>false</apply>
					</Scale>
					<Scale>
						<scales> 0.85 0.85 0.85</scales>
						<segment>toes_r</segment>
						<apply>false</apply>
					</Scale>
					<Scale>
						<scales> 0.85 0.85 0.85</scales>
						<segment>patella_l</segment>
						<apply>false</apply>
					</Scale>
					<Scale>
						<scales> 0.85 0.85 0.85</scales>
						<segment>talus_l</segment>
						<apply>false</apply>
					</Scale>
					<Scale>
						<scales> 0.85 0.85 0.85</scales>
						<segment>toes_l</segment>
						<apply>false</apply>
					</Scale>
					<Scale>
						<scales> 0.8 0.8 0.8</scales>
						<segment>torso</segment>
						<apply>false</apply>
					</Scale>
					<Scale>
						<scales> 0.8 0.8 0.8</scales>
						<segment>head</segment>
						<apply>false</apply>
					</Scale>
				</objects>
				<groups />
			</ScaleSet>
			<!--TRC file (.trc) containing the marker positions used for measurement-based scaling. This is usually a static trial, but doesn't need to be.  The marker-pair distances are computed for each time step in the TRC file and averaged across the time range.-->
			<marker_file>../S00_Demo_Session/S00_P00_SingleParticipant/S00_P00_T00_StaticTrial/pose-3d/S00_P00_T00_StaticTrial_0-12.trc</marker_file>
			<!--Time range over which to average marker-pair distances in the marker file (.trc) for measurement-based scaling.-->
			<time_range> 0.0166667 0.1</time_range>
			<!--Flag (true or false) indicating whether or not to preserve relative mass between segments.-->
			<preserve_mass_distribution>true</preserve_mass_distribution>
			<!--Name of OpenSim model file (.osim) to write when done scaling.-->
			<output_model_file>Unassigned</output_model_file>
			<!--Name of file to write containing the scale factors that were applied to the unscaled model (optional).-->
			<output_scale_file>Unassigned</output_scale_file>
		</ModelScaler>
		<!--Specifies parameters for placing markers on the model once a model is scaled. -->
		<MarkerPlacer>
			<!--Whether or not to use the marker placer during scale-->
			<apply>false</apply>
			<!--Task set used to specify weights used in the IK computation of the static pose.-->
			<IKTaskSet>
				<objects>
					<IKMarkerTask name="Nose">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>0.10000000000000001</weight>
					</IKMarkerTask>
					<IKMarkerTask name="REye">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>0.10000000000000001</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LEye">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LShoulder">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>2</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RShoulder">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>2</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LElbow">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RElbow">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LWrist">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RWrist">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LHip">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>2</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RHip">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>2</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LKnee">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RKnee">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LAnkle">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RAnkle">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LBigToe">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LSmallToe">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="LHeel">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RBigToe">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RSmallToe">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKMarkerTask name="RHeel">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>1</weight>
					</IKMarkerTask>
					<IKCoordinateTask name="pelvis_tilt">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>0</weight>
						<!--Indicates the source of the coordinate value for this task.  Possible values are default_value (use default value of coordinate, as specified in the model file, as the fixed target value), manual_value (use the value specified in the value property of this task as the fixed target value), or from_file (use the coordinate values from the coordinate data specified by the coordinates_file property).-->
						<value_type>default_value</value_type>
						<!--This value will be used as the desired (or prescribed) coordinate value if value_type is set to manual_value.-->
						<value>0</value>
					</IKCoordinateTask>
					<IKCoordinateTask name="pelvis_list">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>0.1</weight>
						<!--Indicates the source of the coordinate value for this task.  Possible values are default_value (use default value of coordinate, as specified in the model file, as the fixed target value), manual_value (use the value specified in the value property of this task as the fixed target value), or from_file (use the coordinate values from the coordinate data specified by the coordinates_file property).-->
						<value_type>default_value</value_type>
						<!--This value will be used as the desired (or prescribed) coordinate value if value_type is set to manual_value.-->
						<value>0</value>
					</IKCoordinateTask>
					<IKCoordinateTask name="ankle_angle_r">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>0</weight>
						<!--Indicates the source of the coordinate value for this task.  Possible values are default_value (use default value of coordinate, as specified in the model file, as the fixed target value), manual_value (use the value specified in the value property of this task as the fixed target value), or from_file (use the coordinate values from the coordinate data specified by the coordinates_file property).-->
						<value_type>manual_value</value_type>
						<!--This value will be used as the desired (or prescribed) coordinate value if value_type is set to manual_value.-->
						<value>0</value>
					</IKCoordinateTask>
					<IKCoordinateTask name="ankle_angle_l">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>0</weight>
						<!--Indicates the source of the coordinate value for this task.  Possible values are default_value (use default value of coordinate, as specified in the model file, as the fixed target value), manual_value (use the value specified in the value property of this task as the fixed target value), or from_file (use the coordinate values from the coordinate data specified by the coordinates_file property).-->
						<value_type>manual_value</value_type>
						<!--This value will be used as the desired (or prescribed) coordinate value if value_type is set to manual_value.-->
						<value>0</value>
					</IKCoordinateTask>
					<IKCoordinateTask name="L5_S1_Flex_Ext">
						<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
						<apply>true</apply>
						<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
						<weight>0.1</weight>
						<!--Indicates the source of the coordinate value for this task.  Possible values are default_value (use default value of coordinate, as specified in the model file, as the fixed target value), manual_value (use the value specified in the value property of this task as the fixed target value), or from_file (use the coordinate values from the coordinate data specified by the coordinates_file property).-->
						<value_type>default_value</value_type>
						<!--This value will be used as the desired (or prescribed) coordinate value if value_type is set to manual_value.-->
						<value>0</value>
					</IKCoordinateTask>
				</objects>
				<groups />
			</IKTaskSet>
			<!--TRC file (.trc) containing the time history of experimental marker positions (usually a static trial).-->
			<marker_file>../S00_Demo_Session/S00_P00_SingleParticipant/S00_P00_T00_StaticTrial/pose-3d/S00_P00_T00_StaticTrial_0-12.trc</marker_file>
			<!--Name of file containing the joint angles used to set the initial configuration of the model for the purpose of placing the markers. These coordinate values can also be included in the optimization problem used to place the markers. Before the model markers are placed, a single frame of an inverse kinematics (IK) problem is solved. The IK problem can be solved simply by matching marker positions, but if the model markers are not in the correct locations, the IK solution will not be very good and neither will marker placement. Alternatively, coordinate values (specified in this file) can be specified and used to influence the IK solution. This is valuable particularly if you have high confidence in the coordinate values. For example, you know for the static trial the subject was standing will all joint angles close to zero. If the coordinate set (see the CoordinateSet property) contains non-zero weights for coordinates, the IK solution will try to match not only the marker positions, but also the coordinates in this file. Least-squared error is used to solve the IK problem. -->
			<coordinate_file>Unassigned</coordinate_file>
			<!--Time range over which the marker positions are averaged.-->
			<time_range> 0.0166667 0.1</time_range>
			<!--Name of the motion file (.mot) written after marker relocation (optional).-->
			<output_motion_file>Unassigned</output_motion_file>
			<!--Output OpenSim model file (.osim) after scaling and maker placement.-->
			<output_model_file>../S00_Demo_Session/S00_P00_SingleParticipant/S00_P00_OpenSim/Model_Pose2Sim_S00_P00_Coco133_scaled.osim</output_model_file>
			<!--Output marker set containing the new marker locations after markers have been placed.-->
			<output_marker_file>Unassigned</output_marker_file>
			<!--Maximum amount of movement allowed in marker data when averaging frames of the static trial. A negative value means there is not limit.-->
			<max_marker_movement>-1</max_marker_movement>
		</MarkerPlacer>
	</ScaleTool>
</OpenSimDocument>
