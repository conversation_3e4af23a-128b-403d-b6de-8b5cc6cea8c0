<?xml version="1.0" encoding="UTF-8" ?>
<OpenSimDocument Version="40000">
	<InverseKinematicsTool>
		<!--Name of the directory where results are written. Be default this is the directory in which the setup file is be  executed.-->
		<results_directory>./</results_directory>
		<!--Name/path to the xml .osim file.-->
		<model_file>../S00_Demo_Session/S00_P00_SingleParticipant/S00_P00_OpenSim/Model_Pose2Sim_S00_P00_Body135_scaled.osim</model_file>
		<!--The relative weighting of kinematic constraint errors. By default this is Infinity, which means constraints are strictly enforced as part of the optimization and are not appended to the objective (cost) function. Any other non-zero positive scalar is the penalty factor for constraint violations.-->
		<constraint_weight>Inf</constraint_weight>
		<!--The accuracy of the solution in absolute terms, i.e. the number of significant digits to which the solution can be trusted. Default 1e-5.-->
		<accuracy>1.0000000000000001e-05</accuracy>
		<!--The time range for the study.-->
		<time_range>0.016666666666666666 1.6666666666666667</time_range>
		<!--Name of the resulting inverse kinematics motion (.mot) file.-->
		<output_motion_file>../S00_Demo_Session/S00_P00_SingleParticipant/S00_P00_OpenSim/S00_P00_T01_BalancingTrial_filt_butterworth_0-100_body135.mot</output_motion_file>
		<!--Flag (true or false) indicating whether or not to report errors from the inverse kinematics solution. Default is true.-->
		<report_errors>true</report_errors>
		<!--Markers and coordinates to be considered (tasks) and their weightings. The sum of weighted-squared task errors composes the cost function.-->
		<IKTaskSet>
			<objects>
				<IKMarkerTask name="Nose">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>0.10000000000000001</weight>
				</IKMarkerTask>
				<IKMarkerTask name="Head">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>0.10000000000000001</weight>
				</IKMarkerTask>
				<IKMarkerTask name="Neck">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>0.20000000000000001</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LShoulder">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>2</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RShoulder">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>2</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LElbow">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RElbow">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LWrist">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RWrist">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LHip">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>2</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RHip">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>2</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LKnee">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RKnee">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LAnkle">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RAnkle">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LBigToe">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LSmallToe">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LHeel">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RBigToe">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RSmallToe">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RHeel">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>1</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RThumb">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>0.20000000000000001</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RIndex">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>0.20000000000000001</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RPinky">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>0.20000000000000001</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LThumb">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>0.20000000000000001</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LIndex">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>0.20000000000000001</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LPinky">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>0.20000000000000001</weight>
				</IKMarkerTask>
				<IKCoordinateTask name="L5_S1_Flex_Ext">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>0.1</weight>
					<!--Indicates the source of the coordinate value for this task.  Possible values are default_value (use default value of coordinate, as specified in the model file, as the fixed target value), manual_value (use the value specified in the value property of this task as the fixed target value), or from_file (use the coordinate values from the coordinate data specified by the coordinates_file property).-->
					<value_type>default_value</value_type>
					<!--This value will be used as the desired (or prescribed) coordinate value if value_type is set to manual_value.-->
					<value>0</value>
				</IKCoordinateTask>
			</objects>
			<groups />
		</IKTaskSet>
		<!--TRC file (.trc) containing the time history of observations of marker positions obtained during a motion capture experiment. Markers in this file that have a corresponding task and model marker are included.-->
		<marker_file>../S00_Demo_Session/S00_P00_SingleParticipant/S00_P00_T01_BalancingTrial/pose-3d/S00_P00_T01_BalancingTrial_0-100_filt_butterworth.trc</marker_file>
		<!--The name of the storage (.sto or .mot) file containing the time history of coordinate observations. Coordinate values from this file are included if there is a corresponding model coordinate and task. -->
		<coordinate_file>Unassigned</coordinate_file>
		<!--Flag indicating whether or not to report model marker locations. Note, model marker locations are expressed in Ground.-->
		<report_marker_locations>false</report_marker_locations>
	</InverseKinematicsTool>
</OpenSimDocument>
