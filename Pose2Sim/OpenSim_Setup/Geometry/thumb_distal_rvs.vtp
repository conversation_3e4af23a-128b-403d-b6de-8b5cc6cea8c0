<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="174" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="344">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.345373 -0.347945  0.871580
			-0.104106 -0.371560  0.922554
			 0.205404 -0.346957  0.915112
			 0.149711 -0.379634  0.912943
			-0.387765 -0.406675  0.827197
			-0.767234 -0.253097  0.589317
			-0.540282 -0.388178  0.746601
			 0.229793 -0.088627  0.969196
			 0.483780 -0.168887  0.858739
			-0.374222 -0.468679  0.800186
			-0.145374 -0.355661  0.923240
			 0.843052  0.041639  0.536218
			-0.220414 -0.551398  0.804598
			 0.344682 -0.538057  0.769213
			 0.626094 -0.265710  0.733079
			-0.631969 -0.474387  0.612840
			-0.924398 -0.205016  0.321647
			-0.945313 -0.141145  0.294042
			-0.824139 -0.176786  0.538091
			-0.956040 -0.042361  0.290159
			-0.394741 -0.688723  0.608145
			-0.739509 -0.548556  0.390143
			 0.286896 -0.315723  0.904439
			 0.453710 -0.102674  0.885215
			 0.768343  0.494704  0.406100
			 0.651686  0.258313  0.713148
			 0.867162  0.406835  0.287255
			-0.101195 -0.589844  0.801152
			 0.929410 -0.081040  0.360040
			 0.822314  0.470663 -0.319806
			 0.910303  0.052106 -0.410650
			 0.992712 -0.094009 -0.075395
			-0.070814 -0.757296  0.649222
			-0.439595 -0.650124  0.619754
			 0.380622 -0.724062  0.575205
			 0.784680 -0.475625  0.397565
			-0.565127 -0.762629  0.314686
			-0.833342 -0.530672  0.154690
			-0.990878 -0.066558  0.117178
			-0.962125 -0.088295 -0.257912
			-0.950592  0.107843 -0.291111
			-0.982208 -0.164646  0.090327
			-0.978511  0.058222  0.197804
			-0.667073  0.583312 -0.463422
			-0.917062  0.394223  0.059879
			 0.135914 -0.981923 -0.131740
			-0.142195 -0.914247 -0.379385
			-0.454483 -0.692797 -0.559891
			-0.738215 -0.326944 -0.590040
			 0.909288 -0.249864  0.332812
			 0.737550 -0.501461  0.452280
			 0.886112  0.052581  0.460479
			 0.677089  0.681661 -0.277287
			 0.490941  0.800007 -0.344915
			 0.775332  0.617123  0.134238
			 0.614516 -0.788035 -0.037016
			 0.426548  0.636535 -0.642558
			 0.496083  0.232420 -0.836590
			 0.925304 -0.167767 -0.340097
			 0.759336 -0.021334 -0.650349
			 0.922880 -0.383470  0.035268
			-0.330840 -0.857420  0.394177
			 0.137169 -0.891841  0.431051
			 0.399584 -0.808557  0.431936
			 0.689875 -0.661198  0.294771
			 0.915603 -0.358138 -0.182779
			-0.606762 -0.593045  0.529279
			-0.705857 -0.707026  0.043347
			-0.632430 -0.767537  0.104493
			-0.415045 -0.817209  0.399885
			-0.909023 -0.333494 -0.249917
			-0.748465 -0.637312 -0.183397
			-0.657537  0.003230 -0.753416
			-0.676648 -0.176314 -0.714885
			-0.260554  0.608619 -0.749463
			-0.561324  0.335212 -0.756669
			-0.836082 -0.003400 -0.548595
			-0.741084  0.413090 -0.529293
			 0.241962  0.603114 -0.760071
			 0.361999  0.662501 -0.655781
			-0.184812  0.804332 -0.564707
			 0.370659 -0.352728 -0.859183
			 0.590207 -0.390909 -0.706291
			 0.133888 -0.234015 -0.962970
			-0.007786  0.059725 -0.998185
			 0.837301  0.164890 -0.521285
			 0.862546 -0.177679 -0.473756
			 0.793190  0.542116 -0.277415
			 0.424178  0.551257 -0.718463
			 0.450178  0.388317 -0.804083
			 0.042082 -0.060969 -0.997252
			 0.546820  0.086326 -0.832788
			 0.287014  0.439320 -0.851247
			-0.067037  0.061887 -0.995829
			 0.837907 -0.080771 -0.539804
			 0.600869  0.207647 -0.771906
			 0.742069  0.067494 -0.666917
			-0.132116 -0.799152  0.586431
			 0.281477 -0.837917  0.467617
			 0.659308 -0.474097  0.583562
			 0.531478 -0.484467  0.694855
			 0.903119 -0.091725  0.419480
			 0.988272  0.059154 -0.140778
			-0.925885  0.347080  0.149240
			-0.592134 -0.311846  0.743054
			-0.744973  0.320618  0.584995
			-0.376690  0.218798  0.900129
			-0.300014 -0.411125  0.860795
			-0.836451 -0.525994 -0.153882
			-0.792910 -0.598507 -0.114376
			-0.801164 -0.598442  0.001853
			-0.867228 -0.450623 -0.211787
			-0.300958  0.200617 -0.932297
			-0.446916 -0.296113 -0.844147
			-0.742328 -0.537230 -0.400416
			-0.076932  0.290637 -0.953735
			 0.168083  0.510216 -0.843462
			 0.470479  0.043846 -0.881321
			-0.249483 -0.338130 -0.907428
			 0.260782  0.109297 -0.959191
			 0.703996  0.292725 -0.647072
			 0.715357  0.048782 -0.697054
			 0.323939  0.359703 -0.875030
			 0.235480  0.775716 -0.585503
			 0.376424  0.294375 -0.878435
			 0.053142 -0.111406  0.992353
			 0.193996 -0.508181  0.839117
			 0.609332  0.385302  0.693006
			 0.698273  0.543621  0.465716
			 0.352829  0.230867  0.906759
			 0.846971  0.505190 -0.165600
			 0.246282  0.959089 -0.139618
			-0.931345  0.363267  0.025176
			-0.004078  0.989529  0.144279
			-0.298141  0.826093  0.478207
			 0.149888  0.738871  0.656965
			-0.704115 -0.707071 -0.065365
			-0.946579 -0.319622 -0.042783
			-0.941598 -0.324737 -0.089098
			-0.683027  0.725776  0.081994
			-0.558567 -0.620333 -0.550627
			-0.547411 -0.388409 -0.741269
			-0.005992  0.104325 -0.994525
			-0.303577 -0.122780 -0.944863
			 0.607374  0.747325 -0.269449
			 0.251372  0.966742  0.047147
			-0.119427  0.552700 -0.824779
			-0.456678  0.768727 -0.447776
			-0.255868  0.898107  0.357681
			-0.073366  0.976085  0.204634
			 0.146313  0.881693  0.448564
			-0.015603  0.853182  0.521380
			 0.517292  0.812416  0.269052
			 0.153498  0.981789  0.111931
			 0.018592  0.994888  0.099253
			 0.074294  0.987240  0.140846
			-0.194486  0.961671  0.193298
			-0.731659 -0.592109 -0.337760
			-0.872781 -0.072789 -0.482655
			-0.762290  0.380158 -0.523826
			-0.382182  0.638645  0.667884
			-0.259776  0.851221  0.456004
			-0.560436  0.172102 -0.810119
			-0.328219  0.264526 -0.906807
			 0.043205  0.913841  0.403766
			-0.235437  0.797364  0.555679
			-0.327738  0.735043  0.593548
			-0.282473  0.563348  0.776433
			-0.101001  0.948527  0.300157
			-0.070070  0.975870  0.206805
			-0.258258  0.957742  0.126626
			 0.098718  0.909600  0.403586
			-0.144753  0.939084  0.311717
			-0.172020  0.838828  0.516505
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.025451 -0.101773  0.050942
			 0.026316 -0.106382  0.049673
			 0.027920 -0.101946  0.051043
			 0.027705 -0.101070  0.051429
			 0.024755 -0.099559  0.051584
			 0.023682 -0.101505  0.049606
			 0.024799 -0.108247  0.047788
			 0.029192 -0.103025  0.050270
			 0.029192 -0.102332  0.050326
			 0.026014 -0.109576  0.047623
			 0.027045 -0.108637  0.048432
			 0.028633 -0.101229  0.050980
			 0.025620 -0.097628  0.052933
			 0.027938 -0.097404  0.053164
			 0.028121 -0.100314  0.051427
			 0.024792 -0.097531  0.052647
			 0.022824 -0.097632  0.049331
			 0.022943 -0.099586  0.048996
			 0.023510 -0.104471  0.048229
			 0.022933 -0.101953  0.047359
			 0.025824 -0.110735  0.046712
			 0.023785 -0.109766  0.046153
			 0.029206 -0.106076  0.049564
			 0.029672 -0.103325  0.050122
			 0.029645 -0.102324  0.049898
			 0.029920 -0.102912  0.049955
			 0.029040 -0.101621  0.050531
			 0.027517 -0.110166  0.048081
			 0.028464 -0.100255  0.051052
			 0.028586 -0.100972  0.049943
			 0.028288 -0.099939  0.049806
			 0.028477 -0.100015  0.050491
			 0.026743 -0.095520  0.055213
			 0.024598 -0.095133  0.054405
			 0.027382 -0.095423  0.055107
			 0.029064 -0.096641  0.052735
			 0.021338 -0.093441  0.052917
			 0.022159 -0.094927  0.051124
			 0.022830 -0.098707  0.048899
			 0.022794 -0.097659  0.048761
			 0.022846 -0.099534  0.048442
			 0.022394 -0.105426  0.045982
			 0.022481 -0.102246  0.046406
			 0.022894 -0.100903  0.046984
			 0.022726 -0.101354  0.046614
			 0.026933 -0.111572  0.045811
			 0.025959 -0.111438  0.045320
			 0.024366 -0.110644  0.044611
			 0.023970 -0.110009  0.044498
			 0.030022 -0.107532  0.048229
			 0.029454 -0.109990  0.047418
			 0.030356 -0.103416  0.049561
			 0.028979 -0.101732  0.049817
			 0.029567 -0.102375  0.048955
			 0.029975 -0.102588  0.049430
			 0.028526 -0.111218  0.046709
			 0.028632 -0.102113  0.048872
			 0.026543 -0.101888  0.047540
			 0.029013 -0.096854  0.050868
			 0.028791 -0.095703  0.050528
			 0.029304 -0.096396  0.052088
			 0.025022 -0.094366  0.056073
			 0.027526 -0.094861  0.056065
			 0.028638 -0.094163  0.056062
			 0.030628 -0.093972  0.054888
			 0.030724 -0.093992  0.053056
			 0.021318 -0.092303  0.056279
			 0.019641 -0.092606  0.052612
			 0.021053 -0.093473  0.051935
			 0.024287 -0.093765  0.057219
			 0.022145 -0.094789  0.049621
			 0.020501 -0.092987  0.051417
			 0.023219 -0.098473  0.048258
			 0.023415 -0.097091  0.047721
			 0.023276 -0.099818  0.047412
			 0.023314 -0.098985  0.048139
			 0.022668 -0.105213  0.044837
			 0.022512 -0.101761  0.045896
			 0.024000 -0.100504  0.046970
			 0.023812 -0.101777  0.045909
			 0.023056 -0.101288  0.046010
			 0.026710 -0.110792  0.045208
			 0.028394 -0.110991  0.046259
			 0.024845 -0.109526  0.044239
			 0.024002 -0.107615  0.044022
			 0.030444 -0.104302  0.048393
			 0.029740 -0.108724  0.046972
			 0.030198 -0.102846  0.048937
			 0.029744 -0.102903  0.048458
			 0.026779 -0.104608  0.046292
			 0.024230 -0.094074  0.047579
			 0.026286 -0.094085  0.048465
			 0.024737 -0.100811  0.047252
			 0.024932 -0.099497  0.047566
			 0.030717 -0.093136  0.052567
			 0.029464 -0.091726  0.051393
			 0.030189 -0.092938  0.051808
			 0.026167 -0.094267  0.057636
			 0.027114 -0.094424  0.057383
			 0.030301 -0.093507  0.056230
			 0.029624 -0.093124  0.057162
			 0.031188 -0.092923  0.055571
			 0.031316 -0.092176  0.053689
			 0.018875 -0.090734  0.053415
			 0.020961 -0.091237  0.056647
			 0.019915 -0.090701  0.055809
			 0.022187 -0.090894  0.057510
			 0.024952 -0.093186  0.058348
			 0.019533 -0.092056  0.051870
			 0.021656 -0.093522  0.049579
			 0.019770 -0.091929  0.050222
			 0.019053 -0.091139  0.051318
			 0.023843 -0.099092  0.047850
			 0.022460 -0.093861  0.047784
			 0.021965 -0.093831  0.048287
			 0.023363 -0.103714  0.044718
			 0.023398 -0.101964  0.045502
			 0.026995 -0.109055  0.045002
			 0.022139 -0.093072  0.047617
			 0.023665 -0.091626  0.047176
			 0.030376 -0.091301  0.052097
			 0.030222 -0.092198  0.052041
			 0.023567 -0.090186  0.047641
			 0.023355 -0.089300  0.048267
			 0.023498 -0.090917  0.047514
			 0.026181 -0.092517  0.058502
			 0.027494 -0.093705  0.058125
			 0.030378 -0.092709  0.056512
			 0.030762 -0.091799  0.055310
			 0.027649 -0.092853  0.058285
			 0.030550 -0.090913  0.052838
			 0.022529 -0.091355  0.056907
			 0.018776 -0.090817  0.052340
			 0.019440 -0.090868  0.053587
			 0.020911 -0.090743  0.056727
			 0.024810 -0.091846  0.058402
			 0.019781 -0.092236  0.049259
			 0.019391 -0.091595  0.049131
			 0.019414 -0.091213  0.050253
			 0.019381 -0.090515  0.049408
			 0.021571 -0.093113  0.048024
			 0.020201 -0.091942  0.047845
			 0.022692 -0.091499  0.047169
			 0.020923 -0.091707  0.047475
			 0.030102 -0.090363  0.052892
			 0.027926 -0.089509  0.051657
			 0.021888 -0.089825  0.047701
			 0.020296 -0.089766  0.048597
			 0.022704 -0.089217  0.048949
			 0.025057 -0.088958  0.050196
			 0.027561 -0.092240  0.058043
			 0.029427 -0.091397  0.054714
			 0.030390 -0.090536  0.053651
			 0.019995 -0.090737  0.052975
			 0.023671 -0.091559  0.053237
			 0.024236 -0.091572  0.053978
			 0.019291 -0.090567  0.052759
			 0.019826 -0.092119  0.048468
			 0.019548 -0.091405  0.048352
			 0.019653 -0.090652  0.048506
			 0.024626 -0.090577  0.050771
			 0.024694 -0.090976  0.051511
			 0.020045 -0.091214  0.047877
			 0.020868 -0.090929  0.047514
			 0.029722 -0.090139  0.053128
			 0.027255 -0.090847  0.052434
			 0.026362 -0.089166  0.051050
			 0.026067 -0.090046  0.051576
			 0.025941 -0.091542  0.053593
			 0.024773 -0.091315  0.053203
			 0.024508 -0.091452  0.052767
			 0.025249 -0.091493  0.053228
			 0.025179 -0.091088  0.052529
			 0.025993 -0.090982  0.052231
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 3 4 
					5 0 4 
					2 3 0 
					0 6 1 
					5 6 0 
					7 8 1 
					1 9 10 
					8 2 1 
					10 7 1 
					1 6 9 
					8 11 2 
					3 2 11 
					12 3 13 
					12 4 3 
					3 11 14 
					13 3 14 
					15 4 12 
					4 15 5 
					16 5 15 
					16 17 5 
					18 5 19 
					17 19 5 
					5 18 6 
					9 6 20 
					21 20 6 
					18 21 6 
					7 10 22 
					23 7 22 
					7 24 8 
					23 25 7 
					25 24 7 
					26 8 24 
					26 11 8 
					9 20 27 
					27 10 9 
					27 22 10 
					14 11 28 
					29 11 26 
					30 11 29 
					31 28 11 
					31 11 30 
					32 12 13 
					33 15 12 
					33 12 32 
					13 34 32 
					35 34 13 
					35 13 14 
					28 35 14 
					15 36 37 
					33 36 15 
					16 15 37 
					38 16 39 
					17 16 38 
					39 16 37 
					19 17 40 
					40 17 38 
					41 18 42 
					18 41 21 
					19 42 18 
					43 19 40 
					44 42 19 
					44 19 43 
					45 27 20 
					21 46 20 
					45 20 46 
					46 21 47 
					48 47 21 
					41 48 21 
					49 22 50 
					51 22 49 
					23 22 51 
					50 22 27 
					23 51 25 
					26 24 52 
					52 24 53 
					54 53 24 
					25 54 24 
					51 54 25 
					52 29 26 
					27 45 55 
					50 27 55 
					35 28 31 
					52 56 29 
					57 29 56 
					29 57 30 
					58 30 59 
					59 30 57 
					30 58 31 
					35 31 60 
					60 31 58 
					32 61 33 
					62 32 34 
					32 62 61 
					61 36 33 
					62 34 63 
					63 34 64 
					34 35 64 
					64 35 65 
					35 60 65 
					36 66 67 
					68 37 36 
					67 68 36 
					69 66 36 
					69 36 61 
					70 39 37 
					71 37 68 
					70 37 71 
					39 40 38 
					40 39 72 
					39 70 73 
					39 73 72 
					74 43 40 
					40 72 75 
					40 75 74 
					76 48 41 
					42 76 41 
					42 44 77 
					42 77 76 
					43 74 78 
					43 78 79 
					80 44 43 
					80 43 79 
					77 44 80 
					81 82 45 
					82 55 45 
					45 46 81 
					83 81 46 
					83 46 47 
					47 48 83 
					76 84 48 
					83 48 84 
					51 49 85 
					49 86 85 
					86 49 50 
					86 50 55 
					87 51 85 
					51 87 54 
					56 52 53 
					54 87 53 
					87 88 53 
					88 56 53 
					86 55 82 
					88 89 56 
					89 57 56 
					57 90 91 
					91 59 57 
					92 57 89 
					93 90 57 
					93 57 92 
					65 60 58 
					65 58 59 
					65 59 94 
					91 95 59 
					96 59 95 
					59 96 94 
					62 97 61 
					97 69 61 
					62 63 98 
					62 98 97 
					99 100 63 
					98 63 100 
					64 99 63 
					99 64 101 
					65 102 64 
					101 64 102 
					102 65 94 
					103 67 66 
					104 105 66 
					66 105 103 
					66 106 104 
					106 66 107 
					107 66 69 
					71 68 67 
					67 108 71 
					108 67 103 
					107 69 97 
					70 71 109 
					70 109 73 
					110 71 111 
					108 111 71 
					109 71 110 
					75 72 112 
					73 112 72 
					113 90 73 
					113 73 114 
					109 114 73 
					90 93 73 
					112 73 93 
					75 112 74 
					92 74 112 
					74 92 78 
					76 115 84 
					77 115 76 
					77 116 115 
					116 77 80 
					92 89 78 
					78 89 79 
					89 116 79 
					116 80 79 
					81 117 82 
					83 117 81 
					82 117 86 
					83 84 117 
					89 84 115 
					89 117 84 
					88 85 89 
					85 86 117 
					117 89 85 
					87 85 88 
					115 116 89 
					118 90 113 
					90 118 119 
					119 91 90 
					95 91 119 
					112 93 92 
					102 94 120 
					121 120 94 
					96 121 94 
					121 95 120 
					122 123 95 
					124 122 95 
					124 95 119 
					95 123 120 
					96 95 121 
					125 97 126 
					107 97 125 
					126 97 98 
					126 98 100 
					100 99 127 
					99 101 128 
					99 128 127 
					127 129 100 
					100 129 126 
					101 102 128 
					130 128 102 
					102 120 130 
					131 103 105 
					108 103 132 
					133 103 131 
					132 103 133 
					106 134 104 
					134 105 104 
					131 105 134 
					106 107 135 
					131 134 106 
					135 131 106 
					135 107 125 
					111 108 132 
					136 109 110 
					109 136 114 
					110 137 136 
					111 138 110 
					138 137 110 
					132 139 111 
					138 111 139 
					118 113 140 
					113 114 140 
					140 114 136 
					140 141 118 
					142 119 118 
					142 118 143 
					118 141 143 
					119 142 124 
					144 130 120 
					144 120 145 
					123 145 120 
					146 122 142 
					142 122 124 
					122 146 123 
					147 123 146 
					148 123 147 
					148 149 123 
					145 123 149 
					125 126 129 
					135 125 129 
					127 150 129 
					127 128 151 
					127 151 150 
					152 151 128 
					152 128 130 
					150 135 129 
					144 152 130 
					153 133 131 
					153 131 154 
					131 135 155 
					155 154 131 
					133 156 132 
					139 132 156 
					133 153 156 
					135 150 155 
					136 137 157 
					140 136 157 
					138 139 137 
					137 139 158 
					137 158 157 
					147 159 139 
					159 158 139 
					156 153 139 
					139 148 147 
					139 160 148 
					139 153 154 
					161 139 154 
					160 139 161 
					141 140 157 
					157 158 141 
					162 141 158 
					162 143 141 
					163 142 143 
					146 142 163 
					163 143 162 
					152 144 164 
					164 144 145 
					165 164 145 
					166 145 149 
					145 166 167 
					167 165 145 
					147 146 163 
					147 162 159 
					147 163 162 
					149 148 160 
					166 149 160 
					165 168 150 
					150 151 165 
					168 155 150 
					164 151 152 
					151 164 165 
					154 155 169 
					170 154 169 
					161 154 170 
					168 171 155 
					171 169 155 
					162 158 159 
					166 160 161 
					161 170 172 
					167 166 161 
					173 167 161 
					173 161 172 
					173 168 165 
					165 167 173 
					171 168 172 
					168 173 172 
					169 171 172 
					170 169 172 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
