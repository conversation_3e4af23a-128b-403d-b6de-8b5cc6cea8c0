<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="116" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="227">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.461884 -0.015337 -0.886808
			-0.698867 -0.327655 -0.635788
			-0.809324 -0.238921 -0.536574
			-0.374093  0.154231 -0.914477
			 0.033931 -0.073760 -0.996699
			-0.050730  0.391337 -0.918848
			-0.899010 -0.435865 -0.042466
			-0.467488 -0.605486 -0.644082
			-0.738696 -0.663004 -0.121468
			-0.864227 -0.501899 -0.034773
			-0.724232 -0.349427 -0.594465
			-0.365063 -0.211176 -0.906716
			 0.465735  0.138331 -0.874046
			 0.283087  0.751198 -0.596291
			-0.178485  0.786012 -0.591885
			-0.074880 -0.722193 -0.687627
			 0.339415 -0.543070 -0.768032
			 0.457300 -0.254077 -0.852245
			 0.616329  0.577756 -0.535104
			 0.886566  0.107918 -0.449838
			 0.287327  0.919225 -0.269201
			-0.796318 -0.424832  0.430576
			-0.878982 -0.402115  0.256309
			-0.699661 -0.713908  0.028455
			-0.199955 -0.977167 -0.071850
			-0.828252 -0.445657 -0.339690
			-0.775586 -0.439179  0.453417
			-0.356457 -0.269592 -0.894572
			 0.308706 -0.039329 -0.950344
			 0.727316  0.159633 -0.667480
			 0.886531  0.318217 -0.335858
			 0.642465  0.764120 -0.057959
			 0.405911 -0.884579 -0.229686
			 0.866090 -0.321676 -0.382638
			 0.782855 -0.595082 -0.181704
			 0.789772  0.606025  0.094836
			 0.990145  0.105926  0.091613
			 0.519192  0.837593  0.169938
			-0.344243 -0.319898  0.882702
			-0.458094 -0.223174  0.860432
			-0.242857  0.130782  0.961206
			-0.720743 -0.583594  0.374097
			-0.282722 -0.958626 -0.033235
			 0.013554 -0.998537 -0.052356
			 0.566154 -0.817444  0.106087
			-0.829723 -0.446243  0.335300
			-0.272761 -0.421929 -0.864626
			-0.810588 -0.492264 -0.317212
			 0.404519 -0.245864 -0.880861
			 0.897079  0.086847 -0.433252
			 0.928000  0.236482  0.287911
			 0.907497  0.407268  0.102873
			 0.672459  0.739019  0.040630
			 0.917507 -0.385899  0.096247
			 0.826969  0.552859  0.102322
			 0.997892 -0.064886  0.001514
			-0.763438 -0.244640  0.597758
			 0.418679  0.007175  0.908106
			 0.128894 -0.220455  0.966843
			-0.048145 -0.207867  0.976972
			 0.577833  0.213737  0.787671
			-0.118271  0.182313  0.976101
			-0.334231 -0.041381  0.941582
			-0.377509 -0.426025  0.822186
			 0.108780 -0.410594  0.905306
			 0.065916  0.116082  0.991050
			 0.385114 -0.006097  0.922849
			 0.284855  0.641358  0.712402
			 0.176598  0.763107  0.621676
			-0.246351 -0.795010  0.554320
			 0.455967 -0.880519  0.129539
			 0.496733 -0.867672  0.020045
			 0.807664 -0.589154 -0.024028
			-0.960285 -0.242448 -0.138101
			-0.938664 -0.269615  0.214982
			-0.753896 -0.342605 -0.560591
			-0.142547 -0.286500 -0.947416
			 0.412733 -0.104319 -0.904859
			 0.784656 -0.069990 -0.615968
			 0.839235  0.510649 -0.186875
			 0.857717  0.182209  0.480751
			 0.565750 -0.018777  0.824363
			 0.708998  0.630460  0.315979
			 0.672873  0.736847  0.065564
			 0.431930  0.882161  0.187694
			 0.962816  0.269655  0.016451
			 0.916696 -0.394223 -0.065241
			-0.948730  0.316041  0.005518
			-0.564674  0.062222  0.822965
			-0.920690  0.207131  0.330796
			-0.113812  0.313174  0.942851
			 0.342373  0.134004  0.929959
			 0.573618  0.535772  0.619606
			 0.755906 -0.417295  0.504451
			 0.891724  0.051796  0.449605
			-0.892508  0.444774 -0.074863
			-0.833085  0.272960 -0.481105
			-0.640029 -0.062900 -0.765772
			 0.287970  0.150185 -0.945789
			-0.162086  0.107846 -0.980866
			 0.673131  0.148113 -0.724539
			 0.454644  0.848837  0.269769
			 0.049496  0.998580 -0.019717
			 0.550746  0.624881  0.553356
			 0.527582  0.471548 -0.706611
			 0.138175  0.852639 -0.503900
			-0.636469  0.716055  0.286656
			-0.497801  0.866154 -0.044394
			-0.242081  0.885045  0.397609
			 0.222938  0.534525  0.815219
			-0.667944  0.742843 -0.045101
			-0.423571  0.880031 -0.214788
			-0.319104  0.497470 -0.806657
			 0.211148  0.457890 -0.863570
			 0.177343  0.865103  0.469197
			-0.172201  0.983740  0.051019
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.019025 -0.135168 -0.024391
			 0.018744 -0.138557 -0.023689
			 0.017906 -0.136291 -0.023189
			 0.018914 -0.134444 -0.024175
			 0.024450 -0.136713 -0.025397
			 0.021551 -0.134310 -0.025027
			 0.017361 -0.136156 -0.021333
			 0.020801 -0.141051 -0.023786
			 0.019680 -0.140809 -0.022046
			 0.012101 -0.128174 -0.019542
			 0.015407 -0.132276 -0.022157
			 0.014782 -0.126729 -0.024178
			 0.016705 -0.126226 -0.024034
			 0.020694 -0.133519 -0.023500
			 0.020570 -0.133892 -0.024617
			 0.021621 -0.141356 -0.023897
			 0.024092 -0.140310 -0.024467
			 0.024655 -0.138599 -0.024992
			 0.024897 -0.135115 -0.024564
			 0.025831 -0.137304 -0.024291
			 0.022622 -0.133608 -0.024002
			 0.017876 -0.136064 -0.017009
			 0.018679 -0.138737 -0.017635
			 0.019617 -0.140549 -0.019606
			 0.021316 -0.141764 -0.022257
			 0.009299 -0.123015 -0.020387
			 0.011666 -0.126005 -0.017303
			 0.012389 -0.121230 -0.024483
			 0.012139 -0.115072 -0.025330
			 0.014007 -0.117588 -0.024223
			 0.015408 -0.120538 -0.023160
			 0.021326 -0.133245 -0.021680
			 0.024027 -0.141171 -0.023137
			 0.025754 -0.138301 -0.024098
			 0.024862 -0.140272 -0.023528
			 0.025150 -0.135529 -0.021985
			 0.026020 -0.137595 -0.021987
			 0.023183 -0.133915 -0.022029
			 0.007657 -0.113507 -0.014293
			 0.020290 -0.138961 -0.015213
			 0.018721 -0.134695 -0.015706
			 0.020429 -0.141016 -0.016084
			 0.021733 -0.142173 -0.017164
			 0.021425 -0.141576 -0.019555
			 0.023845 -0.141127 -0.020449
			 0.004746 -0.112919 -0.016314
			 0.009171 -0.112493 -0.026386
			 0.003927 -0.111470 -0.022659
			 0.010243 -0.112381 -0.026277
			 0.012527 -0.109837 -0.024651
			 0.012037 -0.112294 -0.016001
			 0.016675 -0.123003 -0.020196
			 0.021250 -0.133472 -0.019610
			 0.025852 -0.138443 -0.021767
			 0.025102 -0.136787 -0.019481
			 0.025384 -0.137737 -0.019498
			 0.002907 -0.106327 -0.013814
			 0.010926 -0.113198 -0.013722
			 0.009280 -0.110776 -0.013130
			 0.007680 -0.108747 -0.012306
			 0.017625 -0.128646 -0.015640
			 0.022187 -0.136196 -0.014120
			 0.021436 -0.136457 -0.014316
			 0.021608 -0.140980 -0.015012
			 0.024476 -0.139832 -0.014173
			 0.024170 -0.137361 -0.013868
			 0.024632 -0.138139 -0.013874
			 0.020313 -0.133909 -0.015894
			 0.022073 -0.134576 -0.014889
			 0.021762 -0.142009 -0.015819
			 0.023740 -0.141799 -0.015567
			 0.023708 -0.141013 -0.019659
			 0.024450 -0.140250 -0.019627
			 0.002022 -0.107642 -0.021912
			 0.002435 -0.107910 -0.015800
			 0.003498 -0.108867 -0.025202
			 0.007508 -0.107590 -0.028663
			 0.009959 -0.107132 -0.028303
			 0.010824 -0.107269 -0.027563
			 0.011648 -0.101844 -0.024413
			 0.010763 -0.104564 -0.013566
			 0.010463 -0.111578 -0.013600
			 0.021191 -0.133433 -0.017330
			 0.024489 -0.135425 -0.016836
			 0.022644 -0.134213 -0.017013
			 0.025895 -0.137692 -0.017096
			 0.025561 -0.139874 -0.017278
			 0.001732 -0.105580 -0.018894
			 0.004139 -0.103953 -0.012308
			 0.002306 -0.104116 -0.014897
			 0.004899 -0.103350 -0.012219
			 0.009008 -0.105208 -0.012403
			 0.024177 -0.135908 -0.014576
			 0.025054 -0.140213 -0.014895
			 0.025471 -0.138091 -0.014563
			 0.001773 -0.105514 -0.021364
			 0.003939 -0.103609 -0.026719
			 0.004339 -0.105198 -0.027308
			 0.008160 -0.105052 -0.028649
			 0.006752 -0.105267 -0.028873
			 0.010584 -0.105984 -0.027829
			 0.008453 -0.101388 -0.014870
			 0.010138 -0.100530 -0.023351
			 0.009758 -0.102599 -0.013867
			 0.010419 -0.102395 -0.026727
			 0.007969 -0.101064 -0.026443
			 0.003591 -0.102520 -0.013459
			 0.007107 -0.102246 -0.018880
			 0.005202 -0.101789 -0.013285
			 0.007971 -0.102561 -0.012860
			 0.004184 -0.102453 -0.025132
			 0.005844 -0.101538 -0.026462
			 0.006074 -0.102570 -0.027958
			 0.008600 -0.102662 -0.027847
			 0.007163 -0.101416 -0.013884
			 0.007688 -0.100981 -0.015700
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					4 1 0 
					4 0 5 
					3 5 0 
					6 2 1 
					7 1 4 
					1 7 8 
					8 6 1 
					2 6 9 
					3 2 10 
					9 10 2 
					10 11 3 
					12 3 11 
					3 12 13 
					3 13 14 
					3 14 5 
					4 15 7 
					4 16 15 
					4 17 16 
					18 4 5 
					18 19 4 
					17 4 19 
					5 14 20 
					5 20 18 
					21 9 6 
					22 21 6 
					8 23 6 
					6 23 22 
					24 7 15 
					24 8 7 
					23 8 24 
					25 10 9 
					26 25 9 
					9 21 26 
					11 10 25 
					11 25 27 
					27 28 11 
					11 28 12 
					28 29 12 
					12 30 13 
					12 29 30 
					20 14 13 
					30 31 13 
					31 20 13 
					24 15 32 
					15 16 32 
					17 33 16 
					34 32 16 
					34 16 33 
					19 33 17 
					35 19 18 
					35 18 20 
					19 35 36 
					36 33 19 
					37 35 20 
					20 31 37 
					26 21 38 
					22 39 21 
					38 21 40 
					39 22 41 
					22 23 41 
					41 23 42 
					43 23 24 
					42 23 43 
					32 44 24 
					24 44 43 
					26 45 25 
					46 27 25 
					47 46 25 
					47 25 45 
					38 45 26 
					28 27 46 
					46 48 28 
					28 48 49 
					49 29 28 
					29 49 30 
					30 49 50 
					30 50 51 
					51 31 30 
					52 31 51 
					52 37 31 
					44 32 34 
					53 34 33 
					33 36 53 
					44 34 53 
					37 54 35 
					36 35 54 
					55 36 54 
					55 53 36 
					54 37 52 
					56 45 38 
					57 58 38 
					59 56 38 
					38 58 59 
					40 60 38 
					38 60 57 
					61 62 39 
					39 63 64 
					41 63 39 
					61 39 65 
					65 39 66 
					39 64 66 
					62 40 39 
					40 67 60 
					67 40 68 
					40 62 61 
					40 61 68 
					42 69 41 
					41 69 63 
					42 70 69 
					42 43 70 
					70 43 71 
					43 44 71 
					53 72 44 
					72 71 44 
					47 45 73 
					73 45 74 
					56 74 45 
					47 75 46 
					46 76 48 
					75 76 46 
					47 73 75 
					77 48 76 
					49 48 78 
					78 48 77 
					78 79 49 
					49 80 50 
					49 79 80 
					50 57 60 
					81 57 50 
					60 51 50 
					81 50 80 
					60 82 51 
					52 51 82 
					83 54 52 
					83 52 84 
					52 82 84 
					53 55 72 
					85 54 83 
					54 85 55 
					86 72 55 
					85 86 55 
					87 74 56 
					59 88 56 
					56 89 87 
					89 56 88 
					58 57 81 
					59 58 81 
					88 59 90 
					59 81 91 
					59 91 90 
					82 60 67 
					92 61 65 
					61 92 68 
					69 64 63 
					69 70 64 
					64 93 66 
					64 70 93 
					65 66 92 
					93 94 66 
					66 94 92 
					67 84 82 
					84 67 68 
					83 68 92 
					83 84 68 
					72 70 71 
					72 86 70 
					70 86 93 
					73 74 87 
					73 87 95 
					95 96 73 
					75 73 96 
					97 75 96 
					97 76 75 
					77 76 98 
					76 97 99 
					98 76 99 
					77 100 78 
					100 77 98 
					79 78 100 
					101 79 102 
					79 101 103 
					80 79 103 
					79 100 104 
					105 79 104 
					79 105 102 
					81 80 91 
					103 91 80 
					85 83 92 
					85 92 94 
					85 94 86 
					86 94 93 
					87 89 106 
					87 107 95 
					107 87 106 
					89 88 106 
					90 106 88 
					108 106 90 
					109 90 91 
					109 108 90 
					91 103 109 
					107 110 95 
					96 95 110 
					110 111 96 
					96 111 112 
					97 96 112 
					112 99 97 
					99 113 98 
					98 104 100 
					104 98 113 
					99 112 113 
					101 114 103 
					115 101 102 
					115 114 101 
					111 107 102 
					115 102 107 
					102 105 111 
					114 109 103 
					105 104 113 
					112 111 105 
					112 105 113 
					107 106 108 
					111 110 107 
					107 108 115 
					108 109 114 
					114 115 108 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
