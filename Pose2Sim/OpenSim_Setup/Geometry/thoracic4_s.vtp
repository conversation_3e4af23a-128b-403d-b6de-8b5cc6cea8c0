<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.553381 -0.832928  0.000328
			 0.337855 -0.922946  0.184460
			-0.178667 -0.983910  0.000015
			 0.936690 -0.216670  0.275073
			 0.937186 -0.348829  0.000212
			 0.337953 -0.922956 -0.184227
			 0.936783 -0.216615 -0.274799
			 0.371718 -0.795595  0.478387
			 0.118166 -0.663813  0.738505
			-0.517849 -0.855329 -0.015667
			-0.620821 -0.783952 -0.000014
			-0.556182 -0.814513  0.165016
			-0.293665 -0.634920  0.714589
			 0.371713 -0.795599 -0.478384
			 0.118162 -0.663810 -0.738508
			-0.517846 -0.855331  0.015641
			-0.556175 -0.814519 -0.165012
			-0.293659 -0.634928 -0.714584
			 0.997784  0.066530  0.000161
			 0.940687  0.106537  0.322114
			 0.694444  0.045611  0.718099
			 0.940783  0.106565 -0.321825
			 0.694442  0.045614 -0.718101
			 0.271258  0.116237  0.955462
			-0.938671 -0.343034 -0.034992
			-0.998269 -0.058812 -0.000018
			-0.938671 -0.343035  0.034987
			-0.263842 -0.000764  0.964566
			-0.799454 -0.300658  0.520075
			-0.962638 -0.189208 -0.193724
			 0.271254  0.116234 -0.955464
			-0.799456 -0.300653 -0.520074
			-0.263840 -0.000767 -0.964566
			-0.962639 -0.189207  0.193719
			 0.972298 -0.233746  0.000344
			 0.880654 -0.303881  0.363463
			 0.662350  0.109850  0.741098
			 0.880757 -0.303897 -0.363200
			 0.662349  0.109852 -0.741098
			 0.256067  0.123208  0.958775
			-0.922920  0.280668 -0.263523
			-0.932652  0.360778 -0.000073
			-0.922932  0.280664  0.263486
			-0.146109  0.049790  0.988015
			-0.195455 -0.509517  0.837968
			-0.828542 -0.346676 -0.439697
			 0.256066  0.123216 -0.958774
			-0.195452 -0.509518 -0.837968
			-0.146110  0.049798 -0.988014
			-0.828545 -0.346676  0.439691
			 0.950208 -0.311617  0.000201
			 0.881749 -0.352005  0.314025
			 0.507132 -0.362345  0.782000
			 0.881800 -0.352011 -0.313875
			 0.507133 -0.362341 -0.782001
			 0.136827 -0.339340  0.930659
			-0.914339  0.312309 -0.257773
			-0.377615  0.245324 -0.892873
			-0.963348  0.268256 -0.000147
			-0.914372  0.312294  0.257677
			-0.377622  0.245317  0.892872
			-0.080663 -0.291570  0.953143
			 0.227785  0.038748  0.972940
			-0.337018 -0.925544  0.172588
			-0.418645 -0.523126  0.742345
			 0.081640 -0.074196  0.993896
			 0.681524 -0.061407 -0.729215
			 0.136830 -0.339337 -0.930660
			 0.227773  0.038738 -0.972943
			-0.080666 -0.291574 -0.953141
			-0.418636 -0.523127 -0.742349
			-0.337021 -0.925544 -0.172585
			 0.081651 -0.074195 -0.993896
			 0.681701 -0.061386  0.729051
			 0.953667  0.300865  0.000169
			 0.852746  0.414392  0.317967
			 0.539614 -0.361251  0.760470
			 0.852788  0.414404 -0.317840
			 0.539610 -0.361258 -0.760470
			 0.087638 -0.368083  0.925654
			-0.314033  0.881995 -0.351380
			-0.298763  0.059847 -0.952449
			-0.529356  0.848400 -0.000227
			-0.314072  0.882036  0.351243
			-0.298765  0.059836  0.952449
			-0.197628 -0.114062  0.973619
			-0.233371  0.335288  0.912754
			 0.172986  0.692187  0.700681
			-0.311036 -0.497788  0.809607
			-0.449702 -0.479730  0.753411
			 0.456085 -0.290111  0.841321
			 0.740014 -0.496847  0.453345
			 0.207007  0.200958  0.957478
			-0.610831 -0.406253  0.679591
			 0.960738 -0.277457 -0.000209
			 0.602967 -0.654391 -0.456294
			-0.303489  0.952835  0.000054
			-0.316369  0.591687 -0.741497
			 0.087642 -0.368090 -0.925651
			-0.233370  0.335277 -0.912758
			-0.197626 -0.114067 -0.973618
			 0.172987  0.692183 -0.700684
			-0.449703 -0.479733 -0.753408
			-0.311038 -0.497789 -0.809606
			 0.456089 -0.290107 -0.841321
			 0.740014 -0.496848 -0.453344
			-0.610816 -0.406257 -0.679603
			 0.207004  0.200955 -0.957479
			 0.602946 -0.654443  0.456246
			-0.316281  0.591706  0.741519
			 0.263498  0.964660 -0.000003
			 0.644335  0.572287  0.507268
			 0.388889  0.420399  0.819774
			 0.644333  0.572289 -0.507268
			 0.388890  0.420390 -0.819778
			-0.133299  0.463209  0.876167
			 0.056272  0.992599 -0.107618
			 0.211611  0.911539  0.352585
			-0.489382  0.694865 -0.526943
			 0.005306  0.929877  0.367832
			 0.211613  0.911540 -0.352583
			 0.056277  0.992598  0.107617
			-0.489384  0.694866  0.526939
			 0.005322  0.929880 -0.367826
			 0.462561  0.508229  0.726458
			 0.501917  0.529323  0.684030
			 0.563650  0.209564  0.798988
			 0.104164 -0.720098  0.686010
			 0.590523  0.094474  0.801472
			-0.744581  0.448797  0.494146
			-0.081547  0.157268  0.984183
			-0.868182 -0.410123 -0.279389
			-0.837714  0.458961 -0.295957
			 0.837744 -0.448220  0.311903
			-0.014807  0.247274  0.968832
			 0.112655 -0.028785  0.993217
			-0.928577 -0.247667  0.276417
			-0.910421  0.362099 -0.200047
			-0.194406 -0.980921 -0.000120
			-0.282747 -0.877185 -0.388074
			-0.739077  0.673620  0.000191
			-0.374231  0.861031  0.344351
			-0.630934  0.758276  0.164136
			-0.815387  0.538883 -0.211540
			-0.374057  0.861177 -0.344172
			-0.630802  0.758422 -0.163965
			-0.815373  0.538875  0.211616
			-0.721828  0.465078 -0.512511
			-0.133288  0.463214 -0.876166
			 0.462559  0.508229 -0.726459
			 0.501918  0.529323 -0.684029
			-0.081547  0.157268 -0.984183
			-0.744578  0.448797 -0.494150
			 0.104168 -0.720094 -0.686013
			-0.868182 -0.410119  0.279398
			-0.837714  0.458961  0.295957
			 0.563647  0.209566 -0.798989
			 0.590522  0.094475 -0.801473
			 0.837745 -0.448219 -0.311903
			-0.014806  0.247280 -0.968831
			-0.928570 -0.247668 -0.276437
			 0.112651 -0.028780 -0.993218
			-0.910425  0.362096  0.200031
			-0.282644 -0.877232  0.388042
			-0.721829  0.465076  0.512510
			-0.747753  0.578425 -0.326020
			-0.631573  0.249301 -0.734142
			-0.747744  0.578436  0.326023
			-0.631571  0.249296  0.734146
			-0.601925 -0.198294  0.773541
			-0.747914 -0.605742  0.271478
			-0.826970  0.378346  0.415902
			-0.902360 -0.194994  0.384349
			-0.808374 -0.429520  0.402547
			-0.703887  0.666560  0.245440
			 0.213617 -0.942703  0.256278
			 0.018571 -0.946276 -0.322826
			 0.644280 -0.764790 -0.000079
			 0.018530 -0.946319  0.322703
			-0.647235  0.575992  0.499320
			-0.820455  0.571711 -0.000732
			-0.770492  0.312948  0.555343
			-0.647285  0.575994 -0.499252
			-0.770192  0.313246 -0.555591
			-0.703885  0.666561 -0.245444
			-0.902358 -0.194998 -0.384352
			-0.826967  0.378347 -0.415908
			-0.808376 -0.429515 -0.402550
			-0.601919 -0.198276 -0.773550
			-0.747926 -0.605725 -0.271485
			 0.213622 -0.942701 -0.256281
			-0.669896  0.189966  0.717741
			-0.081636 -0.917415  0.389467
			-0.274807 -0.924786 -0.263156
			 0.586222 -0.810151  0.000026
			-0.274803 -0.924787  0.263159
			-0.081618 -0.917389 -0.389533
			-0.634916  0.772579 -0.001760
			-0.668926  0.189398 -0.718795
			-0.154358  0.202970  0.966942
			-0.086238 -0.042497  0.995368
			 0.482907 -0.724805  0.491383
			 0.046703 -0.998908 -0.001121
			 0.482904 -0.724791 -0.491409
			-0.086188 -0.042528 -0.995371
			-0.785703  0.234660  0.572369
			-0.996931  0.078274 -0.001035
			-0.153436  0.201469 -0.967403
			-0.784849  0.233696 -0.573932
			-0.416376 -0.620081  0.664929
			-0.727117 -0.686513 -0.001206
			-0.414926 -0.619866 -0.666036
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.036030  0.358278  0.001522
			-0.036630  0.358695  0.009142
			-0.048272  0.361036  0.001502
			-0.034843  0.361993  0.010667
			-0.035042  0.359484  0.001524
			-0.036630  0.358695 -0.006144
			-0.034843  0.361993 -0.007670
			-0.040233  0.360026  0.017071
			-0.046121  0.361573  0.020928
			-0.059345  0.362770  0.006095
			-0.059418  0.363050  0.001502
			-0.059632  0.363068  0.015027
			-0.055554  0.363391  0.021032
			-0.040233  0.360026 -0.014073
			-0.046121  0.361573 -0.017930
			-0.059345  0.362770 -0.003097
			-0.059632  0.363068 -0.012029
			-0.055554  0.363391 -0.018034
			-0.034913  0.363708  0.001524
			-0.036011  0.364404  0.011315
			-0.039327  0.363824  0.018042
			-0.036011  0.364404 -0.008317
			-0.039327  0.363824 -0.015045
			-0.046309  0.366167  0.021593
			-0.062212  0.366589  0.006229
			-0.062201  0.366468  0.001502
			-0.062212  0.366589 -0.003231
			-0.055852  0.368391  0.021736
			-0.062144  0.364218  0.016691
			-0.062825  0.368064  0.010488
			-0.046309  0.366167 -0.018595
			-0.062144  0.364218 -0.013693
			-0.055852  0.368391 -0.018738
			-0.062825  0.368064 -0.007490
			-0.033194  0.372214  0.001526
			-0.034603  0.371588  0.010972
			-0.040538  0.366761  0.017629
			-0.034603  0.371588 -0.007975
			-0.040538  0.366761 -0.014632
			-0.045747  0.369918  0.019779
			-0.060887  0.372926  0.007563
			-0.060445  0.372835  0.001503
			-0.060887  0.372926 -0.004566
			-0.055183  0.371626  0.020652
			-0.060066  0.371469  0.019609
			-0.062436  0.373858  0.010774
			-0.045747  0.369918 -0.016781
			-0.060066  0.371469 -0.016612
			-0.055183  0.371626 -0.017654
			-0.062436  0.373858 -0.007777
			-0.032110  0.375472  0.001528
			-0.032716  0.375277  0.011978
			-0.039673  0.373452  0.018249
			-0.032716  0.375277 -0.008980
			-0.039673  0.373452 -0.015252
			-0.043979  0.374749  0.019863
			-0.057763  0.380192  0.007765
			-0.059436  0.381043  0.011197
			-0.057106  0.380107  0.001506
			-0.057763  0.380192 -0.004768
			-0.059436  0.381043 -0.008199
			-0.052826  0.377525  0.020790
			-0.056863  0.379527  0.021165
			-0.065500  0.372571  0.017814
			-0.062564  0.374312  0.021502
			-0.060039  0.379185  0.025417
			-0.069522  0.377379  0.005059
			-0.043979  0.374749 -0.016865
			-0.056863  0.379527 -0.018167
			-0.052826  0.377525 -0.017792
			-0.062564  0.374312 -0.018504
			-0.065500  0.372571 -0.014816
			-0.060039  0.379185 -0.022419
			-0.069522  0.377379 -0.002061
			-0.031547  0.379292  0.001528
			-0.032320  0.379289  0.012170
			-0.037368  0.376202  0.019230
			-0.032320  0.379289 -0.009173
			-0.037368  0.376202 -0.016232
			-0.042854  0.377243  0.021804
			-0.057094  0.386503  0.009488
			-0.058031  0.385218  0.011912
			-0.055778  0.385314  0.001507
			-0.057094  0.386503 -0.006490
			-0.058031  0.385218 -0.008914
			-0.050211  0.380648  0.022783
			-0.054472  0.381724  0.020503
			-0.059821  0.384317  0.020548
			-0.071776  0.381229  0.023578
			-0.070831  0.377237  0.017908
			-0.069229  0.371387  0.025116
			-0.067663  0.370758  0.011555
			-0.067255  0.379084  0.024213
			-0.069822  0.373466  0.022148
			-0.070901  0.372847  0.001502
			-0.075690  0.363287  0.006382
			-0.076897  0.380311  0.001504
			-0.070581  0.382976  0.008868
			-0.042854  0.377243 -0.018806
			-0.054472  0.381724 -0.017506
			-0.050211  0.380648 -0.019785
			-0.059821  0.384317 -0.017550
			-0.070831  0.377237 -0.014910
			-0.071776  0.381229 -0.020580
			-0.069229  0.371387 -0.022118
			-0.067663  0.370758 -0.008557
			-0.069822  0.373466 -0.019151
			-0.067255  0.379084 -0.021215
			-0.075690  0.363287 -0.003384
			-0.070581  0.382976 -0.005870
			-0.044957  0.383192  0.001508
			-0.036769  0.380346  0.019475
			-0.042867  0.382214  0.022785
			-0.036769  0.380346 -0.016477
			-0.042867  0.382214 -0.019787
			-0.050860  0.384055  0.022915
			-0.068190  0.390493  0.016775
			-0.059279  0.385792  0.014965
			-0.068386  0.388156  0.011107
			-0.055502  0.385623  0.017843
			-0.059279  0.385792 -0.011967
			-0.068190  0.390493 -0.013777
			-0.068386  0.388156 -0.008109
			-0.055502  0.385623 -0.014845
			-0.068464  0.384222  0.021773
			-0.067139  0.386646  0.019061
			-0.079819  0.392659  0.034710
			-0.076188  0.383644  0.021636
			-0.070798  0.386177  0.021810
			-0.082347  0.370876  0.013569
			-0.078260  0.371254  0.016275
			-0.081823  0.384521  0.025681
			-0.075067  0.381024  0.013604
			-0.068435  0.369129  0.012954
			-0.082802  0.367434  0.017774
			-0.070593  0.380194  0.026622
			-0.072336  0.378689  0.024410
			-0.072200  0.384889  0.024733
			-0.077285  0.368183  0.001501
			-0.075600  0.362367  0.007898
			-0.088124  0.371959  0.001503
			-0.078363  0.377550  0.006232
			-0.075108  0.375820  0.009424
			-0.074438  0.379455  0.011431
			-0.078363  0.377550 -0.003235
			-0.075108  0.375820 -0.006426
			-0.074438  0.379455 -0.008434
			-0.071459  0.383647  0.012337
			-0.050860  0.384055 -0.019917
			-0.068464  0.384222 -0.018776
			-0.067139  0.386646 -0.016063
			-0.078260  0.371254 -0.013277
			-0.082347  0.370876 -0.010571
			-0.076188  0.383644 -0.018639
			-0.081823  0.384521 -0.022683
			-0.075067  0.381024 -0.010607
			-0.079819  0.392659 -0.031712
			-0.070798  0.386177 -0.018813
			-0.068435  0.369129 -0.009957
			-0.082802  0.367434 -0.014777
			-0.072336  0.378689 -0.021412
			-0.070593  0.380194 -0.023624
			-0.072200  0.384889 -0.021735
			-0.075600  0.362367 -0.004900
			-0.071459  0.383647 -0.009339
			-0.085191  0.391220  0.029336
			-0.074646  0.386021  0.014792
			-0.085191  0.391220 -0.026338
			-0.074646  0.386021 -0.011794
			-0.083810  0.389588  0.038297
			-0.085721  0.388054  0.030093
			-0.084881  0.369031  0.011297
			-0.085715  0.365296  0.010770
			-0.083731  0.361899  0.017542
			-0.081912  0.372580  0.009949
			-0.076962  0.360733  0.014216
			-0.077242  0.365507  0.006588
			-0.080352  0.364386  0.001500
			-0.077242  0.365507 -0.003590
			-0.086383  0.373713  0.005496
			-0.096054  0.363148  0.001484
			-0.086772  0.369899  0.005387
			-0.086383  0.373713 -0.002498
			-0.086772  0.369899 -0.002390
			-0.081912  0.372580 -0.006951
			-0.085715  0.365296 -0.007772
			-0.084881  0.369031 -0.008299
			-0.083731  0.361899 -0.014544
			-0.083810  0.389588 -0.035300
			-0.085722  0.388054 -0.027095
			-0.076962  0.360733 -0.011218
			-0.087577  0.366354  0.005555
			-0.087044  0.364378  0.006255
			-0.082761  0.362169  0.008792
			-0.087640  0.360235  0.001498
			-0.082761  0.362169 -0.005795
			-0.087044  0.364378 -0.003257
			-0.102841  0.354636  0.001479
			-0.087577  0.366354 -0.002558
			-0.097553  0.352701  0.004375
			-0.090534  0.358761  0.005929
			-0.099263  0.346278  0.003741
			-0.099874  0.346144  0.001496
			-0.099263  0.346278 -0.000743
			-0.090534  0.358761 -0.002931
			-0.105322  0.352040  0.004407
			-0.106200  0.351047  0.001485
			-0.097553  0.352701 -0.001377
			-0.105322  0.352040 -0.001409
			-0.104374  0.348707  0.004885
			-0.102914  0.346942  0.001479
			-0.104374  0.348707 -0.001888
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
