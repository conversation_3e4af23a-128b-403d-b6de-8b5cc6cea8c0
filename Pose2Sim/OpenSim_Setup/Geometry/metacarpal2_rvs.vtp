<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="227" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="448">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.421225 -0.056067 -0.905221
			 0.598572  0.308555 -0.739260
			 0.190258 -0.005854 -0.981717
			 0.245012 -0.212188 -0.946016
			 0.145160 -0.280261 -0.948885
			 0.748017 -0.188003 -0.636494
			 0.630857  0.135883 -0.763908
			 0.428923 -0.057207 -0.901528
			 0.289251  0.031472 -0.956736
			 0.417899  0.604589 -0.678109
			 0.355935  0.154821 -0.921597
			 0.854170  0.264078 -0.447947
			 0.808090  0.511606 -0.291977
			 0.613253  0.318741 -0.722720
			 0.809259 -0.001126 -0.587452
			 0.097488 -0.455363 -0.884952
			 0.807329 -0.382404 -0.449429
			 0.545958 -0.295239 -0.784069
			 0.790009 -0.104958 -0.604044
			-0.607255 -0.158360 -0.778565
			-0.221301 -0.238364 -0.945626
			 0.065291 -0.388731 -0.919035
			 0.603265  0.747017 -0.279349
			 0.749905  0.661358  0.015756
			 0.684162  0.063189 -0.726587
			 0.100887  0.011124 -0.994836
			-0.845848 -0.117179 -0.520395
			 0.908859  0.416975  0.010336
			 0.968020  0.234206  0.089917
			 0.912770  0.407971 -0.020276
			 0.986965 -0.107950 -0.119360
			 0.488941 -0.739750 -0.462284
			 0.165085 -0.561790 -0.810641
			 0.728684 -0.684770 -0.010483
			 0.307248 -0.697045 -0.647863
			 0.205910 -0.708997 -0.674481
			-0.974795 -0.164553 -0.150655
			-0.816029 -0.210158 -0.538453
			-0.961555 -0.265895 -0.068639
			-0.858065 -0.458246 -0.231807
			-0.608962 -0.470906 -0.638289
			-0.537433 -0.752825 -0.380027
			 0.904238  0.426029 -0.029222
			 0.922235  0.338548  0.186727
			 0.820291 -0.012721 -0.571805
			 0.248962 -0.096389 -0.963705
			-0.489278 -0.120181 -0.863808
			-0.890175 -0.043100  0.453576
			-0.979118 -0.184787 -0.084748
			-0.747839 -0.083005  0.658670
			 0.778901  0.627131  0.004432
			 0.764732  0.394464  0.509493
			 0.609421  0.567132  0.554047
			 0.372810  0.249754  0.893664
			 0.978027  0.069218  0.196651
			 0.593520  0.133417  0.793684
			 0.916176 -0.371772  0.149691
			 0.259195 -0.944281 -0.202859
			 0.415708 -0.844054  0.338763
			-0.544744 -0.836274 -0.062447
			-0.249219 -0.958681 -0.137186
			-0.884268  0.208491  0.417853
			-0.795783 -0.603296  0.052570
			-0.982857 -0.184363  0.001594
			-0.913504 -0.335573 -0.230004
			-0.853087 -0.516440  0.074374
			-0.856557 -0.406244  0.318239
			-0.855485 -0.514880  0.055173
			-0.671469 -0.728711 -0.134574
			 0.974631  0.223720 -0.006632
			 0.760152  0.630497  0.156977
			 0.950859  0.308230  0.029345
			 0.908487 -0.111007  0.402901
			 0.683285 -0.234138 -0.691593
			 0.129108 -0.397370 -0.908531
			-0.629613 -0.295465 -0.718531
			-0.355482  0.242252  0.902744
			-0.352469  0.085385  0.931920
			-0.715047 -0.304655  0.629201
			-0.908256 -0.320076  0.269487
			-0.932946 -0.341932 -0.112668
			-0.753668 -0.399642 -0.521796
			 0.035131  0.088152  0.995487
			 0.000655 -0.016024  0.999871
			-0.130006 -0.160971  0.978359
			 0.064338  0.788394  0.611797
			 0.334993  0.896830  0.288922
			 0.222338  0.345927  0.911537
			-0.151338  0.232277  0.960804
			 0.993151 -0.102555  0.055984
			 0.807082 -0.015607  0.590233
			 0.734329  0.064926  0.675681
			 0.456533  0.031902  0.889135
			 0.662212 -0.538222  0.521336
			-0.165732 -0.917202  0.362318
			 0.053751 -0.712045  0.700074
			-0.557734 -0.801013  0.217510
			-0.790760 -0.465164  0.397894
			-0.810579 -0.171186  0.560051
			-0.451923  0.186461  0.872352
			-0.733426 -0.456438  0.503737
			-0.361464 -0.557657  0.747236
			-0.590654 -0.708320  0.386536
			-0.660756 -0.211357  0.720229
			 0.936442  0.146367  0.318831
			 0.591899  0.281712  0.755178
			 0.412439  0.730568  0.544210
			 0.265166 -0.292924  0.918631
			 0.175079 -0.539718  0.823439
			 0.737923 -0.015923  0.674697
			 0.940540 -0.322850 -0.105610
			-0.366362 -0.552917 -0.748373
			 0.451819 -0.472056 -0.756983
			-0.768803 -0.280567  0.574652
			-0.303594 -0.398196  0.865604
			-0.940240  0.006691  0.340448
			-0.970587 -0.236959  0.042572
			-0.982346 -0.145386 -0.117728
			-0.778270 -0.469779 -0.416657
			-0.528620 -0.652104 -0.543435
			 0.420320 -0.057256  0.905568
			 0.331547 -0.214602  0.918707
			-0.602472  0.272239  0.750275
			-0.142465  0.607318  0.781581
			-0.190535 -0.395723  0.898388
			-0.640700  0.032839  0.767089
			-0.245669 -0.187133  0.951119
			 0.792621 -0.137048  0.594113
			-0.524193 -0.536183  0.661611
			-0.384920 -0.312325  0.868498
			-0.663843 -0.139215  0.734801
			-0.559466 -0.209480  0.801945
			-0.508278  0.148950  0.848214
			 0.164755  0.006860  0.986311
			-0.539946 -0.067686  0.838974
			 0.940705 -0.052598  0.335122
			 0.950718 -0.230766 -0.207079
			 0.998182 -0.030523 -0.051977
			-0.032506 -0.381698 -0.923715
			-0.465105 -0.556474 -0.688487
			 0.711255 -0.353725 -0.607450
			-0.418706 -0.040507  0.907218
			-0.661924  0.336128  0.669981
			-0.894712  0.303268  0.327900
			-0.971759  0.204263  0.118156
			-0.992491  0.120784 -0.019307
			-0.839545 -0.187891 -0.509765
			-0.911357  0.389442 -0.133279
			-0.837912  0.544814 -0.032873
			 0.544133 -0.045036  0.837789
			 0.715897  0.014284  0.698060
			 0.746956 -0.037839  0.663796
			 0.580420  0.075091  0.810848
			 0.789775  0.241043  0.564051
			 0.688102  0.613994  0.386687
			 0.882602  0.270202  0.384713
			 0.954838 -0.286701 -0.078015
			 0.926182 -0.359915  0.112463
			 0.995857 -0.089894  0.013736
			 0.958235  0.232589  0.166396
			-0.001389  0.250176 -0.968199
			 0.693093 -0.256093 -0.673824
			-0.402646 -0.065876 -0.912982
			-0.339143  0.424286  0.839621
			 0.047556  0.259268  0.964634
			-0.366826  0.625644  0.688483
			-0.313240  0.624188  0.715731
			-0.639551  0.605675  0.473425
			-0.775703  0.614303 -0.144625
			-0.334386  0.919507  0.206622
			-0.809388  0.582458 -0.075054
			-0.637474  0.354884 -0.683875
			-0.512434  0.845362 -0.150914
			-0.271806  0.943245 -0.190817
			-0.341586  0.937489 -0.066586
			 0.370803  0.310727  0.875188
			 0.663295 -0.147609  0.733657
			 0.565217 -0.019588  0.824710
			 0.573794  0.104423  0.812315
			 0.753098  0.049917  0.656012
			 0.379410  0.445516  0.810903
			 0.743072  0.557791  0.369748
			 0.915401  0.393551  0.084603
			 0.613389  0.403618  0.678857
			 0.866130 -0.477951 -0.146229
			 0.909266 -0.111940  0.400880
			 0.929410 -0.306678 -0.205294
			 0.959687 -0.153201  0.235648
			 0.607976  0.179748  0.773341
			 0.254918  0.810406 -0.527502
			-0.218783  0.389688 -0.894582
			-0.119272  0.741324 -0.660465
			 0.720089  0.070640 -0.690276
			 0.166541  0.554839  0.815118
			-0.294909  0.866449 -0.402857
			-0.269272  0.897850 -0.348365
			 0.146984  0.961851  0.230734
			 0.308848  0.815528  0.489415
			-0.294431  0.757593 -0.582549
			-0.039357  0.998995 -0.021456
			-0.133054  0.740298 -0.658980
			 0.050116  0.986325 -0.157008
			 0.043767  0.994274  0.097484
			 0.564240  0.267625  0.781032
			 0.736312  0.110658  0.667533
			 0.949948  0.252743  0.183627
			 0.599323  0.229236  0.766983
			 0.920935  0.024190  0.388966
			 0.684766  0.724774  0.076142
			 0.663543  0.730933  0.159524
			 0.930007  0.122081 -0.346675
			 0.996838  0.075345  0.025244
			 0.769809  0.637158 -0.037728
			 0.779653  0.361766  0.511142
			 0.649725  0.643867  0.404094
			-0.080756  0.961911 -0.261164
			 0.665883  0.695902 -0.268925
			-0.034390  0.876719 -0.479772
			 0.562191  0.662728  0.494705
			 0.024233  0.884034 -0.466793
			 0.099840  0.944852 -0.311909
			-0.160412  0.737574 -0.655937
			 0.027012  0.957015 -0.288778
			 0.472720  0.781777  0.406645
			 0.764562  0.643938 -0.028078
			 0.057268  0.842326 -0.535917
			 0.317957  0.944481  0.082816
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.006418 -0.091497  0.014091
			 0.005763 -0.087840  0.014010
			 0.008875 -0.092487  0.014031
			 0.008727 -0.097804  0.014683
			 0.006357 -0.095254  0.014543
			 0.005324 -0.095440  0.013733
			 0.004292 -0.088033  0.012202
			 0.002910 -0.094522  0.011015
			 0.002249 -0.089396  0.010993
			 0.008047 -0.089445  0.014305
			-0.000622 -0.078941  0.011610
			 0.003696 -0.080672  0.015605
			 0.006507 -0.086323  0.017074
			 0.009354 -0.090167  0.014271
			 0.010657 -0.093107  0.015051
			 0.008426 -0.099044  0.015063
			 0.010230 -0.098536  0.016271
			 0.005573 -0.097176  0.015380
			 0.004416 -0.098195  0.012616
			-0.001093 -0.086250  0.011412
			 0.002616 -0.096928  0.011241
			 0.003303 -0.098532  0.011825
			 0.009401 -0.089423  0.015103
			 0.007991 -0.088663  0.016937
			 0.001591 -0.071860  0.014502
			-0.003313 -0.066150  0.011957
			-0.005083 -0.070218  0.012708
			 0.005936 -0.084570  0.018622
			 0.003924 -0.077927  0.017488
			 0.010136 -0.090566  0.016038
			 0.011215 -0.096214  0.017509
			 0.009050 -0.100744  0.016950
			 0.007468 -0.100197  0.015945
			 0.008953 -0.101139  0.019330
			 0.003883 -0.099095  0.012094
			 0.005595 -0.100640  0.015152
			-0.002705 -0.089473  0.019468
			 0.000435 -0.095839  0.012009
			-0.000291 -0.094836  0.018272
			-0.001222 -0.093301  0.018452
			 0.000727 -0.097438  0.012488
			 0.000972 -0.098446  0.013398
			 0.008017 -0.090234  0.022637
			 0.011086 -0.094382  0.022191
			 0.000389 -0.062395  0.013626
			-0.001656 -0.062519  0.012373
			-0.007048 -0.051412  0.012485
			-0.004320 -0.076252  0.019021
			-0.008201 -0.050700  0.014921
			-0.006767 -0.057267  0.017438
			 0.006338 -0.086849  0.023942
			 0.003377 -0.080081  0.020748
			 0.004908 -0.083998  0.022309
			-0.000627 -0.068893  0.019292
			 0.000851 -0.061558  0.015896
			-0.000355 -0.065345  0.018441
			 0.011320 -0.097686  0.020341
			 0.007736 -0.101589  0.017881
			 0.008192 -0.100751  0.023073
			 0.002066 -0.099656  0.017401
			 0.005339 -0.101962  0.017939
			-0.002330 -0.087171  0.022308
			-0.001568 -0.093527  0.020262
			-0.003191 -0.090934  0.021694
			 0.000378 -0.097254  0.012995
			-0.000158 -0.094894  0.020223
			 0.000559 -0.097205  0.020438
			 0.000334 -0.098501  0.016686
			 0.000794 -0.098912  0.015572
			 0.008799 -0.090610  0.026752
			 0.007918 -0.087812  0.026297
			 0.008637 -0.091450  0.025271
			 0.009514 -0.095319  0.026213
			-0.000353 -0.050340  0.009466
			 0.000760 -0.046022  0.008479
			-0.007478 -0.045993  0.011231
			 0.000032 -0.085235  0.023418
			-0.003067 -0.072436  0.020338
			-0.008508 -0.041104  0.018425
			-0.009346 -0.040681  0.017325
			-0.009862 -0.040246  0.013492
			-0.008387 -0.042214  0.010659
			-0.003685 -0.065966  0.019639
			-0.004105 -0.050127  0.019058
			-0.005016 -0.044632  0.019442
			 0.005944 -0.086565  0.024805
			 0.006860 -0.087093  0.025493
			 0.001289 -0.081021  0.022664
			 0.003356 -0.087372  0.024550
			 0.000796 -0.049836  0.013803
			-0.000005 -0.053653  0.016923
			-0.000062 -0.059689  0.017323
			-0.002664 -0.056294  0.018770
			 0.009006 -0.099580  0.023722
			 0.006157 -0.101493  0.021471
			 0.007872 -0.099635  0.024522
			 0.003302 -0.100752  0.019412
			 0.001803 -0.098287  0.022248
			-0.002643 -0.091024  0.023874
			-0.001809 -0.089441  0.024512
			 0.000346 -0.094894  0.021923
			 0.000738 -0.092887  0.023492
			-0.002303 -0.092314  0.022822
			 0.001988 -0.095771  0.022763
			 0.009070 -0.092012  0.027063
			 0.008202 -0.089835  0.027782
			 0.007426 -0.087955  0.026819
			 0.008276 -0.095284  0.027435
			 0.007881 -0.096710  0.026763
			 0.008829 -0.092329  0.027520
			 0.001951 -0.045131  0.011475
			-0.006074 -0.041444  0.008315
			 0.001332 -0.041782  0.006232
			-0.009284 -0.038402  0.019842
			-0.005600 -0.042018  0.020146
			-0.011040 -0.036929  0.015661
			-0.011130 -0.037536  0.013635
			-0.011074 -0.036623  0.010914
			-0.009251 -0.039895  0.009550
			-0.007870 -0.040857  0.008971
			-0.001333 -0.050108  0.018405
			-0.002003 -0.044087  0.019184
			 0.005293 -0.089019  0.025861
			 0.007251 -0.088384  0.027573
			-0.001119 -0.091513  0.024479
			 0.004163 -0.091897  0.025185
			 0.002132 -0.091433  0.024357
			 0.000606 -0.044281  0.017240
			 0.003137 -0.098227  0.023981
			 0.002099 -0.094116  0.023459
			 0.004612 -0.092850  0.025404
			 0.005576 -0.095235  0.026820
			 0.005980 -0.090682  0.027171
			 0.008333 -0.092506  0.027888
			 0.005893 -0.092716  0.027675
			 0.003899 -0.038738  0.013466
			 0.003439 -0.038217  0.006564
			 0.004271 -0.038419  0.009883
			-0.000605 -0.038553  0.004913
			-0.007078 -0.040894  0.008119
			 0.003318 -0.037396  0.005496
			-0.006147 -0.035558  0.023095
			-0.008005 -0.032010  0.020327
			-0.008762 -0.030025  0.018014
			-0.011080 -0.034641  0.014458
			-0.011332 -0.036006  0.013041
			-0.010803 -0.034955  0.007702
			-0.010725 -0.033142  0.008493
			-0.010469 -0.033045  0.010822
			-0.004098 -0.036662  0.022561
			-0.001985 -0.039544  0.019789
			 0.001968 -0.039579  0.016855
			-0.000806 -0.039758  0.018853
			 0.002370 -0.038486  0.016205
			 0.002493 -0.037663  0.015301
			 0.004124 -0.037836  0.012323
			 0.003615 -0.037010  0.006419
			 0.003641 -0.036551  0.007294
			 0.003629 -0.035728  0.009015
			 0.003610 -0.035783  0.010734
			 0.002133 -0.032930  0.003359
			 0.004535 -0.034129  0.004675
			-0.009292 -0.033941  0.006087
			-0.006292 -0.032866  0.021900
			-0.005515 -0.034519  0.023011
			-0.007752 -0.030529  0.019465
			-0.006417 -0.031293  0.020771
			-0.008525 -0.029700  0.018535
			-0.009242 -0.030662  0.016616
			-0.008335 -0.029298  0.017313
			-0.010087 -0.032288  0.012879
			-0.009899 -0.033160  0.006565
			-0.009117 -0.031779  0.007341
			-0.008838 -0.031302  0.011759
			-0.009124 -0.031870  0.010216
			-0.004430 -0.033176  0.022241
			-0.003336 -0.034611  0.021277
			-0.001106 -0.037451  0.018274
			 0.001329 -0.038315  0.017273
			 0.000094 -0.036832  0.018009
			 0.001714 -0.035856  0.013047
			 0.000371 -0.036011  0.013943
			 0.000272 -0.035882  0.015856
			 0.003122 -0.035522  0.012780
			 0.004220 -0.035450  0.005969
			 0.004029 -0.034292  0.010102
			 0.005067 -0.034178  0.005696
			 0.004458 -0.034269  0.008728
			 0.002100 -0.034328  0.012412
			 0.002538 -0.031952  0.004127
			-0.006861 -0.033151  0.005688
			-0.003959 -0.032431  0.005389
			 0.004693 -0.033366  0.004765
			-0.005561 -0.032297  0.021842
			-0.007700 -0.030327  0.014316
			-0.006899 -0.028840  0.016319
			-0.006199 -0.028869  0.016668
			-0.004681 -0.030902  0.020461
			-0.008541 -0.031811  0.006558
			-0.007844 -0.031641  0.007150
			-0.005268 -0.031514  0.012556
			-0.000874 -0.032750  0.010169
			-0.005075 -0.031770  0.008217
			-0.002312 -0.033613  0.020902
			-0.000341 -0.033883  0.019296
			 0.000126 -0.034008  0.018477
			 0.000238 -0.034705  0.013317
			-0.000241 -0.033260  0.013685
			 0.004038 -0.033168  0.009812
			 0.003334 -0.032767  0.010635
			 0.004984 -0.033551  0.005260
			 0.005071 -0.033657  0.005802
			 0.004941 -0.033280  0.006757
			-0.000169 -0.031987  0.014216
			 0.001331 -0.031506  0.012322
			-0.005299 -0.031367  0.006769
			 0.004550 -0.032811  0.004928
			-0.007177 -0.031603  0.006491
			-0.002939 -0.031652  0.020091
			-0.002241 -0.030524  0.013213
			-0.002070 -0.030723  0.012236
			-0.002259 -0.031125  0.011412
			-0.005240 -0.029078  0.015601
			-0.004058 -0.029582  0.015962
			 0.004887 -0.033037  0.005420
			-0.002056 -0.029862  0.013948
			-0.003241 -0.029199  0.014937
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					0 3 4 
					0 4 5 
					1 0 6 
					7 0 5 
					8 0 7 
					6 0 8 
					9 2 1 
					6 10 1 
					10 11 1 
					12 9 1 
					11 12 1 
					9 13 2 
					13 14 2 
					3 2 14 
					3 15 4 
					15 3 16 
					3 14 16 
					4 15 17 
					4 17 5 
					5 18 7 
					17 18 5 
					6 8 10 
					8 7 19 
					20 19 7 
					7 18 21 
					7 21 20 
					8 19 10 
					22 9 23 
					22 13 9 
					12 23 9 
					10 24 11 
					10 25 24 
					25 10 26 
					19 26 10 
					12 11 27 
					24 28 11 
					27 11 28 
					12 27 23 
					29 13 22 
					13 29 14 
					30 16 14 
					14 29 30 
					31 15 16 
					31 32 15 
					15 32 17 
					16 30 33 
					31 16 33 
					17 34 18 
					35 34 17 
					17 32 35 
					18 34 21 
					26 19 36 
					20 37 19 
					38 19 37 
					39 19 38 
					19 39 36 
					37 20 40 
					21 40 20 
					41 40 21 
					34 41 21 
					29 22 23 
					42 43 23 
					43 29 23 
					42 23 27 
					44 24 45 
					45 24 25 
					24 44 28 
					25 46 45 
					26 46 25 
					26 36 47 
					48 26 49 
					26 47 49 
					26 48 46 
					27 50 42 
					28 51 27 
					52 27 51 
					52 50 27 
					51 28 53 
					54 28 44 
					28 54 55 
					28 55 53 
					43 30 29 
					56 33 30 
					30 43 56 
					31 33 57 
					57 32 31 
					32 57 35 
					58 33 56 
					33 58 57 
					34 59 41 
					34 35 59 
					35 60 59 
					57 60 35 
					47 36 61 
					62 63 36 
					62 36 39 
					36 63 61 
					37 64 38 
					37 40 64 
					65 38 66 
					38 67 66 
					67 38 64 
					39 38 65 
					62 39 65 
					40 41 64 
					64 41 68 
					59 68 41 
					69 42 70 
					71 43 42 
					71 42 69 
					70 42 50 
					56 43 72 
					43 71 72 
					73 54 44 
					45 73 44 
					46 73 45 
					74 46 75 
					74 73 46 
					75 46 48 
					76 77 47 
					77 49 47 
					61 76 47 
					49 78 48 
					78 79 48 
					80 48 79 
					48 80 81 
					75 48 81 
					82 83 49 
					77 82 49 
					83 84 49 
					84 78 49 
					52 85 50 
					86 70 50 
					85 86 50 
					87 52 51 
					53 87 51 
					52 88 85 
					87 88 52 
					87 53 76 
					77 76 53 
					77 53 82 
					53 55 82 
					89 90 54 
					73 89 54 
					55 54 91 
					54 90 91 
					92 82 55 
					92 55 91 
					72 93 56 
					58 56 93 
					58 94 57 
					94 60 57 
					93 95 58 
					94 58 95 
					59 96 97 
					66 67 59 
					66 59 97 
					67 68 59 
					60 96 59 
					94 96 60 
					98 61 63 
					61 98 99 
					61 99 76 
					62 100 101 
					65 100 62 
					62 101 102 
					63 62 102 
					98 63 102 
					64 68 67 
					65 66 100 
					97 103 66 
					103 100 66 
					72 69 104 
					69 105 104 
					71 69 72 
					70 105 69 
					106 105 70 
					70 86 106 
					107 108 72 
					104 109 72 
					107 72 109 
					93 72 108 
					110 89 73 
					74 110 73 
					75 111 74 
					112 74 111 
					74 112 110 
					81 111 75 
					88 76 99 
					87 76 88 
					113 79 78 
					78 114 113 
					78 84 114 
					113 115 79 
					116 80 79 
					116 79 115 
					80 117 118 
					81 80 118 
					116 117 80 
					81 119 111 
					118 119 81 
					83 82 92 
					92 120 83 
					84 83 120 
					84 120 121 
					84 121 114 
					122 85 88 
					86 85 123 
					123 85 122 
					86 123 106 
					99 124 88 
					125 122 88 
					125 88 126 
					126 88 124 
					127 89 110 
					90 89 127 
					91 90 92 
					90 127 120 
					120 92 90 
					108 95 93 
					128 96 94 
					95 128 94 
					108 128 95 
					96 128 97 
					97 128 103 
					124 99 98 
					102 124 98 
					101 100 103 
					101 126 124 
					101 103 129 
					101 129 126 
					101 124 102 
					130 129 103 
					128 131 103 
					130 103 131 
					109 104 105 
					132 105 123 
					123 105 106 
					133 109 105 
					133 105 132 
					134 107 133 
					131 107 134 
					107 131 108 
					107 109 133 
					128 108 131 
					110 135 127 
					110 112 136 
					137 110 136 
					135 110 137 
					138 112 111 
					139 111 119 
					138 111 139 
					112 138 140 
					112 140 136 
					113 141 115 
					113 114 141 
					121 141 114 
					115 142 143 
					144 115 143 
					115 141 142 
					115 144 116 
					144 145 116 
					145 117 116 
					146 118 117 
					147 146 117 
					148 147 117 
					148 117 145 
					118 146 139 
					139 119 118 
					120 127 121 
					149 141 121 
					127 150 121 
					149 121 150 
					132 122 125 
					123 122 132 
					125 126 130 
					125 130 134 
					132 125 134 
					130 126 129 
					135 151 127 
					152 127 151 
					127 152 150 
					130 131 134 
					134 133 132 
					153 135 154 
					155 135 137 
					155 154 135 
					151 135 153 
					156 136 140 
					157 136 156 
					157 158 136 
					137 136 158 
					158 159 137 
					155 137 159 
					160 161 138 
					161 140 138 
					162 160 138 
					138 139 162 
					162 139 146 
					140 161 156 
					141 163 142 
					164 163 141 
					149 164 141 
					142 165 143 
					142 163 166 
					166 165 142 
					165 167 143 
					144 143 168 
					143 167 169 
					168 143 169 
					144 168 170 
					170 145 144 
					148 145 170 
					162 146 171 
					147 171 146 
					172 147 148 
					172 171 147 
					148 173 174 
					148 170 173 
					172 148 174 
					149 175 164 
					150 176 149 
					175 149 176 
					152 177 150 
					176 150 177 
					151 153 178 
					178 152 151 
					178 177 152 
					153 154 179 
					153 179 178 
					155 180 154 
					154 180 181 
					181 182 154 
					154 182 179 
					183 155 159 
					183 180 155 
					156 184 157 
					161 184 156 
					157 185 158 
					157 184 186 
					186 187 157 
					187 185 157 
					159 158 185 
					159 188 183 
					185 188 159 
					160 189 161 
					190 191 160 
					191 189 160 
					160 162 190 
					186 184 161 
					192 186 161 
					192 161 189 
					190 162 171 
					163 175 193 
					163 193 166 
					164 175 163 
					169 165 166 
					167 165 169 
					168 194 170 
					168 195 194 
					169 195 168 
					169 196 195 
					197 196 169 
					173 170 194 
					171 172 198 
					198 190 171 
					199 172 174 
					199 198 172 
					173 200 201 
					202 174 173 
					202 173 201 
					173 194 200 
					174 202 199 
					203 175 176 
					175 203 193 
					176 177 179 
					179 204 176 
					203 176 204 
					177 178 179 
					179 182 205 
					179 205 204 
					180 206 181 
					183 188 180 
					206 180 188 
					207 182 181 
					207 181 206 
					205 182 207 
					185 187 208 
					185 208 209 
					209 188 185 
					210 211 186 
					187 186 211 
					210 186 192 
					212 208 187 
					212 187 211 
					213 188 214 
					188 209 214 
					188 207 206 
					213 207 188 
					191 215 189 
					215 202 189 
					216 192 189 
					189 202 201 
					216 189 201 
					217 191 190 
					190 198 217 
					199 215 191 
					199 191 217 
					216 210 192 
					193 218 197 
					203 218 193 
					219 220 194 
					200 194 221 
					221 194 220 
					194 195 219 
					219 195 222 
					196 222 195 
					196 197 223 
					222 196 223 
					197 218 223 
					217 198 199 
					199 202 215 
					221 201 200 
					224 216 201 
					212 224 201 
					212 201 221 
					203 204 218 
					218 204 205 
					213 205 207 
					205 213 218 
					219 209 208 
					208 212 221 
					208 221 220 
					208 220 219 
					214 209 225 
					225 209 219 
					216 224 210 
					224 211 210 
					224 212 211 
					223 218 213 
					223 213 214 
					214 225 226 
					223 214 226 
					222 225 219 
					226 225 222 
					223 226 222 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 1275 1278 1281 1284 1287 1290 1293 1296 1299 1302 1305 1308 1311 1314 1317 1320 1323 1326 1329 1332 1335 1338 1341 1344 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
