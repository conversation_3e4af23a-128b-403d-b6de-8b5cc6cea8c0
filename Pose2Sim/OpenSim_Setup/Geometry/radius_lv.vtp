<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="194" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="384">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.363482 -0.592934  0.718547
			-0.002958 -0.903033  0.429560
			-0.302971 -0.542083  0.783808
			-0.024822 -0.402710  0.914991
			 0.193513 -0.761703  0.618354
			-0.305790  0.351938  0.884665
			 0.007647  0.278697  0.960349
			-0.314152 -0.929675  0.192387
			-0.031974 -0.898751  0.437292
			-0.904249  0.072862  0.420743
			-0.718064 -0.567334  0.403134
			 0.570431  0.192283  0.798521
			 0.453869 -0.773635  0.442144
			 0.833942  0.225384  0.503730
			 0.566000  0.606151  0.558771
			 0.146951  0.559798  0.815495
			-0.369136  0.505764  0.779706
			-0.868245 -0.425749 -0.254731
			-0.912181 -0.260297 -0.316498
			 0.138802 -0.921934  0.361623
			-0.229118 -0.971881  0.054330
			-0.654281 -0.745874 -0.124856
			 0.525183 -0.780070  0.340109
			 0.303634 -0.857079  0.416200
			 0.078020 -0.942074  0.326204
			-0.413220 -0.888176  0.200982
			-0.385114 -0.922854 -0.005281
			-0.719319  0.392949  0.572863
			-0.933892  0.112109 -0.339524
			 0.733265 -0.633621  0.246670
			 0.930162  0.028083  0.366073
			 0.782731  0.496103  0.375785
			 0.505574  0.351835  0.787786
			 0.809734  0.268902  0.521559
			-0.007351  0.418311  0.908274
			-0.947951 -0.255790 -0.189633
			-0.753471 -0.643321 -0.135716
			-0.785836 -0.496272 -0.369020
			-0.306198 -0.939727 -0.152172
			-0.624246 -0.704420 -0.337800
			 0.852697 -0.521496 -0.030814
			 0.879090 -0.476534 -0.010785
			-0.112654 -0.977906 -0.176095
			 0.457625 -0.763233 -0.456130
			-0.624498 -0.490387 -0.607885
			-0.614181 -0.556977 -0.559069
			-0.566401 -0.396980 -0.722216
			 0.080595  0.152459  0.985018
			-0.923543  0.223523  0.311618
			-0.998526  0.050442  0.020042
			 0.976895 -0.156152  0.145921
			 0.907897 -0.417630 -0.036166
			 0.914538  0.320789  0.246403
			 0.759974  0.102006  0.641899
			 0.838228  0.218954 -0.499433
			-0.907133 -0.174169 -0.383112
			 0.711969 -0.222457 -0.666043
			 0.854854 -0.211272 -0.473908
			-0.348666 -0.440057 -0.827515
			 0.126568 -0.264993 -0.955908
			-0.518214 -0.162137 -0.839741
			-0.597227 -0.042993 -0.800919
			-0.154279  0.065321 -0.985866
			-0.705236  0.087235  0.703586
			 0.161626  0.004669  0.986841
			 0.621167 -0.031287  0.783053
			-0.989055  0.132535  0.064841
			 0.967646  0.068803 -0.242751
			 0.986240 -0.003200  0.165287
			 0.703400  0.095261 -0.704382
			 0.147879  0.137757 -0.979365
			-0.773629  0.144393 -0.616968
			-0.305873  0.107262 -0.946011
			-0.537722 -0.008179  0.843083
			-0.987149  0.096196 -0.127609
			 0.980326 -0.075212  0.182494
			 0.865071  0.021292 -0.501198
			 0.249010  0.061084 -0.966573
			-0.736531  0.110365 -0.667339
			-0.240598  0.068608 -0.968197
			-0.187018 -0.166390  0.968163
			-0.882525  0.084541 -0.462605
			-0.937855  0.089990 -0.335158
			-0.676968  0.093128 -0.730096
			 0.899487 -0.094290  0.426652
			 0.929596  0.015102 -0.368271
			 0.419481  0.057885 -0.905917
			 0.652916  0.060983 -0.754972
			-0.291912  0.083144 -0.952824
			 0.179153 -0.164076  0.970043
			-0.823782 -0.027364  0.566246
			-0.533656  0.120727 -0.837040
			-0.173944  0.202732 -0.963661
			-0.616751  0.167759 -0.769074
			-0.980084 -0.063358  0.188204
			-0.345738 -0.170359  0.922737
			-0.083207  0.167683 -0.982323
			 0.871698 -0.067002  0.485442
			 0.892073  0.133837 -0.431618
			 0.478532  0.184249 -0.858522
			-0.077171  0.115631 -0.990290
			 0.439726  0.219981 -0.870775
			 0.164687  0.212307 -0.963226
			-0.402939  0.156277 -0.901786
			-0.863948  0.073947 -0.498123
			-0.377530 -0.078365  0.922675
			-0.792347 -0.012992  0.609932
			-0.999777 -0.004568 -0.020634
			 0.785955 -0.192990  0.587392
			 0.223333 -0.312312  0.923354
			 0.927968  0.072756 -0.365488
			 0.699072  0.014884 -0.714897
			 0.035506 -0.007966 -0.999338
			-0.823876 -0.015487 -0.566558
			-0.440475 -0.082137 -0.894000
			-0.500329  0.062477  0.863579
			-0.007606 -0.021680  0.999736
			 0.170281 -0.225777  0.959182
			 0.247886 -0.062648  0.966762
			-0.836260 -0.035210  0.547201
			-0.997812 -0.065803 -0.006465
			 0.925417 -0.140284  0.352028
			 0.626487 -0.304965  0.717294
			 0.575903 -0.288641  0.764867
			 0.965649  0.004059  0.259819
			 0.997038  0.041247 -0.064917
			 0.957188 -0.288887 -0.018310
			 0.809660 -0.452703 -0.373512
			 0.491605 -0.469748 -0.733254
			 0.233746 -0.349889 -0.907161
			-0.249630 -0.439064 -0.863081
			-0.807851 -0.293630 -0.511036
			-0.514151 -0.124107  0.848673
			 0.557912  0.021211  0.829629
			 0.098855 -0.418088  0.903012
			 0.623836 -0.142464  0.768461
			 0.713122 -0.126499  0.689532
			 0.686141  0.041872  0.726263
			-0.859807 -0.099418  0.500847
			-0.957923 -0.287006  0.003225
			 0.875859  0.084007  0.475199
			 0.883973 -0.040931  0.465742
			 0.840495 -0.362004  0.403140
			 0.742683 -0.523404  0.417695
			 0.693798 -0.716275 -0.074799
			 0.808602 -0.466106 -0.359037
			 0.686786 -0.341469 -0.641657
			 0.130246 -0.427383 -0.894640
			 0.432286 -0.120453 -0.893656
			-0.712869 -0.227453 -0.663388
			-0.216522 -0.291181 -0.931844
			-0.960979 -0.202255 -0.188714
			-0.520038 -0.357855  0.775565
			 0.424791 -0.532438  0.732163
			 0.602213 -0.529597  0.597384
			-0.053268 -0.405637  0.912481
			-0.860524 -0.295002  0.415299
			-0.934337 -0.300050  0.192313
			 0.363054 -0.154764  0.918825
			 0.681015  0.306343  0.665111
			 0.890941 -0.354085  0.284337
			 0.940060 -0.335562 -0.060709
			 0.919158  0.096787 -0.381812
			 0.687491  0.167098 -0.706706
			 0.127280  0.190226 -0.973455
			 0.384890  0.559742 -0.733859
			-0.555866  0.296865 -0.776456
			-0.302797  0.151015 -0.941014
			-0.823575  0.155967 -0.545343
			-0.936079  0.081941 -0.342114
			-0.877613  0.474639 -0.067185
			-0.966603  0.053711  0.250586
			-0.489839 -0.337557  0.803812
			-0.784918 -0.254065  0.565114
			-0.461981 -0.122158  0.878437
			-0.069997 -0.121500  0.990120
			 0.153706  0.437866  0.885803
			 0.755037  0.649360  0.090838
			 0.172346  0.960322  0.219270
			 0.785830  0.555387 -0.272061
			 0.630349  0.635567 -0.445774
			-0.070432  0.715173 -0.695390
			 0.185946  0.976060 -0.112832
			-0.648844  0.628069 -0.429570
			-0.345829  0.733293 -0.585392
			-0.645571  0.623068  0.441616
			-0.242654  0.969768  0.025872
			-0.782190  0.007631  0.622993
			-0.356654  0.317920  0.878479
			-0.042653  0.933488  0.356063
			 0.284083  0.955858 -0.075054
			 0.081355  0.996266  0.028895
			-0.052086  0.992455 -0.110996
			-0.100452  0.979752  0.173192
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.012807 -0.227620 -0.011017
			-0.013906 -0.229378 -0.014953
			-0.018792 -0.229397 -0.015465
			-0.007831 -0.227726 -0.008202
			-0.003959 -0.228413 -0.009251
			-0.010300 -0.222893 -0.008970
			-0.006925 -0.224190 -0.007649
			-0.016840 -0.233137 -0.020368
			-0.003570 -0.232487 -0.027583
			-0.020873 -0.226810 -0.016046
			-0.019980 -0.229816 -0.016064
			-0.003494 -0.225922 -0.009132
			-0.001507 -0.229580 -0.011938
			-0.000459 -0.227416 -0.012410
			-0.006535 -0.220379 -0.013224
			-0.011412 -0.218668 -0.012810
			-0.012698 -0.218634 -0.013016
			-0.016802 -0.231600 -0.023950
			-0.020038 -0.229244 -0.017451
			-0.008889 -0.234115 -0.027961
			-0.014345 -0.233849 -0.023899
			-0.015652 -0.232957 -0.023784
			 0.002201 -0.233835 -0.027081
			 0.001082 -0.236946 -0.031757
			 0.001192 -0.232679 -0.024164
			-0.004699 -0.237392 -0.032661
			-0.007458 -0.235616 -0.031222
			-0.016402 -0.215415 -0.017506
			-0.018751 -0.224172 -0.019161
			 0.002611 -0.232528 -0.025187
			 0.002632 -0.228528 -0.019733
			 0.000960 -0.224825 -0.023096
			-0.006479 -0.214361 -0.016863
			-0.005214 -0.219404 -0.015905
			-0.010012 -0.214441 -0.015198
			-0.016276 -0.231489 -0.026161
			-0.015604 -0.232902 -0.025442
			-0.015321 -0.232827 -0.026630
			-0.010171 -0.234620 -0.029519
			-0.014596 -0.233642 -0.026839
			 0.001925 -0.236103 -0.033050
			 0.003622 -0.231543 -0.031412
			-0.001760 -0.238496 -0.033939
			 0.000171 -0.237757 -0.034630
			-0.007060 -0.234733 -0.032732
			-0.004768 -0.236899 -0.033883
			-0.012831 -0.230181 -0.031432
			-0.010531 -0.208876 -0.016641
			-0.014505 -0.209184 -0.018183
			-0.016791 -0.226552 -0.025854
			 0.004889 -0.229356 -0.027259
			 0.004817 -0.230221 -0.030299
			 0.004709 -0.225450 -0.029131
			-0.003106 -0.209231 -0.021412
			 0.001638 -0.216138 -0.032050
			-0.017714 -0.225712 -0.028610
			 0.001706 -0.235556 -0.035160
			 0.004170 -0.230514 -0.033350
			-0.002656 -0.236867 -0.035771
			-0.001335 -0.235448 -0.036616
			-0.004100 -0.232077 -0.035333
			-0.016491 -0.225106 -0.030221
			-0.002620 -0.230562 -0.036240
			-0.011755 -0.194929 -0.017976
			-0.007865 -0.187825 -0.017667
			-0.003824 -0.184567 -0.019537
			-0.015071 -0.208991 -0.024859
			 0.004859 -0.227537 -0.032199
			-0.000879 -0.187407 -0.025051
			-0.001127 -0.194182 -0.029375
			-0.004849 -0.208294 -0.032003
			-0.016345 -0.222652 -0.029893
			-0.011011 -0.196621 -0.030061
			-0.008743 -0.146781 -0.016471
			-0.013017 -0.187801 -0.024766
			 0.001280 -0.141554 -0.021405
			-0.000488 -0.170097 -0.026568
			-0.004050 -0.183105 -0.030044
			-0.010782 -0.180135 -0.027759
			-0.006709 -0.169985 -0.029394
			-0.006915 -0.108148 -0.009187
			-0.009404 -0.150746 -0.025524
			-0.007713 -0.101541 -0.018654
			-0.008273 -0.154829 -0.027435
			 0.004237 -0.074194 -0.009913
			 0.002744 -0.112028 -0.020382
			-0.002631 -0.136804 -0.027173
			 0.000514 -0.111550 -0.023618
			-0.005385 -0.145584 -0.028053
			-0.005447 -0.077090 -0.004369
			-0.007081 -0.077017 -0.004865
			-0.007344 -0.136928 -0.026499
			-0.002792 -0.075317 -0.016380
			-0.004357 -0.039016 -0.009127
			-0.006724 -0.046001 -0.002282
			-0.005179 -0.061822 -0.002923
			-0.001008 -0.106362 -0.023414
			 0.003460 -0.066548 -0.006608
			 0.003693 -0.075809 -0.014133
			 0.001330 -0.084748 -0.018840
			-0.003914 -0.131994 -0.026995
			 0.001031 -0.075384 -0.016241
			-0.000632 -0.032441 -0.008162
			-0.003284 -0.023968 -0.005203
			-0.006506 -0.030551 -0.004339
			-0.003637 -0.027095  0.004824
			-0.006663 -0.026528  0.002639
			-0.007226 -0.027171 -0.000762
			 0.003416 -0.048575 -0.002369
			-0.000447 -0.043640  0.001803
			 0.002810 -0.061447 -0.011018
			 0.002935 -0.018762 -0.003659
			-0.000056 -0.018105 -0.004702
			-0.006578 -0.018017 -0.002388
			-0.004396 -0.013991 -0.004581
			-0.005425 -0.023554  0.003148
			-0.001207 -0.024063  0.004948
			 0.000868 -0.035545  0.003937
			 0.001047 -0.031617  0.004724
			-0.006546 -0.018377  0.002195
			-0.007401 -0.016017 -0.000166
			 0.003479 -0.044503 -0.001388
			 0.002715 -0.044268 -0.000345
			 0.002340 -0.040314  0.002064
			 0.004772 -0.024184  0.001531
			 0.004433 -0.018747 -0.000697
			 0.004551 -0.010046  0.000245
			 0.003862 -0.010011 -0.002737
			 0.002312 -0.009756 -0.004922
			 0.000061 -0.009267 -0.006088
			-0.002793 -0.009485 -0.006204
			-0.007544 -0.009581 -0.002727
			-0.004422 -0.019166  0.004021
			 0.002231 -0.019494  0.003887
			-0.000838 -0.011546  0.006633
			 0.002006 -0.015365  0.004294
			 0.003190 -0.031854  0.003510
			 0.003630 -0.024232  0.003397
			-0.006674 -0.013473  0.003189
			-0.007898 -0.009528 -0.001405
			 0.003888 -0.019426  0.002119
			 0.003811 -0.014798  0.002163
			 0.003255 -0.010964  0.003995
			 0.005935 -0.007421  0.006119
			 0.006295 -0.007474  0.000012
			 0.007758 -0.005096 -0.004224
			 0.005232 -0.004721 -0.007624
			 0.000527 -0.004327 -0.010148
			 0.003066 -0.004451 -0.009379
			-0.008384 -0.004303 -0.006957
			-0.003295 -0.004456 -0.009921
			-0.010383 -0.004497 -0.002311
			-0.003923 -0.011275  0.005973
			 0.003492 -0.009098  0.007208
			 0.002209 -0.011468  0.005278
			-0.000624 -0.009400  0.008427
			-0.007047 -0.009992  0.003084
			-0.010446 -0.004622  0.000589
			 0.003387 -0.005394  0.008432
			 0.005580 -0.000392  0.007261
			 0.007822 -0.006408  0.003516
			 0.008658 -0.005775  0.000174
			 0.007988 -0.002455 -0.004045
			 0.005633 -0.001649 -0.007309
			-0.001340 -0.001469 -0.010182
			 0.003438  0.000696 -0.007988
			-0.006608 -0.000038 -0.007873
			-0.003223 -0.001531 -0.010182
			-0.009383 -0.001136 -0.005472
			-0.009975 -0.001139 -0.003982
			-0.010170  0.001355 -0.001946
			-0.010529 -0.001432  0.001122
			-0.003776 -0.008705  0.007860
			-0.008142 -0.006249  0.004253
			-0.003608 -0.003195  0.009032
			-0.001906 -0.004884  0.009199
			 0.000338 -0.000568  0.009643
			 0.007954  0.000446  0.000758
			 0.004665  0.000666  0.006121
			 0.007756 -0.000034 -0.003134
			 0.005622  0.000272 -0.006268
			-0.002526  0.000936 -0.009179
			 0.002673  0.001419 -0.006609
			-0.008684  0.001556 -0.004980
			-0.006185  0.001340 -0.007391
			-0.008504  0.001057  0.004229
			-0.008497  0.002203 -0.001495
			-0.009200 -0.001812  0.004161
			-0.003293 -0.000065  0.009131
			-0.002797  0.001095  0.007738
			 0.007013  0.000477 -0.003040
			 0.001661 -0.000403  0.001182
			-0.007280  0.002313 -0.004042
			-0.007127  0.002063  0.003676
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					2 1 0 
					4 3 0 
					0 6 5 
					6 0 3 
					0 5 2 
					4 0 1 
					7 1 2 
					8 4 1 
					1 7 8 
					10 2 9 
					2 10 7 
					5 9 2 
					11 3 4 
					6 3 11 
					12 4 8 
					13 4 12 
					4 13 11 
					14 5 6 
					14 15 5 
					15 16 5 
					9 5 16 
					11 14 6 
					18 17 7 
					10 18 7 
					20 19 7 
					19 8 7 
					21 20 7 
					17 21 7 
					23 22 8 
					8 24 12 
					22 24 8 
					23 8 25 
					25 8 26 
					8 19 26 
					27 9 16 
					28 9 27 
					18 9 28 
					18 10 9 
					13 14 11 
					29 12 24 
					12 30 13 
					30 12 29 
					14 13 31 
					13 30 31 
					14 32 15 
					33 32 14 
					33 14 31 
					34 16 15 
					32 34 15 
					27 16 34 
					17 28 35 
					36 21 17 
					37 17 35 
					37 36 17 
					28 17 18 
					26 19 38 
					20 38 19 
					39 20 21 
					38 20 39 
					36 39 21 
					40 22 23 
					22 29 24 
					41 29 22 
					41 22 40 
					23 25 42 
					23 43 40 
					42 43 23 
					44 25 26 
					25 44 45 
					45 42 25 
					26 38 46 
					26 46 44 
					48 27 47 
					34 47 27 
					48 28 27 
					28 48 49 
					28 49 35 
					30 29 50 
					51 29 41 
					29 51 50 
					52 30 50 
					31 30 52 
					31 52 33 
					32 33 53 
					47 32 53 
					32 47 34 
					53 33 54 
					52 54 33 
					55 35 49 
					55 37 35 
					39 36 37 
					55 39 37 
					39 46 38 
					55 46 39 
					56 41 40 
					40 43 56 
					57 41 56 
					57 51 41 
					42 45 58 
					42 58 43 
					59 43 58 
					43 59 56 
					60 44 46 
					60 45 44 
					58 45 60 
					62 46 61 
					46 62 60 
					55 61 46 
					48 47 63 
					63 47 64 
					64 47 65 
					47 53 65 
					49 48 66 
					63 66 48 
					66 55 49 
					67 52 50 
					51 67 50 
					67 51 57 
					52 67 54 
					53 68 65 
					68 53 54 
					70 69 54 
					54 69 68 
					54 67 57 
					70 54 62 
					62 54 56 
					54 57 56 
					66 71 55 
					55 71 61 
					56 59 62 
					62 58 60 
					58 62 59 
					61 71 62 
					62 72 70 
					62 71 72 
					64 73 63 
					74 66 63 
					74 63 73 
					65 73 64 
					73 65 75 
					68 75 65 
					71 66 74 
					75 68 76 
					68 69 76 
					77 76 69 
					69 70 77 
					72 77 70 
					78 72 71 
					78 71 74 
					72 79 77 
					79 72 78 
					75 80 73 
					74 73 81 
					82 81 73 
					80 82 73 
					74 83 78 
					74 81 83 
					84 80 75 
					84 75 85 
					86 75 76 
					75 86 87 
					87 85 75 
					76 77 86 
					79 86 77 
					79 78 83 
					83 88 79 
					86 79 88 
					84 89 80 
					82 80 90 
					90 80 89 
					81 91 88 
					81 88 83 
					91 81 82 
					93 92 82 
					95 94 82 
					96 91 82 
					96 82 92 
					95 82 90 
					94 93 82 
					84 97 89 
					84 85 98 
					98 97 84 
					87 99 85 
					99 98 85 
					100 96 86 
					86 88 100 
					86 96 87 
					96 99 87 
					100 88 91 
					95 90 89 
					95 89 97 
					100 91 96 
					99 96 92 
					92 101 99 
					92 102 101 
					92 93 102 
					102 93 103 
					94 104 93 
					103 93 104 
					95 105 94 
					94 105 106 
					104 94 107 
					107 94 106 
					95 97 108 
					105 95 109 
					109 95 108 
					97 98 110 
					110 108 97 
					98 99 101 
					110 98 101 
					110 101 102 
					102 111 110 
					111 102 112 
					103 112 102 
					103 104 113 
					112 103 114 
					113 114 103 
					104 107 113 
					115 106 105 
					116 115 105 
					117 105 109 
					105 117 118 
					105 118 116 
					106 115 119 
					107 106 119 
					107 120 113 
					119 120 107 
					121 108 110 
					109 108 122 
					122 108 121 
					123 117 109 
					109 122 123 
					121 110 124 
					125 124 110 
					110 111 125 
					125 111 126 
					126 111 127 
					127 111 128 
					129 111 112 
					129 128 111 
					129 112 130 
					112 114 130 
					120 131 113 
					114 113 131 
					114 131 130 
					115 116 132 
					119 115 132 
					116 118 133 
					134 132 116 
					116 135 134 
					135 116 133 
					117 123 136 
					136 118 117 
					137 118 136 
					133 118 137 
					132 138 119 
					120 119 138 
					120 139 131 
					139 120 138 
					121 123 122 
					136 121 124 
					121 136 123 
					140 137 124 
					137 136 124 
					124 125 140 
					141 125 126 
					125 141 140 
					126 142 141 
					126 144 143 
					126 143 142 
					126 127 144 
					127 146 145 
					145 144 127 
					146 127 128 
					147 128 129 
					146 128 148 
					148 128 147 
					147 129 130 
					149 130 131 
					150 147 130 
					150 130 149 
					139 151 131 
					149 131 151 
					138 132 152 
					132 134 152 
					140 141 133 
					133 137 140 
					133 141 135 
					154 153 134 
					152 134 155 
					134 135 154 
					134 153 155 
					142 154 135 
					141 142 135 
					152 156 138 
					138 156 139 
					151 139 157 
					157 139 156 
					142 143 154 
					153 143 158 
					158 143 159 
					159 143 160 
					143 153 154 
					144 160 143 
					160 144 161 
					144 145 161 
					162 161 145 
					146 162 145 
					148 163 146 
					162 146 163 
					150 164 147 
					148 147 164 
					165 163 148 
					165 148 164 
					167 149 166 
					149 167 150 
					149 168 166 
					169 149 151 
					168 149 169 
					164 150 167 
					171 170 151 
					151 157 171 
					151 170 169 
					152 155 172 
					173 152 172 
					173 156 152 
					155 153 158 
					155 174 172 
					155 175 174 
					155 158 175 
					156 173 157 
					171 157 173 
					176 158 159 
					158 176 175 
					160 177 159 
					176 159 178 
					159 177 178 
					160 161 177 
					161 162 177 
					162 179 177 
					180 162 163 
					179 162 180 
					180 163 165 
					181 164 167 
					164 181 165 
					180 165 182 
					182 165 181 
					168 183 166 
					183 184 166 
					181 166 184 
					166 181 167 
					169 183 168 
					183 169 170 
					185 170 171 
					186 170 185 
					186 183 170 
					185 171 187 
					187 171 173 
					173 172 174 
					187 173 174 
					175 188 174 
					188 187 174 
					188 175 176 
					188 176 189 
					176 178 189 
					179 190 177 
					178 177 191 
					191 177 190 
					189 178 191 
					190 179 180 
					180 182 190 
					181 192 182 
					181 184 192 
					191 182 192 
					191 190 182 
					183 192 184 
					186 192 183 
					187 188 185 
					185 189 193 
					185 188 189 
					185 193 186 
					191 192 186 
					191 186 193 
					191 193 189 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
