<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="166" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="328">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.170017 -0.980730 -0.096250
			 0.334503 -0.934144 -0.124432
			 0.019904 -0.905319  0.424266
			-0.644797 -0.764139 -0.018120
			-0.449339 -0.872011  0.194140
			 0.319268 -0.942251 -0.101150
			-0.610194 -0.784693 -0.109180
			-0.218843 -0.959895 -0.175240
			 0.884025 -0.460301 -0.081377
			 0.773668 -0.633473  0.012242
			-0.386871 -0.559051  0.733344
			-0.387368 -0.727879  0.565808
			 0.466079 -0.620937  0.630244
			-0.851276 -0.505161  0.141918
			-0.803762 -0.554520 -0.215581
			-0.832134 -0.553704 -0.031078
			 0.238314 -0.941999 -0.236314
			 0.744747 -0.665514 -0.049429
			-0.608347 -0.762018 -0.221905
			-0.775764 -0.629371  0.045631
			-0.335397 -0.795951 -0.503955
			 0.981108  0.096110 -0.167901
			 0.891842  0.451921 -0.019637
			 0.999688  0.021832 -0.012123
			 0.949827 -0.033652  0.310961
			-0.629795 -0.331369  0.702533
			 0.423668 -0.421559  0.801744
			 0.391720 -0.094631  0.915205
			-0.073275  0.065736  0.995143
			-0.324799  0.102274  0.940237
			-0.735702 -0.585800  0.339973
			-0.740154 -0.666677 -0.087836
			-0.775531 -0.628499  0.059498
			 0.130153 -0.719444 -0.682246
			 0.627605 -0.482716 -0.610817
			-0.690325 -0.543741 -0.477281
			-0.776409 -0.630212  0.004696
			-0.200876 -0.286011 -0.936935
			 0.843533  0.535631 -0.039382
			 0.762556  0.643950  0.061946
			 0.524716  0.843502  0.114789
			 0.567712  0.823019 -0.018526
			 0.854193 -0.005358 -0.519928
			 0.807625  0.365251 -0.462962
			 0.779846  0.482015  0.399376
			-0.437796 -0.159392  0.884833
			-0.465039  0.202459  0.861829
			 0.422719  0.642550  0.639092
			 0.033662  0.869744  0.492354
			-0.111697  0.616626  0.779292
			-0.163278 -0.462574  0.871416
			-0.580934 -0.757339  0.298251
			-0.579048 -0.693324 -0.428958
			-0.776806 -0.524385 -0.348703
			 0.219954  0.014930 -0.975396
			-0.659532 -0.246144 -0.710233
			-0.843783 -0.423605 -0.329529
			-0.413133  0.048098 -0.909399
			 0.658840  0.628486 -0.413442
			 0.417886  0.837415 -0.352288
			 0.357731  0.933531  0.023399
			 0.379608  0.897394  0.224903
			 0.348678  0.443696 -0.825565
			 0.467573  0.330862  0.819698
			-0.202619 -0.739551  0.641880
			 0.315515 -0.082830  0.945299
			 0.436760 -0.032622  0.898986
			-0.281706 -0.396249 -0.873858
			-0.234075 -0.124005 -0.964278
			-0.473540 -0.853496 -0.217495
			-0.474495 -0.347885 -0.808597
			-0.418169 -0.338806 -0.842820
			-0.778396 -0.281617 -0.561063
			-0.638232 -0.586530 -0.498641
			-0.085486  0.459789 -0.883904
			-0.586324 -0.179150 -0.790018
			-0.181906  0.414258 -0.891796
			 0.056871  0.874482 -0.481713
			 0.486638  0.865699 -0.117253
			 0.194004  0.864030 -0.464559
			 0.647895  0.744507  0.161062
			 0.674156  0.516220  0.528233
			-0.425985 -0.846970  0.318086
			-0.399786 -0.634040  0.661940
			 0.044241 -0.484469  0.873689
			 0.419743 -0.223994  0.879570
			 0.792075  0.529198  0.304247
			 0.863685  0.269480  0.425945
			 0.052548  0.085150 -0.994981
			 0.029828 -0.046870 -0.998456
			 0.351465  0.595425 -0.722455
			 0.329240  0.353791 -0.875462
			-0.635466 -0.692630 -0.341242
			-0.533166 -0.540940 -0.650475
			-0.769918 -0.630221 -0.100240
			-0.614726 -0.710647  0.342188
			-0.502804 -0.305059 -0.808781
			-0.483449  0.272154 -0.831991
			-0.759810  0.417690 -0.498221
			-0.689571 -0.247982 -0.680439
			 0.690507  0.714444 -0.113002
			-0.463147  0.044785  0.885149
			-0.790725 -0.022540  0.611757
			 0.129541 -0.227320  0.965166
			 0.406298 -0.166614  0.898422
			 0.508454 -0.264795  0.819365
			 0.830371  0.551033 -0.082739
			 0.849277  0.477701  0.224792
			 0.748170  0.660053 -0.067625
			 0.698897  0.129469  0.703407
			 0.253974  0.468487 -0.846178
			 0.596830  0.453904 -0.661638
			-0.568705  0.603197 -0.559221
			-0.765324 -0.464326 -0.445736
			-0.576870  0.815776  0.041605
			-0.914792  0.086560 -0.394542
			-0.912582 -0.099799  0.396527
			-0.718268  0.670985  0.184038
			-0.751718  0.260546  0.605834
			-0.666868  0.448087  0.595403
			-0.656717  0.733425  0.175529
			-0.258602  0.942844  0.210166
			-0.799863  0.528112  0.285160
			-0.030510  0.266384  0.963384
			-0.407594  0.535213  0.739875
			-0.725793  0.595040  0.345183
			-0.651948  0.752481  0.093468
			 0.343179  0.098634  0.934077
			 0.222193  0.308293  0.924979
			 0.515876  0.469206  0.716741
			 0.634552  0.663681 -0.396070
			 0.563899  0.732494  0.381407
			-0.087412  0.990820 -0.103124
			-0.661309  0.675705  0.325721
			-0.464992  0.880043  0.096471
			-0.499500  0.855385 -0.137173
			-0.841280  0.381907  0.382617
			-0.100070  0.980358  0.169956
			-0.290783  0.879969  0.375634
			-0.187537  0.946217  0.263633
			-0.378490  0.875998  0.298952
			-0.377038  0.921785  0.090304
			-0.749596  0.612073  0.251938
			-0.229829  0.719754  0.655082
			-0.828530  0.558745 -0.036637
			-0.630433  0.775816 -0.025780
			-0.577386  0.816430  0.008283
			-0.280848  0.870089  0.405054
			-0.943647  0.297422  0.145157
			-0.951808  0.301688  0.055184
			-0.923748  0.373742  0.083710
			-0.893796  0.020345  0.448011
			-0.986240  0.106511  0.126438
			-0.902374  0.279153  0.328321
			-0.966221  0.167980  0.195448
			-0.211047  0.976934 -0.032553
			-0.514639  0.852337  0.093099
			-0.486005  0.867739  0.104062
			-0.143357  0.987264 -0.068986
			-0.886937  0.425664 -0.179310
			-0.816852  0.558308  0.145068
			-0.710571  0.685168 -0.160103
			-0.828421  0.351592  0.436006
			-0.642644  0.658961  0.390870
			-0.930055  0.261580  0.258017
			-0.636442  0.767524  0.076470
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.043186 -0.168442 -0.007580
			 0.043861 -0.168407 -0.007553
			 0.043568 -0.168348 -0.006173
			 0.042408 -0.168103 -0.007511
			 0.042765 -0.168285 -0.006733
			 0.043741 -0.167742 -0.010287
			 0.041967 -0.167377 -0.010034
			 0.042905 -0.167701 -0.011203
			 0.045985 -0.166186 -0.009799
			 0.046399 -0.167175 -0.006632
			 0.039945 -0.165271 -0.005240
			 0.042797 -0.168046 -0.006157
			 0.045845 -0.167279 -0.005538
			 0.039326 -0.164689 -0.005595
			 0.039872 -0.165339 -0.008365
			 0.041035 -0.166294 -0.009924
			 0.044298 -0.167423 -0.013359
			 0.046016 -0.166641 -0.011844
			 0.042253 -0.167172 -0.012169
			 0.040312 -0.165513 -0.010645
			 0.043043 -0.167153 -0.013804
			 0.046705 -0.164802 -0.007601
			 0.045886 -0.163840 -0.009339
			 0.046675 -0.164450 -0.011684
			 0.046779 -0.165128 -0.005505
			 0.039250 -0.163128 -0.004550
			 0.046146 -0.165993 -0.004749
			 0.045491 -0.164410 -0.003926
			 0.042037 -0.162408 -0.003432
			 0.040368 -0.162338 -0.003876
			 0.038903 -0.163872 -0.006054
			 0.039782 -0.164694 -0.009525
			 0.040684 -0.165324 -0.009812
			 0.044203 -0.167191 -0.013929
			 0.045594 -0.166201 -0.014118
			 0.041743 -0.166026 -0.013556
			 0.039856 -0.165001 -0.010565
			 0.043521 -0.166176 -0.014496
			 0.046191 -0.163236 -0.006804
			 0.045788 -0.162443 -0.011350
			 0.043850 -0.161640 -0.008783
			 0.045308 -0.162476 -0.006615
			 0.046203 -0.164426 -0.014091
			 0.045938 -0.163407 -0.014027
			 0.046038 -0.163510 -0.004655
			 0.038779 -0.162457 -0.004688
			 0.039481 -0.162207 -0.004253
			 0.044023 -0.161915 -0.003931
			 0.041170 -0.160966 -0.004357
			 0.039655 -0.161192 -0.004933
			 0.036971 -0.161004 -0.004753
			 0.036227 -0.162016 -0.006368
			 0.027925 -0.154750 -0.009038
			 0.038788 -0.162870 -0.011451
			 0.045463 -0.164090 -0.014680
			 0.039701 -0.163489 -0.013046
			 0.039119 -0.163221 -0.012271
			 0.040712 -0.162409 -0.013869
			 0.045442 -0.162419 -0.013918
			 0.044338 -0.161609 -0.013722
			 0.043295 -0.160840 -0.011106
			 0.039865 -0.160507 -0.006383
			 0.044670 -0.162584 -0.014605
			 0.038122 -0.159907 -0.004530
			 0.030532 -0.154923 -0.001563
			 0.035767 -0.158466 -0.003674
			 0.033841 -0.156665 -0.002812
			 0.028539 -0.153517 -0.010445
			 0.031107 -0.155700 -0.010559
			 0.023092 -0.152452 -0.007671
			 0.037320 -0.160774 -0.012072
			 0.023913 -0.149200 -0.011133
			 0.038947 -0.162348 -0.012404
			 0.038515 -0.162034 -0.011988
			 0.043075 -0.161610 -0.014279
			 0.038839 -0.161613 -0.012675
			 0.038531 -0.160739 -0.012164
			 0.041866 -0.160571 -0.013129
			 0.039535 -0.159937 -0.010290
			 0.038797 -0.160037 -0.011669
			 0.038140 -0.159062 -0.006956
			 0.036063 -0.157851 -0.003978
			 0.023635 -0.152353 -0.001171
			 0.023802 -0.150818  0.000897
			 0.025432 -0.149846  0.002355
			 0.030798 -0.153376 -0.000970
			 0.034176 -0.155821 -0.003443
			 0.030495 -0.150962 -0.000992
			 0.026231 -0.149512 -0.011366
			 0.028341 -0.151230 -0.011022
			 0.037371 -0.159040 -0.011475
			 0.031888 -0.154080 -0.010902
			 0.020899 -0.151148 -0.007389
			 0.021515 -0.150114 -0.009129
			 0.019501 -0.150152 -0.004908
			 0.020762 -0.150859 -0.002319
			 0.038289 -0.161686 -0.012315
			 0.022513 -0.147527 -0.010833
			 0.020029 -0.148670 -0.008361
			 0.020499 -0.149223 -0.008757
			 0.032305 -0.153652 -0.008690
			 0.024746 -0.148491  0.002802
			 0.022883 -0.150008  0.000932
			 0.025965 -0.148807  0.002740
			 0.027286 -0.148830  0.002423
			 0.028055 -0.150081  0.001547
			 0.030883 -0.151467 -0.004581
			 0.028937 -0.147030 -0.000169
			 0.027450 -0.145199 -0.002495
			 0.028506 -0.148265  0.001688
			 0.026236 -0.146841 -0.010665
			 0.028890 -0.150812 -0.010763
			 0.023205 -0.146161 -0.010324
			 0.020055 -0.149645 -0.007938
			 0.019239 -0.148816 -0.006628
			 0.019550 -0.148867 -0.007557
			 0.019125 -0.149594 -0.003601
			 0.018901 -0.149101 -0.004690
			 0.019979 -0.150005 -0.002338
			 0.021876 -0.150009 -0.000235
			 0.022792 -0.146771 -0.010367
			 0.021184 -0.148272 -0.008192
			 0.023614 -0.148748  0.001390
			 0.025680 -0.148133  0.002929
			 0.025354 -0.147623  0.002484
			 0.025181 -0.147293  0.002077
			 0.023618 -0.148674 -0.001002
			 0.026858 -0.148309  0.002684
			 0.026461 -0.147788  0.002636
			 0.027483 -0.146822  0.001730
			 0.027373 -0.146274 -0.009387
			 0.027971 -0.145682 -0.000157
			 0.025504 -0.144243 -0.006230
			 0.026306 -0.144646 -0.000572
			 0.025660 -0.143863 -0.004102
			 0.024090 -0.144947 -0.009431
			 0.024722 -0.146158 -0.007465
			 0.022596 -0.149224 -0.005473
			 0.022549 -0.148883 -0.006564
			 0.020464 -0.149568 -0.002529
			 0.021252 -0.149615 -0.001503
			 0.022599 -0.149214 -0.001139
			 0.024582 -0.147655 -0.005654
			 0.025783 -0.145796  0.001432
			 0.025291 -0.146798 -0.000110
			 0.024456 -0.148300 -0.002585
			 0.025014 -0.144391 -0.008349
			 0.026250 -0.145433  0.000739
			 0.024882 -0.144889 -0.007643
			 0.025111 -0.144577 -0.005765
			 0.025838 -0.146539 -0.002619
			 0.025593 -0.146352 -0.003475
			 0.025232 -0.146034 -0.004183
			 0.024837 -0.144863 -0.008484
			 0.025282 -0.146109 -0.006324
			 0.021613 -0.149672 -0.002796
			 0.023162 -0.149059 -0.005333
			 0.022980 -0.149120 -0.004172
			 0.021834 -0.149471 -0.001833
			 0.025191 -0.146993 -0.004739
			 0.024616 -0.147539 -0.004337
			 0.024303 -0.148268 -0.004766
			 0.025325 -0.147020 -0.003864
			 0.024449 -0.148209 -0.003853
			 0.024969 -0.146728 -0.004186
			 0.024039 -0.148143 -0.004242
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 0 4 
					0 5 1 
					2 4 0 
					6 7 0 
					7 5 0 
					0 3 6 
					5 8 1 
					9 2 1 
					1 8 9 
					2 10 11 
					2 12 10 
					2 11 4 
					9 12 2 
					10 13 3 
					4 10 3 
					3 14 15 
					3 13 14 
					3 15 6 
					4 11 10 
					7 16 5 
					8 5 17 
					17 5 16 
					18 7 6 
					19 18 6 
					6 15 19 
					16 7 20 
					7 18 20 
					21 8 22 
					8 23 22 
					9 8 21 
					17 23 8 
					24 12 9 
					21 24 9 
					13 10 25 
					12 26 10 
					26 27 10 
					27 28 10 
					28 29 10 
					29 25 10 
					24 26 12 
					25 30 13 
					13 30 31 
					13 31 14 
					14 32 15 
					14 31 32 
					15 32 19 
					20 33 16 
					33 34 16 
					17 16 34 
					23 17 34 
					20 18 35 
					35 18 19 
					35 19 36 
					36 19 32 
					37 20 35 
					37 33 20 
					38 21 22 
					21 38 24 
					23 39 22 
					22 39 40 
					38 22 41 
					22 40 41 
					42 43 23 
					23 43 39 
					42 23 34 
					44 24 38 
					26 24 27 
					24 44 27 
					25 45 30 
					45 25 46 
					25 29 46 
					27 44 47 
					28 27 47 
					48 29 28 
					47 48 28 
					29 49 46 
					49 29 48 
					50 30 45 
					51 30 50 
					30 51 31 
					31 51 52 
					52 53 31 
					36 32 31 
					31 53 36 
					33 37 34 
					37 54 34 
					54 42 34 
					35 55 37 
					56 55 35 
					56 35 36 
					53 56 36 
					37 55 57 
					54 37 57 
					41 44 38 
					39 43 58 
					58 59 39 
					59 60 39 
					60 40 39 
					61 40 60 
					61 48 40 
					41 40 48 
					47 44 41 
					47 41 48 
					42 54 43 
					62 43 54 
					58 43 62 
					49 63 45 
					46 49 45 
					50 45 63 
					49 48 61 
					49 61 63 
					64 51 50 
					65 66 50 
					66 64 50 
					63 65 50 
					52 51 64 
					67 68 52 
					69 52 64 
					68 70 52 
					53 52 70 
					52 69 71 
					67 52 71 
					72 56 53 
					70 73 53 
					53 73 72 
					54 74 62 
					54 57 74 
					57 55 72 
					55 56 72 
					72 75 57 
					76 57 75 
					74 57 76 
					62 59 58 
					77 59 74 
					62 74 59 
					59 77 60 
					78 60 79 
					60 78 61 
					77 79 60 
					80 61 78 
					80 63 61 
					63 80 81 
					81 65 63 
					64 82 69 
					83 82 64 
					84 83 64 
					64 85 84 
					66 85 64 
					65 81 66 
					86 66 81 
					85 66 87 
					86 87 66 
					67 71 88 
					67 88 89 
					67 89 68 
					68 90 76 
					68 76 70 
					68 89 91 
					91 90 68 
					69 92 93 
					93 71 69 
					92 69 94 
					69 95 94 
					95 69 82 
					70 76 96 
					70 96 73 
					97 71 98 
					98 71 99 
					71 93 99 
					71 97 88 
					73 75 72 
					73 96 75 
					77 74 76 
					76 75 96 
					90 79 76 
					79 77 76 
					79 90 78 
					90 100 78 
					100 80 78 
					81 80 100 
					81 100 86 
					95 82 83 
					101 102 83 
					83 102 95 
					83 84 101 
					103 101 84 
					104 84 105 
					84 85 105 
					84 104 103 
					87 105 85 
					100 106 86 
					87 86 106 
					107 87 108 
					87 106 108 
					109 87 107 
					87 109 105 
					88 110 111 
					112 88 97 
					111 89 88 
					112 110 88 
					111 91 89 
					90 91 100 
					111 100 91 
					113 93 92 
					113 92 94 
					93 113 99 
					94 114 115 
					94 95 116 
					94 117 114 
					115 113 94 
					116 117 94 
					95 118 116 
					102 119 95 
					119 118 95 
					97 120 112 
					98 120 97 
					121 98 115 
					115 98 99 
					98 121 120 
					99 113 115 
					100 111 106 
					102 101 122 
					101 123 124 
					125 122 101 
					125 101 124 
					123 101 103 
					126 102 122 
					119 102 126 
					103 127 128 
					104 127 103 
					128 123 103 
					104 105 109 
					109 129 104 
					127 104 129 
					106 111 130 
					130 108 106 
					107 108 131 
					107 129 109 
					107 131 129 
					130 132 108 
					108 133 131 
					134 108 132 
					108 134 133 
					110 130 111 
					135 130 110 
					135 110 112 
					136 112 120 
					136 135 112 
					121 115 114 
					114 117 137 
					121 114 138 
					114 137 138 
					116 139 117 
					116 118 139 
					139 137 117 
					119 140 118 
					118 140 139 
					126 141 119 
					141 140 119 
					120 121 138 
					142 120 138 
					142 136 120 
					126 122 125 
					123 128 143 
					123 143 124 
					143 125 124 
					144 126 125 
					144 125 143 
					141 126 145 
					145 126 144 
					128 127 129 
					128 129 143 
					129 131 143 
					130 135 146 
					130 146 132 
					133 147 131 
					147 143 131 
					132 146 148 
					148 149 132 
					134 132 149 
					150 133 151 
					144 133 150 
					133 144 147 
					134 152 133 
					151 133 152 
					134 149 152 
					146 135 153 
					153 135 136 
					148 153 136 
					142 154 136 
					148 136 154 
					155 137 139 
					138 137 156 
					137 157 156 
					137 155 157 
					156 142 138 
					139 140 158 
					158 155 139 
					141 158 140 
					155 158 141 
					155 141 145 
					159 142 160 
					142 156 161 
					154 142 159 
					142 161 160 
					143 147 144 
					150 145 144 
					145 150 162 
					155 145 157 
					157 145 163 
					162 163 145 
					153 148 146 
					148 154 149 
					154 152 149 
					150 151 162 
					151 152 164 
					162 151 164 
					152 154 159 
					159 164 152 
					165 161 156 
					156 157 165 
					163 165 157 
					164 159 160 
					161 165 160 
					165 163 160 
					163 162 160 
					162 164 160 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
