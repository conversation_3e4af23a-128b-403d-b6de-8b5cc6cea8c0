<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="159" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="316">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.137584 -0.680260 -0.719942
			-0.332368 -0.831904 -0.444374
			-0.260028 -0.438535 -0.860275
			 0.254401 -0.088771 -0.963016
			-0.023687 -0.927680 -0.372625
			 0.571309 -0.209151 -0.793638
			-0.222066 -0.475620 -0.851159
			-0.184031 -0.807404 -0.560564
			-0.278064 -0.959641 -0.042073
			 0.005004 -0.987266 -0.158997
			 0.157900  0.201653 -0.966646
			-0.131622  0.147208 -0.980309
			 0.641886  0.635647 -0.428876
			 0.977518 -0.035255 -0.207883
			 0.728188 -0.684818  0.027693
			 0.869129  0.468244 -0.159253
			-0.069069 -0.340197 -0.937814
			-0.096958  0.363920 -0.926370
			 0.043951 -0.568500 -0.821509
			-0.031146 -0.590660 -0.806319
			-0.101811 -0.859636 -0.500660
			-0.365751 -0.929273  0.051750
			-0.222437 -0.947076 -0.231451
			 0.113316 -0.993424  0.016404
			-0.339484 -0.793477  0.505119
			-0.289637 -0.669699  0.683823
			-0.128578 -0.874206  0.468222
			 0.894641 -0.403902  0.190997
			 0.320821  0.746286 -0.583208
			 0.063455  0.964792 -0.255245
			 0.519243  0.792775  0.319209
			 0.765791  0.631002  0.124097
			 0.931278  0.333442  0.146758
			 0.213235 -0.008264 -0.976966
			 0.083815  0.649812 -0.755460
			-0.042754  0.976579 -0.210870
			 0.149857 -0.792749 -0.590840
			 0.365481 -0.228200 -0.902412
			 0.430512  0.000216 -0.902585
			-0.264343 -0.953420  0.145306
			-0.269988 -0.755789  0.596565
			-0.238489 -0.932204 -0.272246
			 0.624948 -0.546439  0.557534
			-0.402244 -0.592432  0.698014
			-0.246779 -0.525466  0.814239
			 0.330122 -0.214439  0.919258
			 0.258789 -0.027976  0.965529
			 0.001034  0.004832  0.999988
			-0.347469 -0.277703  0.895626
			 0.794536  0.443584  0.414663
			 0.905884  0.343803  0.247333
			 0.225247  0.965682  0.129314
			 0.078789  0.994378  0.070745
			 0.405692  0.481988  0.776596
			 0.243231  0.655484  0.714968
			 0.093839  0.991336 -0.091908
			 0.293253  0.955371  0.035626
			 0.475742  0.714640 -0.512796
			 0.641773  0.540551 -0.543996
			 0.345237  0.935128  0.079673
			 0.521944 -0.375060 -0.766097
			-0.185996 -0.673822 -0.715101
			 0.445245 -0.504498 -0.739756
			 0.714457  0.520156 -0.467963
			 0.667175  0.204835 -0.716185
			-0.137241 -0.837392  0.529092
			-0.086160 -0.846809  0.524873
			-0.651893 -0.730758  0.202552
			 0.044626 -0.199452  0.978891
			-0.005188 -0.540976  0.841022
			-0.911391  0.123677 -0.392517
			-0.863403  0.091850 -0.496083
			-0.920378 -0.041218 -0.388852
			-0.934502  0.329037 -0.135796
			-0.321231  0.196706  0.926346
			-0.501111 -0.304230  0.810143
			-0.145035  0.032031  0.988908
			-0.277200  0.427470  0.860482
			 0.114586  0.991891 -0.054974
			-0.024054  0.995597  0.090600
			 0.618183  0.786030 -0.002412
			 0.214875  0.963514  0.159591
			 0.504152  0.805860  0.310517
			 0.488828  0.735307  0.469437
			 0.612142 -0.075312 -0.787153
			-0.134266 -0.205426 -0.969419
			-0.709858  0.189350 -0.678416
			 0.113051  0.019739 -0.993393
			 0.410866  0.741748 -0.530093
			 0.416200  0.908626 -0.034297
			 0.534313  0.814137 -0.227356
			 0.281167  0.549967 -0.786436
			 0.139567 -0.690604  0.709639
			-0.770958 -0.203351  0.603550
			-0.896532  0.110217  0.429048
			-0.621636  0.781191 -0.057523
			 0.298644 -0.293567  0.908091
			 0.522874  0.100198  0.846500
			 0.590292  0.392991  0.705063
			 0.131994  0.337629  0.931979
			-0.623074  0.779075 -0.069436
			-0.951816  0.270978 -0.143588
			-0.932246  0.358458 -0.049244
			-0.983453  0.179258 -0.026201
			 0.019831  0.978646  0.204594
			 0.018145  0.767337  0.640988
			 0.643165  0.671438  0.368118
			 0.370646  0.682546  0.629883
			 0.206250  0.217612 -0.953995
			-0.649493  0.269958 -0.710831
			-0.744034  0.277970 -0.607575
			-0.286711  0.770224 -0.569695
			-0.131989  0.925322 -0.355469
			 0.123443  0.973908  0.190432
			-0.041979  0.995609  0.083668
			-0.245683  0.968324  0.044604
			 0.261171  0.913640  0.311531
			-0.121653  0.978531 -0.166364
			 0.138806 -0.595171  0.791520
			 0.499357 -0.511135  0.699559
			-0.525454 -0.123128  0.841865
			-0.842542  0.536703 -0.045531
			-0.852205  0.506782 -0.130073
			-0.831711  0.420405  0.362652
			-0.797302  0.588412 -0.134465
			 0.544291 -0.131315  0.828555
			 0.566953  0.272406  0.777405
			-0.896476  0.441109  0.041879
			-0.994134 -0.017797  0.106683
			-0.989679 -0.094962 -0.107318
			-0.990898  0.016966 -0.133543
			-0.967900  0.250638  0.018689
			-0.995475 -0.092035  0.023639
			 0.257288  0.605612  0.753019
			-0.812059  0.492432 -0.313164
			-0.982030  0.154040 -0.109040
			-0.920297  0.297546 -0.254008
			-0.316065  0.916422  0.245506
			 0.207768  0.858700  0.468472
			 0.025532  0.879211  0.475749
			-0.984085  0.177638 -0.004588
			-0.997834  0.063554 -0.016996
			-0.728777  0.675099  0.114571
			 0.547934 -0.368901  0.750786
			 0.341227 -0.364178  0.866567
			-0.040730  0.069976  0.996717
			-0.941912  0.182155 -0.282173
			-0.845576  0.437900 -0.305360
			-0.900578  0.415776 -0.126845
			-0.954798  0.277067 -0.107679
			-0.469928  0.636239  0.611856
			-0.963900  0.264831  0.027586
			-0.959859  0.270991  0.072344
			 0.434099  0.037949  0.900065
			-0.997964  0.046600  0.043557
			-0.944672  0.310986  0.104319
			-0.920768 -0.344540 -0.182971
			-0.957429 -0.266193  0.111678
			-0.998809 -0.033925  0.035061
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.047332 -0.137461  0.047591
			 0.046574 -0.137410  0.048256
			 0.045478 -0.135995  0.047091
			 0.047736 -0.136511  0.047157
			 0.047778 -0.138007  0.048268
			 0.048838 -0.137386  0.047819
			 0.044990 -0.135838  0.047248
			 0.041890 -0.136109  0.048180
			 0.044300 -0.137533  0.049516
			 0.047843 -0.138106  0.049663
			 0.046315 -0.135589  0.046955
			 0.045107 -0.135242  0.047069
			 0.048195 -0.136295  0.047588
			 0.049128 -0.137643  0.048199
			 0.049182 -0.137836  0.049069
			 0.048822 -0.136855  0.048246
			 0.042775 -0.135368  0.047268
			 0.044082 -0.135108  0.047173
			 0.039359 -0.134254  0.046548
			 0.042070 -0.135472  0.047419
			 0.038552 -0.134723  0.047148
			 0.038719 -0.135358  0.049182
			 0.037789 -0.134715  0.047522
			 0.047820 -0.138323  0.050425
			 0.042746 -0.136686  0.050292
			 0.045061 -0.136642  0.052010
			 0.047278 -0.138138  0.050974
			 0.048863 -0.137831  0.050144
			 0.046023 -0.134823  0.047212
			 0.045220 -0.134492  0.047530
			 0.047413 -0.136191  0.051508
			 0.048571 -0.136550  0.048598
			 0.048898 -0.137325  0.049804
			 0.041833 -0.135017  0.047221
			 0.043083 -0.134947  0.047297
			 0.044150 -0.134666  0.047851
			 0.037385 -0.134359  0.046495
			 0.039413 -0.133281  0.046109
			 0.040528 -0.133916  0.046558
			 0.036832 -0.134491  0.049727
			 0.039575 -0.135068  0.050728
			 0.035580 -0.134152  0.045759
			 0.048632 -0.137808  0.051368
			 0.042796 -0.135489  0.051437
			 0.041087 -0.135468  0.051002
			 0.048257 -0.137593  0.051656
			 0.046615 -0.136603  0.052256
			 0.045403 -0.135948  0.052423
			 0.043234 -0.135298  0.052047
			 0.048324 -0.136997  0.051285
			 0.048761 -0.137228  0.050334
			 0.045489 -0.134974  0.049589
			 0.044719 -0.134925  0.049546
			 0.046341 -0.135826  0.052119
			 0.044845 -0.134992  0.052147
			 0.044713 -0.134851  0.050301
			 0.044168 -0.134567  0.051519
			 0.042222 -0.134214  0.047717
			 0.041464 -0.133716  0.047445
			 0.043482 -0.134536  0.049355
			 0.038138 -0.132566  0.045237
			 0.035960 -0.133357  0.044354
			 0.036585 -0.133004  0.044259
			 0.039748 -0.132429  0.046504
			 0.038235 -0.131478  0.045159
			 0.036853 -0.134159  0.050903
			 0.036309 -0.134050  0.051043
			 0.033491 -0.133732  0.050058
			 0.039991 -0.134370  0.051236
			 0.037181 -0.133361  0.051639
			 0.035260 -0.133493  0.044862
			 0.034514 -0.133458  0.045813
			 0.034179 -0.133810  0.046565
			 0.033486 -0.133715  0.048311
			 0.042139 -0.134957  0.051515
			 0.042729 -0.134933  0.051864
			 0.041306 -0.134850  0.051337
			 0.043136 -0.134575  0.052079
			 0.044206 -0.134909  0.050339
			 0.043651 -0.134509  0.051894
			 0.040692 -0.132971  0.048702
			 0.042597 -0.134530  0.050523
			 0.041904 -0.133881  0.049764
			 0.041794 -0.134128  0.050352
			 0.037451 -0.132071  0.044501
			 0.036056 -0.132757  0.043935
			 0.035769 -0.132367  0.044222
			 0.036721 -0.132301  0.044056
			 0.037255 -0.130289  0.045148
			 0.037414 -0.129771  0.049893
			 0.037804 -0.130251  0.045891
			 0.037164 -0.130703  0.044656
			 0.036150 -0.133589  0.051581
			 0.034660 -0.132858  0.052242
			 0.034383 -0.133165  0.051813
			 0.034215 -0.133315  0.050977
			 0.038260 -0.132032  0.051991
			 0.038655 -0.131563  0.051880
			 0.039921 -0.133006  0.051394
			 0.040698 -0.134314  0.051120
			 0.035236 -0.132770  0.048525
			 0.035957 -0.131811  0.044723
			 0.035829 -0.132079  0.047448
			 0.035853 -0.131519  0.045782
			 0.042400 -0.134439  0.051053
			 0.041718 -0.134514  0.051323
			 0.037986 -0.130026  0.051477
			 0.041288 -0.134251  0.050877
			 0.036807 -0.131599  0.044032
			 0.036045 -0.131810  0.044217
			 0.036052 -0.131292  0.044368
			 0.036060 -0.130308  0.045242
			 0.036759 -0.129839  0.045953
			 0.036914 -0.129770  0.051396
			 0.036552 -0.129552  0.050606
			 0.036033 -0.129555  0.049827
			 0.037487 -0.129792  0.051450
			 0.036416 -0.129580  0.046651
			 0.036307 -0.132497  0.052912
			 0.037456 -0.132117  0.052413
			 0.035155 -0.131835  0.052905
			 0.034646 -0.132631  0.051282
			 0.034838 -0.132247  0.051578
			 0.035336 -0.131357  0.052904
			 0.035545 -0.132485  0.049115
			 0.037867 -0.131635  0.052297
			 0.037750 -0.130780  0.052348
			 0.035629 -0.132282  0.048350
			 0.036073 -0.130777  0.046477
			 0.036110 -0.131246  0.047499
			 0.035938 -0.131264  0.048220
			 0.035756 -0.131638  0.048560
			 0.035882 -0.130961  0.045504
			 0.037263 -0.130464  0.052496
			 0.035779 -0.130191  0.045825
			 0.035734 -0.129968  0.046433
			 0.035589 -0.129882  0.047593
			 0.036041 -0.129700  0.051292
			 0.037152 -0.129991  0.051975
			 0.036651 -0.129827  0.051871
			 0.035519 -0.129971  0.050650
			 0.035446 -0.129884  0.048539
			 0.035330 -0.129978  0.050151
			 0.037159 -0.131869  0.052802
			 0.036562 -0.132087  0.053078
			 0.036164 -0.131792  0.053236
			 0.035919 -0.131254  0.050584
			 0.035500 -0.131309  0.051498
			 0.035308 -0.131404  0.051984
			 0.035464 -0.130926  0.051968
			 0.035702 -0.130576  0.052577
			 0.035865 -0.131645  0.049798
			 0.035786 -0.132065  0.048838
			 0.036815 -0.131625  0.053081
			 0.035942 -0.131277  0.048915
			 0.035526 -0.130305  0.051730
			 0.035881 -0.130643  0.050332
			 0.035888 -0.130678  0.049510
			 0.036066 -0.131027  0.049937
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 2 3 
					1 0 4 
					0 3 5 
					5 4 0 
					6 2 1 
					6 1 7 
					8 1 9 
					9 1 4 
					8 7 1 
					3 2 10 
					2 6 11 
					10 2 11 
					5 3 12 
					12 3 10 
					13 4 5 
					14 4 13 
					14 9 4 
					15 5 12 
					15 13 5 
					7 16 6 
					11 6 17 
					6 16 17 
					7 18 19 
					7 19 16 
					18 7 20 
					7 8 21 
					22 20 7 
					7 21 22 
					8 9 23 
					24 21 8 
					25 8 26 
					26 8 23 
					8 25 24 
					9 27 23 
					14 27 9 
					10 11 28 
					28 12 10 
					11 29 28 
					29 11 17 
					12 30 31 
					15 12 31 
					30 12 28 
					32 14 13 
					13 31 32 
					15 31 13 
					32 27 14 
					33 16 19 
					17 16 34 
					33 34 16 
					34 35 17 
					35 29 17 
					33 19 18 
					18 36 37 
					18 20 36 
					38 33 18 
					38 18 37 
					22 36 20 
					39 22 21 
					40 21 24 
					40 39 21 
					22 41 36 
					22 39 41 
					23 42 26 
					42 23 27 
					43 24 25 
					40 24 44 
					24 43 44 
					26 45 25 
					25 45 46 
					46 47 25 
					47 48 25 
					25 48 43 
					42 45 26 
					27 49 42 
					50 49 27 
					32 50 27 
					28 29 30 
					30 29 51 
					35 52 29 
					29 52 51 
					31 30 49 
					53 30 54 
					30 51 55 
					54 30 56 
					56 30 55 
					45 30 46 
					49 30 45 
					53 46 30 
					50 32 31 
					49 50 31 
					57 34 33 
					58 33 38 
					33 58 57 
					35 34 59 
					57 59 34 
					59 52 35 
					60 37 36 
					36 41 61 
					61 62 36 
					60 36 62 
					63 38 37 
					63 37 64 
					64 37 60 
					38 63 58 
					39 65 66 
					40 65 39 
					66 67 39 
					39 67 41 
					68 40 44 
					40 68 69 
					69 65 40 
					70 61 41 
					71 41 72 
					71 70 41 
					41 67 73 
					72 41 73 
					49 45 42 
					74 44 43 
					43 48 75 
					74 43 75 
					76 44 74 
					68 44 76 
					53 47 46 
					54 47 53 
					47 54 48 
					54 77 48 
					75 48 77 
					55 51 52 
					78 55 52 
					59 78 52 
					54 79 77 
					56 79 54 
					78 79 55 
					79 56 55 
					57 58 80 
					59 57 80 
					58 63 80 
					78 59 81 
					59 82 83 
					81 59 83 
					80 82 59 
					60 62 84 
					64 60 84 
					85 62 61 
					86 61 70 
					86 85 61 
					62 85 87 
					62 87 84 
					64 88 63 
					80 63 89 
					89 63 90 
					63 88 90 
					88 64 91 
					84 91 64 
					66 65 69 
					92 66 69 
					92 93 66 
					67 66 94 
					66 93 94 
					67 94 95 
					73 67 95 
					68 96 69 
					97 96 68 
					98 68 99 
					68 98 97 
					99 68 76 
					96 92 69 
					100 70 71 
					101 86 70 
					102 70 100 
					101 70 103 
					102 103 70 
					100 71 72 
					100 72 73 
					73 95 100 
					79 74 77 
					74 75 77 
					79 104 74 
					74 105 76 
					105 74 104 
					99 76 105 
					104 78 81 
					79 78 104 
					80 106 98 
					89 106 80 
					82 80 98 
					104 81 105 
					107 105 81 
					81 83 107 
					82 98 83 
					83 98 107 
					108 84 87 
					91 84 108 
					85 86 87 
					87 86 109 
					101 109 86 
					110 108 87 
					87 109 110 
					111 88 91 
					90 88 112 
					88 111 112 
					113 89 114 
					114 89 115 
					89 113 116 
					89 116 106 
					115 89 117 
					89 90 117 
					117 90 112 
					91 110 111 
					110 91 108 
					93 92 118 
					92 96 119 
					119 118 92 
					118 120 93 
					94 93 121 
					122 121 93 
					93 120 123 
					122 93 123 
					121 95 94 
					124 100 95 
					124 95 121 
					119 96 125 
					125 96 97 
					125 97 126 
					97 106 126 
					98 106 97 
					99 107 98 
					105 107 99 
					127 102 100 
					127 100 124 
					103 110 101 
					101 110 109 
					103 102 128 
					129 102 130 
					129 128 102 
					127 131 102 
					131 130 102 
					110 103 132 
					128 132 103 
					126 106 133 
					116 133 106 
					134 111 110 
					110 132 134 
					134 112 111 
					134 111 135 
					111 134 135 
					117 112 136 
					135 112 134 
					112 135 136 
					114 137 113 
					113 138 116 
					138 113 139 
					113 137 139 
					140 114 115 
					140 137 114 
					115 141 142 
					140 115 142 
					141 115 117 
					116 138 133 
					136 141 117 
					118 119 143 
					144 118 143 
					145 120 118 
					144 145 118 
					125 143 119 
					120 145 123 
					121 122 124 
					146 122 147 
					148 122 123 
					122 146 124 
					147 122 148 
					149 148 123 
					149 123 150 
					123 145 150 
					124 151 152 
					124 152 127 
					151 124 146 
					126 153 125 
					125 153 143 
					153 126 133 
					131 127 152 
					128 135 132 
					129 135 128 
					129 130 136 
					136 135 129 
					136 130 141 
					141 130 154 
					131 154 130 
					151 131 152 
					131 151 154 
					135 134 132 
					133 139 150 
					133 138 139 
					153 133 145 
					150 145 133 
					137 150 139 
					137 155 150 
					137 140 155 
					146 155 140 
					140 156 146 
					140 142 141 
					157 140 141 
					140 157 156 
					157 141 154 
					144 143 153 
					153 145 144 
					149 146 147 
					149 155 146 
					146 156 158 
					151 146 158 
					147 148 149 
					150 155 149 
					151 158 157 
					157 154 151 
					156 157 158 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
