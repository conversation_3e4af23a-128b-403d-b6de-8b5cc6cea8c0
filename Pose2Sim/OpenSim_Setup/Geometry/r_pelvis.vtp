<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="125" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="242">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.179324  0.266632  0.946969
			-0.354023 -0.104927  0.929332
			-0.267537 -0.600731  0.753357
			-0.637155 -0.471716  0.609523
			-0.761351 -0.588225  0.272647
			 0.537959 -0.600882  0.591220
			 0.836107  0.113477  0.536701
			 0.461437  0.510762  0.725395
			 0.310915  0.215176  0.925760
			 0.010792 -0.549332  0.835535
			 0.114995 -0.573523  0.811078
			 0.360558 -0.870106  0.336025
			 0.558320 -0.701964  0.442183
			-0.148657 -0.834996  0.529795
			 0.911922 -0.377030  0.162007
			 0.800223 -0.592796  0.090749
			 0.670086 -0.253985  0.697478
			 0.384632 -0.107701  0.916765
			 0.066755 -0.198429  0.977839
			-0.242120 -0.455979  0.856423
			 0.051261 -0.506395  0.860777
			-0.435040 -0.819409  0.373243
			-0.210331 -0.977590  0.008925
			-0.442443 -0.802043  0.401211
			-0.334209 -0.676750  0.655983
			 0.104531 -0.986292  0.127675
			-0.011922 -0.828766  0.559468
			-0.231189 -0.273197  0.933764
			-0.389343 -0.745245  0.541315
			-0.692890 -0.244899  0.678180
			-0.502312 -0.218437  0.836641
			 0.275710 -0.832391  0.480739
			 0.204806 -0.147806  0.967578
			 0.714934  0.501517  0.487185
			-0.384453  0.089694  0.918777
			 0.470066  0.780736  0.411691
			-0.639350  0.144805  0.755158
			-0.565430  0.010556  0.824729
			-0.067001  0.869315  0.489696
			-0.266906  0.953897  0.137267
			-0.563369  0.380534  0.733355
			-0.365570 -0.201200  0.908778
			-0.393668  0.039438  0.918407
			-0.613406 -0.494181  0.616050
			-0.863398  0.270420  0.425931
			-0.720859 -0.193629  0.665485
			-0.871537 -0.343231  0.350166
			-0.932626 -0.184444  0.310143
			-0.662757  0.743115 -0.092379
			-0.759573 -0.187786  0.622724
			-0.352530 -0.839149  0.414188
			-0.437299 -0.641299 -0.630480
			 0.472906  0.108858 -0.874362
			 0.407015 -0.117676 -0.905810
			 0.670215  0.259324 -0.695387
			 0.842356  0.215150 -0.494112
			-0.965877  0.142812 -0.216070
			 0.211799  0.103396 -0.971828
			 0.064613 -0.137611 -0.988377
			-0.058610  0.117085 -0.991391
			 0.005120 -0.111795 -0.993718
			 0.730417  0.473330 -0.492392
			 0.609002  0.176478 -0.773287
			 0.664658  0.016412 -0.746968
			 0.527607 -0.708164 -0.469184
			 0.799383  0.002763 -0.600815
			 0.729546  0.431836 -0.530358
			 0.976842  0.213949  0.002350
			 0.746819  0.379468 -0.546136
			 0.523558  0.077652 -0.848444
			 0.313987  0.651395 -0.690722
			 0.028979  0.551892 -0.833412
			 0.322325 -0.128057 -0.937928
			-0.928883 -0.347637 -0.127767
			-0.892838  0.076933 -0.443758
			-0.074899 -0.161026 -0.984104
			-0.639293  0.454950 -0.619939
			 0.051435 -0.379150 -0.923905
			-0.301834  0.901876 -0.309057
			 0.042422  0.286621 -0.957104
			 0.716507  0.551027  0.427770
			-0.603308 -0.122417 -0.788057
			 0.122695  0.849226  0.513576
			-0.174760  0.523640 -0.833823
			 0.442904  0.801604 -0.401581
			 0.692764 -0.301178 -0.655263
			-0.386977 -0.146409 -0.910392
			-0.759849  0.332307 -0.558749
			-0.316785 -0.457139  0.831067
			-0.764908  0.570408 -0.299248
			 0.489285 -0.865157  0.110017
			 0.150003 -0.938292  0.311619
			 0.067810 -0.515173  0.854399
			-0.875424 -0.473345  0.097865
			-0.677614  0.297518  0.672550
			-0.571314  0.439045 -0.693427
			 0.806719 -0.590347 -0.026366
			 0.102875 -0.877018  0.469315
			 0.169056 -0.132224  0.976697
			-0.407576  0.479503  0.777148
			-0.660002  0.551069 -0.510608
			-0.762495  0.574170 -0.298210
			-0.630911  0.166530  0.757773
			 0.008816  0.268014 -0.963375
			-0.270876 -0.652059  0.708128
			-0.836732 -0.204561  0.507971
			 0.004593 -0.267670 -0.963500
			-0.837575  0.175274  0.517443
			-0.522677  0.163736 -0.836659
			-0.524019 -0.588970 -0.615238
			-0.808007 -0.531388 -0.254465
			 0.902474 -0.105892  0.417525
			-0.028317 -0.102829  0.994296
			 0.928279 -0.356060 -0.107331
			-0.531323  0.329204  0.780590
			 0.081692 -0.106371  0.990965
			 0.298445 -0.932280  0.204414
			 0.904981  0.383230 -0.184779
			 0.979260 -0.156690  0.128446
			 0.102989 -0.990187  0.094460
			 0.975002  0.120513  0.186673
			 0.597962  0.712441 -0.367245
			 0.676044 -0.687131  0.266112
			 0.874146 -0.080398  0.478963
			 0.082667  0.501333 -0.861296
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.062448 -0.071901  0.057002
			-0.062417 -0.062953  0.055540
			-0.045919 -0.070017  0.069408
			-0.057682 -0.053884  0.064535
			-0.047413 -0.050859  0.081192
			-0.084750 -0.081351  0.070701
			-0.087329 -0.064967  0.065912
			-0.080483 -0.072644  0.062249
			-0.076977 -0.066999  0.055770
			-0.094784 -0.078564  0.080703
			-0.075725 -0.050801  0.059263
			-0.077690 -0.044275  0.065884
			-0.084509 -0.048300  0.064622
			-0.070202 -0.044831  0.065144
			-0.089862 -0.054217  0.072831
			-0.091591 -0.050185  0.089785
			-0.085142 -0.053865  0.060240
			-0.078795 -0.058398  0.056620
			-0.071624 -0.058301  0.055727
			-0.067026 -0.053924  0.056784
			-0.097661 -0.060940  0.092403
			-0.048881 -0.048842  0.096234
			-0.061698 -0.040081  0.089113
			-0.061428 -0.046106  0.069411
			-0.065351 -0.049490  0.061383
			-0.072428 -0.040380  0.077771
			-0.088044 -0.048933  0.100261
			-0.018107  0.019491  0.132935
			-0.028267 -0.009664  0.112591
			-0.045061  0.043864  0.123584
			-0.063048 -0.006570  0.099770
			-0.008839 -0.013896  0.121746
			-0.001073  0.011410  0.128095
			-0.016222  0.038473  0.132645
			-0.042118  0.051224  0.132586
			-0.042675  0.072014  0.119140
			-0.065829  0.083022  0.103489
			-0.077514  0.027034  0.079831
			-0.063704  0.089931  0.100713
			-0.086810  0.093832  0.076345
			-0.105118  0.082413  0.073602
			-0.097580  0.072126  0.071741
			-0.127064  0.049844  0.068258
			-0.101925 -0.005538  0.073290
			-0.140045  0.047838  0.057869
			-0.132189  0.020569  0.063785
			-0.135709  0.025433  0.056811
			-0.142756  0.037853  0.055989
			-0.121831  0.074797  0.061410
			-0.134095  0.010597  0.055875
			-0.120259 -0.006688  0.063216
			-0.130339 -0.000991  0.057141
			-0.058937 -0.031863  0.062129
			-0.083712  0.011709  0.060801
			-0.036015 -0.018197  0.095127
			-0.045607 -0.037891  0.078833
			-0.095264 -0.012132  0.070120
			-0.097172  0.024295  0.055110
			-0.104359  0.010080  0.050771
			-0.134140  0.009515  0.055856
			-0.118896  0.021691  0.059151
			-0.068605  0.091887  0.085194
			-0.056939  0.031117  0.098576
			-0.082516  0.044380  0.066276
			-0.023835 -0.009300  0.106864
			 0.000000  0.000000  0.118718
			-0.008366  0.022010  0.122779
			-0.001184  0.011484  0.127749
			-0.026130  0.046823  0.120518
			-0.084245  0.065990  0.059553
			-0.080521  0.089270  0.070947
			-0.099933  0.080171  0.058907
			-0.110783  0.041488  0.052254
			-0.140283  0.027057  0.051667
			-0.142567  0.036860  0.050538
			-0.130442  0.036093  0.043881
			-0.137764  0.052955  0.051671
			-0.135777  0.025181  0.056779
			-0.101184  0.087329  0.067624
			-0.121330  0.055186  0.048853
			-0.096303 -0.102807  0.040325
			-0.106712 -0.111791  0.036161
			-0.065047 -0.095889  0.019016
			-0.046878 -0.072892  0.002407
			-0.026301 -0.071631  0.004815
			-0.025062 -0.079029  0.002221
			-0.055663 -0.097839 -0.000351
			-0.055379 -0.082850  0.001799
			-0.062757 -0.090507  0.019243
			-0.055601 -0.064625  0.037258
			-0.072771 -0.117347  0.025230
			-0.092129 -0.120870  0.035677
			-0.119191 -0.101490  0.069475
			-0.135738 -0.096931  0.060259
			-0.131818 -0.075844  0.069124
			-0.135611 -0.084397  0.054166
			-0.098898 -0.090770  0.050130
			-0.114308 -0.115077  0.052030
			-0.111746 -0.086624  0.072478
			-0.118362 -0.066696  0.067764
			-0.126944 -0.070428  0.061559
			-0.125353 -0.054405  0.052960
			-0.110855 -0.059933  0.067604
			-0.089373 -0.030293  0.066890
			-0.102557 -0.079412  0.071675
			-0.105344 -0.063815  0.075543
			-0.080788 -0.075604  0.049913
			-0.100969 -0.048285  0.084058
			-0.062492 -0.055190  0.052861
			-0.054641 -0.074427  0.051754
			-0.052625 -0.067426  0.044255
			-0.041877 -0.045942  0.088833
			-0.059916 -0.027590  0.099000
			-0.029562 -0.031117  0.096830
			-0.088158 -0.031197  0.092581
			-0.071333 -0.042764  0.104575
			-0.031563 -0.083550  0.016874
			-0.043487 -0.047599  0.064706
			-0.041830 -0.065559  0.060295
			-0.052737 -0.076823  0.059980
			-0.021941 -0.075541  0.023867
			-0.043358 -0.059739  0.041738
			-0.054177 -0.102026  0.005631
			-0.036276 -0.072749  0.036449
			-0.059231 -0.032040  0.061986
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					2 1 0 
					4 3 2 
					7 6 5 
					8 6 7 
					5 6 9 
					10 11 12 
					11 10 13 
					11 14 12 
					14 11 15 
					0 1 8 
					16 14 6 
					17 16 6 
					17 6 8 
					18 10 17 
					19 18 1 
					6 20 9 
					14 15 6 
					6 15 20 
					2 21 4 
					22 4 21 
					23 3 4 
					23 4 22 
					24 10 19 
					24 19 3 
					25 22 26 
					25 23 22 
					19 1 3 
					24 3 23 
					13 24 23 
					13 23 25 
					18 8 1 
					18 17 8 
					16 10 12 
					17 10 16 
					15 11 25 
					25 26 15 
					15 26 20 
					19 10 18 
					13 10 24 
					12 14 16 
					29 28 27 
					30 28 29 
					31 27 28 
					32 27 31 
					27 32 33 
					34 27 33 
					34 29 27 
					34 33 35 
					36 37 29 
					34 36 29 
					36 34 35 
					36 35 38 
					40 36 39 
					40 41 36 
					39 36 38 
					41 42 37 
					36 41 37 
					30 29 37 
					43 30 37 
					43 37 42 
					44 45 42 
					46 45 47 
					73 46 47 
					47 45 44 
					48 42 40 
					40 42 41 
					48 44 42 
					49 45 46 
					49 50 45 
					51 50 49 
					50 43 42 
					42 45 50 
					52 53 54 
					54 113 55 
					54 55 52 
					56 53 52 
					58 57 53 
					58 53 50 
					58 50 51 
					59 58 51 
					58 60 57 
					43 53 56 
					50 53 43 
					61 62 63 
					62 53 63 
					113 54 64 
					65 31 64 
					67 65 66 
					31 65 67 
					53 62 54 
					54 62 64 
					64 62 65 
					66 62 68 
					68 62 61 
					67 66 33 
					33 66 68 
					35 33 68 
					35 68 61 
					35 61 38 
					63 69 61 
					70 69 71 
					72 69 63 
					57 72 63 
					57 60 72 
					73 74 75 
					76 74 44 
					74 76 75 
					72 60 75 
					75 60 77 
					77 73 75 
					75 73 77 
					60 59 77 
					78 48 40 
					78 71 48 
					39 70 78 
					39 78 40 
					38 61 39 
					70 39 61 
					76 44 48 
					71 69 79 
					72 79 69 
					79 76 48 
					75 76 79 
					59 60 58 
					75 79 72 
					57 63 53 
					61 69 70 
					78 70 71 
					66 65 62 
					82 81 80 
					83 84 85 
					86 83 85 
					83 86 87 
					86 88 89 
					87 86 89 
					89 83 87 
					81 90 91 
					94 93 92 
					95 93 94 
					81 93 95 
					81 95 80 
					95 96 80 
					93 81 97 
					93 97 92 
					99 94 98 
					99 100 94 
					99 102 101 
					103 101 56 
					99 98 102 
					102 98 104 
					105 102 104 
					106 100 101 
					20 105 9 
					107 105 20 
					103 106 101 
					109 106 108 
					109 108 110 
					110 108 89 
					113 112 111 
					64 112 113 
					30 112 64 
					114 112 30 
					112 115 111 
					114 30 56 
					30 43 56 
					107 20 26 
					114 107 26 
					114 26 115 
					107 114 56 
					107 56 102 
					9 104 5 
					96 95 100 
					106 96 100 
					85 116 86 
					118 117 111 
					89 121 120 
					89 120 84 
					89 84 83 
					122 90 86 
					81 86 90 
					97 96 92 
					80 96 97 
					80 97 91 
					91 90 80 
					120 123 116 
					116 123 118 
					118 119 116 
					89 116 110 
					110 116 109 
					109 116 119 
					120 116 85 
					120 85 84 
					92 96 98 
					96 106 5 
					96 5 104 
					98 96 104 
					82 90 122 
					88 82 122 
					82 80 90 
					88 122 116 
					122 86 116 
					108 106 124 
					121 117 118 
					121 118 123 
					121 123 120 
					111 117 55 
					55 117 124 
					108 121 89 
					111 55 113 
					56 124 103 
					124 106 103 
					108 124 121 
					124 117 121 
					89 88 116 
					97 81 91 
					86 81 82 
					94 92 98 
					102 56 101 
					94 100 95 
					105 104 9 
					112 114 115 
					13 25 11 
					2 3 1 
					0 7 5 
					7 0 8 
					21 115 22 
					26 22 115 
					111 115 21 
					2 111 21 
					111 2 118 
					0 119 2 
					2 119 118 
					106 119 5 
					109 119 106 
					101 100 99 
					105 107 102 
					64 31 28 
					30 64 28 
					48 71 79 
					88 86 82 
					44 74 47 
					47 74 73 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
