<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="320" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="636">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.835117  0.111147 -0.538726
			 0.836002  0.464409 -0.292277
			 0.626314  0.185708 -0.757128
			 0.679884 -0.014633 -0.733173
			 0.597194 -0.079770 -0.798121
			 0.978252 -0.041623 -0.203203
			 0.955273  0.054401 -0.290677
			 0.918220  0.274040 -0.285961
			 0.629284  0.721055 -0.289969
			 0.805340  0.389184 -0.447171
			 0.908221  0.413596  0.063828
			 0.797854  0.565623  0.208565
			 0.846541  0.452637 -0.280157
			 0.985000  0.124545 -0.119433
			 0.573869 -0.237443 -0.783770
			 0.963885 -0.259440 -0.060138
			 0.887040 -0.083000 -0.454170
			 0.991105  0.030646 -0.129508
			 0.784596  0.063256 -0.616773
			 0.595777  0.208558 -0.775599
			 0.867846  0.213760 -0.448499
			 0.723872  0.376689 -0.578026
			 0.592755  0.787520  0.168683
			 0.591054  0.644934  0.484474
			 0.357210  0.429552 -0.829389
			 0.462244  0.138840 -0.875816
			 0.661334  0.247161 -0.708201
			 0.947413  0.263094 -0.182181
			 0.752546  0.394012  0.527664
			 0.772169  0.406578  0.488313
			 0.933757 -0.063863  0.352164
			 0.780553 -0.575169 -0.244781
			 0.645005 -0.336548 -0.686078
			 0.739005 -0.622350  0.257978
			 0.657057 -0.348548 -0.668423
			 0.575378 -0.558445 -0.597561
			 0.883395  0.125636 -0.451474
			 0.447322  0.020119 -0.894147
			-0.188278 -0.031033 -0.981625
			 0.543121  0.469273 -0.696277
			-0.005965  0.243260 -0.969943
			 0.768177  0.425055  0.478782
			 0.689339  0.300723  0.659073
			 0.316507  0.252322 -0.914416
			-0.075413 -0.012547 -0.997073
			-0.042328 -0.011729 -0.999035
			 0.585890  0.295010 -0.754786
			-0.219252  0.107326 -0.969747
			 0.798693  0.215543 -0.561810
			 0.992040  0.125512  0.010138
			 0.678185  0.095392 -0.728674
			 0.724867  0.178509  0.665359
			 0.363187  0.300419  0.881954
			-0.030659  0.092635  0.995228
			 0.625296  0.613147  0.482758
			 0.219707  0.443256  0.869053
			 0.776772 -0.368074  0.511025
			 0.429246 -0.884342 -0.183543
			 0.178052 -0.916841  0.357352
			-0.157649 -0.272773 -0.949074
			-0.116461 -0.819939 -0.560480
			-0.248452 -0.667815 -0.701637
			-0.218523 -0.887257 -0.406230
			-0.680064 -0.256107 -0.686966
			-0.630852 -0.142423 -0.762720
			 0.836986  0.228153  0.497394
			 0.534361  0.586902  0.608279
			 0.791363  0.301897  0.531604
			 0.654492 -0.110473  0.747954
			-0.670686 -0.148813 -0.726660
			 0.513666  0.228992 -0.826868
			-0.148213  0.173536 -0.973611
			-0.795310 -0.101151 -0.597704
			 0.767270  0.085692  0.635573
			 0.765135 -0.075195  0.639464
			 0.970194  0.048326 -0.237461
			 0.352896  0.087609 -0.931552
			 0.185691  0.050980 -0.981285
			 0.605921  0.068974 -0.792529
			 0.144523 -0.025374  0.989176
			-0.266566  0.155739  0.951151
			-0.759235  0.052101  0.648728
			-0.727822 -0.094203  0.679265
			-0.452488 -0.104868  0.885583
			-0.313947  0.642953  0.698604
			 0.068797  0.819404  0.569073
			-0.618047  0.026638  0.785690
			 0.401215 -0.600821  0.691405
			-0.257770 -0.966093  0.014802
			-0.239332 -0.791918  0.561771
			-0.721850 -0.596225  0.351352
			-0.472562 -0.798505 -0.372928
			-0.144586 -0.880582 -0.451298
			-0.607161 -0.609036 -0.510323
			-0.726874 -0.683645  0.065457
			-0.752565 -0.359492 -0.551735
			-0.700754 -0.288250 -0.652576
			-0.803092 -0.170457 -0.570953
			 0.658261  0.091836  0.747167
			 0.138710  0.131459  0.981569
			 0.031476  0.603333  0.796868
			 0.334218 -0.132671  0.933111
			-0.110381 -0.516481  0.849155
			-0.137392  0.050568 -0.989225
			-0.974927 -0.168600  0.145226
			-0.827176 -0.141614 -0.543806
			 0.663345 -0.298849  0.686049
			 0.940921 -0.299153  0.158668
			 0.319073 -0.208045  0.924613
			-0.024970 -0.131249  0.991035
			 0.058363 -0.220703  0.973593
			 0.908115  0.060338 -0.414351
			 0.708694 -0.132883 -0.692889
			 0.355343 -0.069950 -0.932115
			 0.253864 -0.015013 -0.967124
			-0.981558 -0.114118  0.153363
			-0.993873  0.105660 -0.032439
			-0.835977 -0.014808  0.548564
			-0.544120 -0.217855  0.810230
			-0.909103  0.103901  0.403405
			-0.555935  0.429389  0.711731
			-0.938025 -0.133936  0.319641
			-0.646236 -0.403523  0.647725
			-0.629655 -0.498394  0.595935
			-0.828514 -0.407958  0.383580
			-0.855342 -0.202220  0.476967
			-0.772586 -0.271537  0.573915
			-0.850373 -0.503578  0.152563
			-0.879382 -0.460425 -0.121232
			-0.767891 -0.518171 -0.376620
			-0.590508 -0.455178 -0.666418
			-0.627529 -0.534750 -0.565906
			-0.842911 -0.225768 -0.488396
			-0.858141 -0.030140  0.512529
			-0.316782 -0.179971  0.931268
			-0.817629 -0.208526  0.536657
			-0.384058 -0.132463 -0.913758
			-0.901291 -0.315669  0.296693
			-0.690082 -0.008044 -0.723687
			-0.725434 -0.080989 -0.683510
			-0.936044 -0.266327 -0.229980
			 0.346096 -0.329482  0.878441
			 0.158746 -0.165880  0.973285
			 0.648327 -0.264443  0.713963
			 0.923592 -0.126979  0.361738
			 0.810226 -0.322798 -0.489219
			 0.954587 -0.286971  0.080075
			-0.356606  0.030807  0.933747
			-0.138791 -0.165401  0.976411
			-0.189429 -0.374193  0.907798
			 0.605473 -0.188351 -0.773257
			 0.556592 -0.267380 -0.786584
			 0.081189 -0.214945 -0.973246
			-0.968584 -0.236151  0.077957
			-0.597248 -0.360918  0.716263
			-0.766259 -0.635307  0.096088
			-0.844314 -0.423156  0.328745
			-0.637927 -0.702325  0.315893
			-0.803227 -0.595414 -0.017570
			-0.732115 -0.636440 -0.242801
			-0.645836 -0.117535 -0.754375
			-0.412204 -0.542428  0.732024
			-0.734313 -0.587714  0.339671
			-0.753674  0.023234 -0.656837
			-0.419847 -0.074581 -0.904525
			-0.704652 -0.238506 -0.668267
			-0.876470 -0.438345  0.199134
			-0.915812 -0.309140  0.256361
			-0.889618 -0.321399 -0.324472
			 0.408018 -0.044525  0.911888
			 0.202933  0.017662  0.979033
			-0.468966  0.274745  0.839396
			-0.020375  0.220522  0.975169
			 0.653977  0.147680  0.741960
			 0.907450 -0.143034  0.395065
			 0.824608  0.012597  0.565564
			 0.816079 -0.288468 -0.500800
			 0.910039 -0.352458  0.218181
			-0.571907  0.192669  0.797371
			-0.435816  0.296837  0.849678
			-0.317602  0.150597  0.936189
			-0.463355 -0.395375  0.793083
			 0.396437 -0.281245 -0.873921
			 0.730880 -0.183926 -0.657256
			 0.617779 -0.292066 -0.730101
			 0.517301 -0.035890 -0.855051
			-0.329554 -0.217231 -0.918806
			-0.867165 -0.164593 -0.470035
			-0.765009 -0.049983 -0.642077
			-0.251826 -0.222975 -0.941735
			-0.650475 -0.369462 -0.663611
			-0.689197 -0.436280  0.578505
			-0.858600 -0.511147 -0.039190
			-0.419683 -0.041505  0.906721
			-0.830574 -0.384465 -0.402906
			-0.807625 -0.151476 -0.569910
			 0.026888  0.271110  0.962173
			 0.409079  0.326899  0.851934
			-0.107203  0.434859  0.894094
			-0.348400  0.326641  0.878591
			-0.520892  0.227197  0.822832
			 0.457568 -0.113332  0.881923
			-0.209678  0.079638  0.974522
			-0.026105  0.520301  0.853584
			 0.789859 -0.246332  0.561643
			 0.714449 -0.264787  0.647649
			 0.791608 -0.030801 -0.610253
			 0.990067 -0.125069  0.064220
			-0.546493  0.170473  0.819929
			-0.497900  0.073207  0.864139
			-0.278608  0.183010  0.942807
			-0.295963 -0.501368 -0.813041
			 0.371303 -0.414027 -0.831093
			-0.017884 -0.375220 -0.926763
			 0.197027 -0.115422 -0.973580
			 0.345475  0.651607 -0.675318
			 0.265608  0.869488 -0.416464
			-0.863559 -0.377934 -0.333814
			-0.548993 -0.502385 -0.667994
			-0.334927 -0.347649 -0.875765
			-0.085279 -0.038079  0.995629
			-0.627570 -0.483957  0.609870
			-0.912764 -0.374850  0.162322
			-0.961005 -0.078809 -0.265063
			-0.498612  0.290348 -0.816752
			-0.477056  0.029345 -0.878383
			-0.103185  0.467691  0.877849
			-0.474613  0.009450  0.880144
			 0.011448  0.181575  0.983310
			 0.293863  0.349088  0.889822
			-0.365022  0.015007  0.930878
			-0.502612  0.165751  0.848474
			 0.291669 -0.323938  0.899996
			-0.281759 -0.159960  0.946057
			-0.363655  0.226591  0.903555
			-0.594397 -0.007598  0.804136
			 0.796963  0.013316  0.603881
			 0.680423 -0.384783  0.623672
			 0.710681  0.658809  0.246786
			 0.622006  0.654900  0.429203
			 0.883085  0.355504 -0.306231
			 0.201473  0.108861  0.973426
			-0.124054 -0.008540  0.992239
			-0.265725  0.050008  0.962751
			-0.764874 -0.283080 -0.578648
			-0.514258 -0.032249 -0.857029
			 0.128634  0.236442 -0.963093
			 0.091071  0.956229 -0.278087
			-0.110492  0.883672 -0.454879
			-0.302116  0.568349 -0.765314
			-0.415153  0.809148 -0.415844
			 0.511037  0.818291  0.263138
			 0.116043  0.989718  0.083624
			-0.023481  0.999724 -0.000806
			 0.031903  0.999147 -0.026206
			-0.729943 -0.457404 -0.507903
			-0.043436 -0.000791  0.999056
			-0.593134  0.019058  0.804878
			-0.651086  0.142477  0.745511
			-0.978611  0.204986  0.017338
			-0.504818  0.593577 -0.626758
			-0.025278  0.784719 -0.619336
			 0.486089  0.751648 -0.445806
			-0.800087  0.177836 -0.572917
			-0.799681 -0.221169 -0.558207
			 0.430685  0.489941  0.757937
			-0.089686 -0.119900  0.988727
			-0.302872 -0.172813  0.937232
			 0.962696  0.095192  0.253290
			-0.290224 -0.222570  0.930716
			-0.396019 -0.019412  0.918037
			 0.426237 -0.179847  0.886553
			-0.617952  0.215071  0.756227
			-0.636861  0.039162  0.769983
			 0.451421  0.230276  0.862086
			 0.823689  0.272862  0.497074
			 0.020341  0.755850 -0.654429
			 0.145891  0.968427 -0.202151
			 0.223032  0.934108 -0.278744
			 0.658980  0.709869  0.248658
			 0.128862  0.219238  0.967124
			-0.761134  0.083083  0.643251
			-0.447631 -0.021021  0.893971
			-0.822101  0.214621 -0.527341
			-0.258472  0.768027 -0.585941
			-0.180330  0.921478 -0.344033
			-0.694323  0.527990 -0.489022
			 0.299898  0.925634 -0.230789
			 0.441305  0.876757 -0.191174
			 0.463450  0.824015 -0.325904
			-0.819824  0.567175 -0.078752
			-0.878235  0.405655  0.253271
			 0.048926  0.984055 -0.171002
			 0.595625  0.796168 -0.106528
			-0.504899  0.300677  0.809117
			 0.506759  0.035351  0.861363
			 0.169947 -0.405638  0.898096
			-0.201510 -0.246443  0.947977
			-0.269464  0.113559  0.956291
			-0.059597  0.124215  0.990464
			-0.151949  0.028257  0.987984
			-0.175186 -0.254337  0.951117
			-0.030453  0.637144  0.770143
			-0.234466  0.217406  0.947502
			 0.679060  0.485895  0.550258
			 0.427962  0.875762 -0.223359
			 0.400772  0.872560 -0.279322
			 0.300564  0.953744  0.005877
			-0.108849  0.577667  0.808982
			 0.463666  0.867622  0.179574
			-0.707331  0.345732  0.616565
			-0.063662  0.562149  0.824582
			-0.404339  0.470381  0.784380
			-0.280238  0.828992  0.483982
			-0.140008  0.955007  0.261457
			 0.540890  0.817216  0.198990
			-0.000324  0.384713  0.923036
			 0.271667  0.687568  0.673385
			-0.233326  0.138548  0.962478
			 0.442653  0.850137  0.285175
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.004444 -0.091913 -0.003436
			 0.003506 -0.088176 -0.003208
			 0.006738 -0.092875 -0.002477
			 0.006887 -0.098431 -0.002875
			 0.004592 -0.095837 -0.003703
			 0.004071 -0.095875 -0.004913
			 0.004315 -0.092420 -0.004675
			 0.003862 -0.089443 -0.005126
			 0.005550 -0.089837 -0.002128
			 0.000158 -0.083760 -0.006230
			 0.001115 -0.083514 -0.001559
			 0.002563 -0.087219  0.000045
			 0.006792 -0.090548 -0.001653
			 0.007895 -0.093681 -0.000848
			 0.006582 -0.099777 -0.002899
			 0.007546 -0.099468 -0.000908
			 0.003724 -0.097975 -0.003678
			 0.004206 -0.097580 -0.006036
			 0.003021 -0.094287 -0.008162
			 0.002676 -0.090685 -0.008177
			 0.003308 -0.090610 -0.006527
			 0.002932 -0.089160 -0.006576
			 0.006363 -0.089952 -0.000789
			 0.004186 -0.089558  0.000246
			 0.001084 -0.088083 -0.007286
			 0.000318 -0.086165 -0.007086
			-0.002515 -0.078791 -0.008007
			-0.000748 -0.077992 -0.001812
			 0.001146 -0.085743  0.001399
			 0.006697 -0.091291  0.000178
			 0.007578 -0.097327  0.001019
			 0.006455 -0.101903 -0.001276
			 0.005543 -0.101279 -0.002874
			 0.005284 -0.102747  0.000682
			 0.004062 -0.099143 -0.007803
			 0.004363 -0.101675 -0.004573
			 0.003493 -0.096273 -0.007332
			 0.002875 -0.096749 -0.008455
			 0.000524 -0.095976 -0.008669
			 0.002017 -0.089184 -0.007788
			-0.000783 -0.089288 -0.008288
			 0.001721 -0.092282  0.004912
			 0.005079 -0.096380  0.005302
			-0.000395 -0.087904 -0.007434
			-0.001778 -0.086459 -0.007936
			-0.003389 -0.079516 -0.008445
			-0.004973 -0.069053 -0.006554
			-0.006801 -0.069950 -0.007397
			-0.004520 -0.065484 -0.004238
			-0.003824 -0.062217 -0.001856
			-0.005021 -0.062127 -0.003937
			-0.001788 -0.075810  0.000487
			-0.002584 -0.081620  0.002759
			-0.006647 -0.069977  0.001457
			-0.000734 -0.089109  0.005799
			-0.001540 -0.085900  0.004183
			 0.006510 -0.099384  0.003266
			 0.005037 -0.103091 -0.001318
			 0.002824 -0.103096  0.003613
			 0.000822 -0.097642 -0.008530
			 0.002054 -0.100263 -0.005895
			 0.000795 -0.098858 -0.007895
			 0.002206 -0.103302 -0.002641
			 0.000276 -0.097599 -0.008240
			-0.001969 -0.089629 -0.007452
			 0.000528 -0.093463  0.008772
			-0.000340 -0.090531  0.008426
			 0.001169 -0.094031  0.007276
			 0.001924 -0.098151  0.007862
			-0.006431 -0.073418 -0.007186
			-0.005837 -0.065760 -0.005766
			-0.007836 -0.066736 -0.006454
			-0.010541 -0.063741 -0.004048
			-0.004569 -0.061802  0.000462
			-0.005982 -0.053197  0.001959
			-0.004049 -0.059006 -0.002752
			-0.006003 -0.055343 -0.003346
			-0.005177 -0.055646 -0.003388
			-0.004604 -0.059025 -0.003663
			-0.006401 -0.066185  0.001447
			-0.005208 -0.082987  0.003255
			-0.006202 -0.087454  0.002599
			-0.008889 -0.073834  0.000600
			-0.009815 -0.067104  0.000782
			-0.001513 -0.088994  0.006403
			-0.000972 -0.089655  0.007346
			-0.003578 -0.089807  0.004809
			 0.003108 -0.102016  0.004758
			 0.001949 -0.103548  0.000256
			 0.001745 -0.102247  0.004895
			-0.002891 -0.100641  0.001858
			-0.000167 -0.100902 -0.003841
			 0.000422 -0.100373 -0.005550
			-0.000418 -0.099853 -0.006024
			-0.003305 -0.100336  0.001117
			-0.003677 -0.096348 -0.002638
			-0.002716 -0.093939 -0.005104
			-0.006296 -0.091044 -0.002826
			 0.000774 -0.094950  0.008935
			-0.000561 -0.092883  0.009504
			-0.001000 -0.090787  0.008616
			 0.000384 -0.095367  0.009161
			 0.000263 -0.098374  0.008328
			-0.009241 -0.058591 -0.005445
			-0.012448 -0.057842 -0.001136
			-0.012469 -0.054351 -0.003341
			-0.004902 -0.043528  0.002427
			-0.002245 -0.039776  0.000209
			-0.007307 -0.044440  0.004393
			-0.009575 -0.057049  0.002139
			-0.007878 -0.050391  0.003236
			-0.004548 -0.055572 -0.003018
			-0.003348 -0.044521 -0.002716
			-0.009406 -0.050858 -0.005095
			-0.006234 -0.052393 -0.003420
			-0.007832 -0.084205 -0.000858
			-0.007543 -0.089247  0.000185
			-0.007771 -0.091988  0.001820
			-0.011256 -0.054100  0.002207
			-0.002308 -0.091716  0.006592
			-0.001458 -0.091375  0.009111
			-0.002669 -0.094538  0.004986
			-0.004053 -0.094702  0.003027
			-0.006947 -0.094104  0.001815
			-0.001803 -0.100015  0.004369
			-0.002270 -0.098256  0.003147
			-0.003964 -0.098405  0.001030
			-0.003969 -0.099548  0.000758
			-0.004311 -0.098947  0.000078
			-0.004670 -0.097275 -0.000463
			-0.004265 -0.094750 -0.003389
			-0.005485 -0.095590 -0.001819
			-0.007529 -0.092981 -0.001487
			-0.002135 -0.093661  0.007772
			-0.000204 -0.095627  0.009211
			-0.002223 -0.095837  0.007825
			-0.010726 -0.050877 -0.004999
			-0.012917 -0.054476 -0.000506
			-0.011946 -0.045047 -0.005285
			-0.013763 -0.044637 -0.003574
			-0.014367 -0.049275 -0.000301
			-0.005274 -0.039288  0.004986
			-0.007420 -0.039602  0.005734
			-0.004310 -0.039023  0.004640
			-0.001299 -0.036956  0.000782
			-0.002264 -0.038315 -0.002959
			-0.001212 -0.037104 -0.001242
			-0.008228 -0.039821  0.005568
			-0.010507 -0.044758  0.004853
			-0.012324 -0.048117  0.003285
			-0.006094 -0.041846 -0.004266
			-0.004995 -0.037868 -0.004566
			-0.009442 -0.042556 -0.007164
			-0.007989 -0.093496  0.000545
			-0.012896 -0.051045  0.001881
			-0.005551 -0.096333  0.000192
			-0.004713 -0.097522  0.000779
			-0.006313 -0.095305  0.000667
			-0.007647 -0.094081  0.000115
			-0.006404 -0.095267 -0.000902
			-0.010992 -0.042896 -0.006496
			-0.013458 -0.049189  0.002517
			-0.014330 -0.049623  0.001304
			-0.011911 -0.042608 -0.004528
			-0.014131 -0.041960 -0.003740
			-0.015199 -0.039747 -0.003163
			-0.014911 -0.048786  0.001896
			-0.016292 -0.041801  0.001771
			-0.016706 -0.040579 -0.000439
			-0.005015 -0.038177  0.005243
			-0.005992 -0.038298  0.005430
			-0.008123 -0.038502  0.005565
			-0.007333 -0.038347  0.005748
			-0.003043 -0.036852  0.003508
			-0.001345 -0.034390 -0.000618
			-0.002575 -0.034391  0.002236
			-0.000470 -0.035082 -0.002894
			 0.000004 -0.033168 -0.001898
			-0.009795 -0.040171  0.003998
			-0.011065 -0.042273  0.004492
			-0.013860 -0.042846  0.003603
			-0.014257 -0.048326  0.003167
			-0.008347 -0.039828 -0.007442
			-0.007216 -0.039800 -0.006038
			-0.006750 -0.037365 -0.007051
			-0.001494 -0.034322 -0.003567
			-0.010121 -0.040704 -0.007204
			-0.011630 -0.041117 -0.006197
			-0.011810 -0.038894 -0.004294
			-0.014055 -0.038804 -0.004095
			-0.015588 -0.038568 -0.003463
			-0.016951 -0.040221  0.002147
			-0.017161 -0.040025  0.001205
			-0.015613 -0.040980  0.002827
			-0.017349 -0.038705 -0.000298
			-0.017007 -0.034412 -0.003442
			-0.005863 -0.037285  0.005296
			-0.004701 -0.037155  0.004963
			-0.006872 -0.037472  0.005337
			-0.008004 -0.036940  0.004667
			-0.008600 -0.036991  0.004170
			-0.004875 -0.033958  0.004898
			-0.006302 -0.035684  0.004504
			-0.006113 -0.036191  0.004889
			-0.001920 -0.032691  0.002423
			-0.004029 -0.033765  0.003782
			 0.000531 -0.031574 -0.002338
			 0.000733 -0.031733 -0.001840
			-0.011130 -0.039319  0.003297
			-0.010634 -0.038954  0.003701
			-0.012243 -0.039106  0.002402
			-0.010266 -0.039383 -0.007678
			-0.009101 -0.039237 -0.008140
			-0.009577 -0.038389 -0.008615
			-0.009548 -0.037193 -0.008960
			-0.007104 -0.031039 -0.005757
			-0.001523 -0.029297 -0.002939
			-0.011850 -0.039824 -0.006567
			-0.012009 -0.037755 -0.004153
			-0.015091 -0.036726 -0.004964
			-0.016047 -0.039716  0.002721
			-0.017618 -0.039150  0.002568
			-0.018599 -0.038449  0.001714
			-0.019108 -0.036553  0.000782
			-0.016119 -0.029110 -0.003908
			-0.015546 -0.033291 -0.004869
			-0.006815 -0.036196  0.004538
			-0.008925 -0.035145  0.003543
			-0.007742 -0.036089  0.004478
			-0.007989 -0.035227  0.004349
			-0.010613 -0.035499  0.002877
			-0.010639 -0.037135  0.003224
			-0.004905 -0.033112  0.005178
			-0.005881 -0.034079  0.004841
			-0.006968 -0.035234  0.003941
			-0.006987 -0.034370  0.004103
			-0.003237 -0.032145  0.003862
			-0.003474 -0.032922  0.003928
			 0.000388 -0.030834 -0.001090
			-0.001062 -0.031480  0.001082
			 0.000536 -0.030812 -0.002046
			-0.015031 -0.038094  0.001892
			-0.015447 -0.035567  0.002181
			-0.011502 -0.036493  0.002744
			-0.012658 -0.037410 -0.007323
			-0.011947 -0.033017 -0.009049
			-0.010305 -0.032637 -0.009643
			-0.011220 -0.030632 -0.006117
			-0.011452 -0.030907 -0.006827
			-0.011315 -0.031842 -0.009114
			-0.012186 -0.031397 -0.008202
			 0.000259 -0.030280 -0.001859
			-0.011804 -0.030667 -0.005291
			-0.009345 -0.030656 -0.003977
			-0.006701 -0.031308 -0.000300
			-0.013036 -0.036815 -0.005049
			-0.017637 -0.037728  0.003121
			-0.018189 -0.037130  0.002795
			-0.018706 -0.032944  0.001525
			-0.018704 -0.029681  0.000563
			-0.016448 -0.027248 -0.003057
			-0.013148 -0.030126 -0.004816
			-0.014838 -0.027749 -0.002887
			-0.013767 -0.032749 -0.005860
			-0.013565 -0.034061 -0.005800
			-0.007767 -0.035309  0.003866
			-0.008117 -0.032007  0.004110
			-0.009974 -0.034227  0.003570
			-0.008074 -0.034163  0.003898
			-0.013715 -0.031922  0.002131
			-0.006183 -0.032099  0.004965
			-0.004328 -0.032174  0.005404
			-0.007707 -0.034724  0.003766
			-0.007417 -0.033894  0.003524
			-0.004107 -0.031417  0.005288
			-0.003229 -0.031607  0.003645
			-0.008557 -0.029913  0.000213
			-0.009486 -0.029655  0.001951
			-0.009734 -0.029121  0.002773
			-0.003182 -0.031260  0.003169
			-0.016933 -0.036126  0.002616
			-0.017771 -0.028393  0.001851
			-0.017236 -0.028369  0.002381
			-0.012942 -0.033095 -0.008074
			-0.012108 -0.030634 -0.006335
			-0.012660 -0.030395 -0.005601
			-0.012441 -0.031312 -0.006488
			-0.012314 -0.030586 -0.004079
			-0.012837 -0.029805 -0.003173
			-0.011678 -0.030579 -0.000324
			-0.017336 -0.026060  0.000349
			-0.017686 -0.026684  0.001059
			-0.016974 -0.025836 -0.000318
			-0.014909 -0.027290 -0.001163
			-0.007745 -0.034338  0.003376
			-0.007911 -0.033587  0.003296
			-0.007996 -0.032597  0.003667
			-0.010643 -0.031174  0.003468
			-0.008078 -0.031288  0.004045
			-0.011395 -0.030358  0.003365
			-0.014104 -0.029241  0.003455
			-0.013013 -0.030598  0.003429
			-0.005669 -0.030258  0.004500
			-0.007976 -0.030562  0.004025
			-0.003896 -0.030997  0.004935
			-0.012760 -0.029565  0.000614
			-0.013157 -0.028982  0.002050
			-0.013225 -0.028669  0.002548
			-0.011241 -0.028653  0.003053
			-0.003861 -0.030723  0.004478
			-0.017426 -0.027084  0.002002
			-0.016242 -0.027139  0.002618
			-0.016973 -0.026946  0.002235
			-0.017253 -0.026253  0.001275
			-0.017018 -0.025860  0.000713
			-0.015251 -0.027097  0.001698
			-0.012073 -0.029238  0.003013
			-0.014569 -0.028217  0.003142
			-0.007465 -0.030056  0.003941
			-0.015213 -0.027437  0.002404
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					0 3 4 
					0 4 5 
					6 0 5 
					1 0 7 
					6 7 0 
					8 2 1 
					7 9 1 
					9 10 1 
					11 8 1 
					10 11 1 
					8 12 2 
					12 13 2 
					3 2 13 
					3 14 4 
					14 3 15 
					3 13 15 
					4 16 5 
					14 16 4 
					17 5 16 
					5 17 18 
					18 6 5 
					19 6 18 
					20 6 19 
					7 6 20 
					21 9 7 
					7 20 21 
					22 8 23 
					22 12 8 
					11 23 8 
					24 25 9 
					24 9 21 
					26 27 9 
					26 9 25 
					9 27 10 
					10 27 28 
					10 28 11 
					11 28 23 
					29 12 22 
					12 29 13 
					30 15 13 
					13 29 30 
					14 15 31 
					31 32 14 
					14 32 16 
					15 30 33 
					31 15 33 
					34 17 16 
					16 35 34 
					16 32 35 
					18 17 36 
					36 17 34 
					18 37 38 
					18 38 19 
					18 36 37 
					39 20 19 
					39 19 40 
					38 40 19 
					21 20 39 
					39 24 21 
					29 22 23 
					41 42 23 
					42 29 23 
					41 23 28 
					24 43 25 
					39 40 24 
					40 43 24 
					25 43 44 
					44 45 25 
					45 26 25 
					46 26 47 
					26 46 27 
					45 47 26 
					46 48 27 
					49 27 50 
					27 48 50 
					27 49 51 
					51 28 27 
					52 28 53 
					28 54 41 
					51 53 28 
					55 28 52 
					55 54 28 
					42 30 29 
					56 33 30 
					30 42 56 
					31 33 57 
					57 32 31 
					32 57 35 
					58 33 56 
					33 58 57 
					34 59 37 
					34 60 61 
					36 34 37 
					61 59 34 
					34 35 60 
					62 60 35 
					57 62 35 
					38 37 59 
					63 64 38 
					38 59 63 
					40 38 64 
					44 40 64 
					40 44 43 
					65 41 66 
					67 42 41 
					67 41 65 
					66 41 54 
					56 42 68 
					42 67 68 
					64 69 44 
					45 44 69 
					47 45 69 
					70 48 46 
					47 71 46 
					71 70 46 
					71 47 72 
					47 69 72 
					50 48 70 
					73 51 49 
					49 74 73 
					50 75 49 
					49 75 74 
					70 76 50 
					76 77 50 
					77 78 50 
					78 75 50 
					51 73 79 
					51 79 53 
					80 55 52 
					53 80 52 
					80 53 81 
					82 81 53 
					82 53 83 
					53 79 83 
					55 84 54 
					85 66 54 
					84 85 54 
					55 86 84 
					80 86 55 
					68 87 56 
					58 56 87 
					57 58 88 
					57 88 62 
					87 89 58 
					88 58 90 
					89 90 58 
					59 61 63 
					62 91 60 
					60 91 92 
					60 92 61 
					63 61 93 
					93 61 92 
					62 88 94 
					91 62 94 
					93 95 63 
					96 64 63 
					95 96 63 
					97 69 64 
					64 96 97 
					68 65 98 
					65 99 98 
					67 65 68 
					66 99 65 
					100 99 66 
					66 85 100 
					98 101 68 
					102 68 101 
					87 68 102 
					69 97 72 
					76 70 103 
					71 103 70 
					103 71 72 
					104 72 97 
					105 72 104 
					72 105 103 
					79 73 74 
					106 74 107 
					106 108 74 
					109 79 74 
					109 74 110 
					74 108 110 
					75 111 74 
					112 74 111 
					74 112 107 
					78 111 75 
					113 114 76 
					76 114 77 
					76 103 113 
					77 111 78 
					112 111 77 
					114 112 77 
					79 109 83 
					80 81 86 
					81 82 115 
					116 81 115 
					81 117 86 
					116 117 81 
					82 104 115 
					82 83 104 
					118 83 109 
					83 118 104 
					119 84 86 
					85 84 120 
					120 84 119 
					85 120 100 
					121 119 86 
					121 86 122 
					122 86 123 
					117 123 86 
					102 89 87 
					90 94 88 
					90 89 124 
					102 124 89 
					124 125 90 
					125 126 90 
					94 90 126 
					91 127 128 
					94 127 91 
					128 93 91 
					93 92 91 
					95 93 128 
					94 126 127 
					129 95 128 
					130 96 95 
					130 95 129 
					130 97 96 
					115 104 97 
					115 97 116 
					131 132 97 
					131 97 130 
					97 132 116 
					101 98 99 
					133 99 120 
					120 99 100 
					134 101 99 
					134 99 133 
					102 101 134 
					135 102 134 
					124 102 135 
					136 103 105 
					136 113 103 
					104 137 105 
					137 104 118 
					138 105 139 
					105 137 140 
					139 105 140 
					136 105 138 
					141 142 106 
					143 141 106 
					106 142 108 
					107 144 106 
					106 144 143 
					112 145 107 
					107 145 146 
					144 107 146 
					147 108 142 
					110 108 148 
					108 147 148 
					118 109 110 
					110 149 118 
					110 148 149 
					150 151 112 
					112 114 113 
					113 150 112 
					145 112 151 
					150 113 152 
					113 136 152 
					153 116 132 
					117 116 153 
					123 117 153 
					137 118 154 
					118 149 154 
					133 119 121 
					120 119 133 
					121 122 125 
					121 124 135 
					121 125 124 
					133 121 135 
					155 156 122 
					125 122 126 
					122 156 126 
					122 123 157 
					155 122 157 
					157 123 158 
					158 123 153 
					127 126 128 
					126 156 128 
					129 128 156 
					131 129 155 
					129 156 155 
					131 130 129 
					131 155 159 
					132 131 159 
					153 132 158 
					132 159 158 
					135 134 133 
					160 152 136 
					136 138 160 
					154 161 137 
					162 137 161 
					137 162 140 
					138 139 163 
					138 163 160 
					164 163 139 
					165 139 140 
					164 139 165 
					162 166 140 
					166 167 140 
					167 168 140 
					165 140 168 
					141 143 169 
					142 141 170 
					141 169 170 
					142 171 147 
					142 170 172 
					172 171 142 
					173 169 143 
					144 173 143 
					144 146 174 
					173 144 175 
					144 174 175 
					145 151 176 
					176 146 145 
					177 146 176 
					177 174 146 
					178 179 147 
					179 148 147 
					171 178 147 
					179 180 148 
					181 149 148 
					181 148 180 
					149 181 161 
					149 161 154 
					152 182 150 
					151 150 183 
					150 182 183 
					183 184 151 
					151 184 185 
					185 176 151 
					186 182 152 
					186 152 160 
					155 157 159 
					159 157 158 
					160 163 187 
					187 186 160 
					162 161 181 
					166 162 181 
					188 187 163 
					188 163 164 
					189 164 165 
					189 188 164 
					165 168 190 
					165 190 189 
					166 181 167 
					191 192 167 
					167 193 191 
					168 167 192 
					181 193 167 
					168 192 194 
					195 168 194 
					195 190 168 
					170 169 196 
					197 196 169 
					197 169 173 
					170 196 198 
					170 198 172 
					172 198 171 
					198 199 171 
					199 200 171 
					171 200 178 
					201 173 175 
					201 202 173 
					173 202 203 
					173 203 197 
					174 177 204 
					175 174 204 
					204 205 175 
					175 205 201 
					176 185 206 
					176 206 177 
					207 177 206 
					207 204 177 
					208 179 178 
					178 200 209 
					208 178 209 
					208 210 179 
					210 180 179 
					180 193 181 
					210 193 180 
					186 211 182 
					212 182 211 
					182 212 183 
					212 184 183 
					212 213 184 
					185 184 214 
					184 213 214 
					214 215 185 
					206 185 216 
					185 215 216 
					187 217 186 
					217 211 186 
					187 188 217 
					218 217 188 
					218 188 189 
					189 190 219 
					189 219 218 
					190 195 219 
					191 220 221 
					193 220 191 
					221 192 191 
					194 192 222 
					221 222 192 
					193 210 220 
					223 194 222 
					223 195 194 
					195 224 225 
					195 225 219 
					223 224 195 
					197 203 196 
					196 203 226 
					196 226 198 
					226 199 198 
					227 200 199 
					226 228 199 
					199 228 229 
					199 229 227 
					230 200 227 
					200 230 231 
					200 231 209 
					205 232 201 
					201 232 233 
					201 233 202 
					203 202 234 
					202 235 234 
					235 202 233 
					203 234 226 
					236 237 204 
					204 207 238 
					238 239 204 
					236 204 239 
					237 205 204 
					237 232 205 
					240 207 206 
					240 206 216 
					240 238 207 
					231 210 208 
					231 208 209 
					241 220 210 
					242 241 210 
					231 243 210 
					243 242 210 
					213 212 211 
					217 244 211 
					244 213 211 
					214 213 244 
					245 246 214 
					245 214 244 
					214 246 215 
					247 216 215 
					215 248 247 
					215 249 250 
					215 246 249 
					248 215 250 
					251 240 216 
					247 252 216 
					252 253 216 
					216 253 254 
					251 216 254 
					218 255 217 
					244 217 255 
					219 255 218 
					219 225 255 
					256 221 220 
					256 220 241 
					222 221 257 
					221 256 257 
					257 258 222 
					223 222 258 
					259 223 258 
					260 224 223 
					260 223 259 
					224 261 225 
					224 262 261 
					224 260 262 
					263 264 225 
					225 264 255 
					225 261 263 
					226 234 265 
					229 226 265 
					228 226 229 
					229 266 227 
					266 267 227 
					267 230 227 
					229 268 266 
					229 265 268 
					269 243 230 
					243 231 230 
					267 269 230 
					270 233 232 
					271 232 237 
					270 232 271 
					235 233 270 
					234 272 265 
					235 272 234 
					235 273 272 
					273 235 270 
					274 236 275 
					236 239 275 
					274 271 236 
					271 237 236 
					238 254 276 
					238 276 277 
					239 238 277 
					251 238 240 
					238 251 254 
					278 279 239 
					239 279 275 
					239 277 278 
					280 241 242 
					256 241 280 
					281 258 242 
					242 282 281 
					282 242 269 
					280 242 258 
					243 269 242 
					255 264 244 
					283 244 263 
					244 264 263 
					283 245 244 
					246 245 249 
					245 283 249 
					284 285 247 
					252 247 285 
					247 248 284 
					286 284 248 
					286 248 250 
					249 283 250 
					286 250 283 
					287 253 252 
					285 261 252 
					252 261 287 
					253 287 288 
					253 288 254 
					288 289 254 
					276 254 289 
					257 256 280 
					280 258 257 
					259 258 281 
					259 290 260 
					291 259 281 
					259 291 290 
					292 260 290 
					262 260 293 
					292 293 260 
					261 286 263 
					262 288 261 
					287 261 288 
					285 286 261 
					262 293 289 
					288 262 289 
					286 283 263 
					294 265 272 
					294 295 265 
					265 295 268 
					268 296 266 
					267 266 269 
					269 266 297 
					266 298 299 
					266 299 297 
					266 296 270 
					298 266 270 
					295 296 268 
					269 300 282 
					269 297 301 
					301 300 269 
					270 271 274 
					270 274 302 
					298 270 303 
					270 302 303 
					270 296 273 
					273 294 272 
					273 296 295 
					273 295 294 
					275 304 274 
					274 304 302 
					279 304 275 
					289 305 276 
					276 305 277 
					305 306 277 
					277 306 278 
					306 307 278 
					308 278 307 
					302 309 278 
					302 278 308 
					278 309 279 
					279 309 304 
					282 310 281 
					310 291 281 
					300 311 282 
					310 282 312 
					282 311 312 
					284 286 285 
					289 293 305 
					290 291 313 
					313 314 290 
					314 292 290 
					310 313 291 
					292 315 293 
					314 315 292 
					315 305 293 
					297 299 301 
					316 299 298 
					316 298 303 
					300 299 316 
					301 299 300 
					317 300 316 
					300 317 311 
					303 302 318 
					304 309 302 
					318 302 308 
					308 303 318 
					316 303 308 
					305 315 306 
					319 306 315 
					306 319 307 
					316 308 307 
					317 307 319 
					307 317 316 
					313 310 312 
					313 312 311 
					311 319 313 
					311 317 319 
					313 315 314 
					313 319 315 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 1275 1278 1281 1284 1287 1290 1293 1296 1299 1302 1305 1308 1311 1314 1317 1320 1323 1326 1329 1332 1335 1338 1341 1344 1347 1350 1353 1356 1359 1362 1365 1368 1371 1374 1377 1380 1383 1386 1389 1392 1395 1398 1401 1404 1407 1410 1413 1416 1419 1422 1425 1428 1431 1434 1437 1440 1443 1446 1449 1452 1455 1458 1461 1464 1467 1470 1473 1476 1479 1482 1485 1488 1491 1494 1497 1500 1503 1506 1509 1512 1515 1518 1521 1524 1527 1530 1533 1536 1539 1542 1545 1548 1551 1554 1557 1560 1563 1566 1569 1572 1575 1578 1581 1584 1587 1590 1593 1596 1599 1602 1605 1608 1611 1614 1617 1620 1623 1626 1629 1632 1635 1638 1641 1644 1647 1650 1653 1656 1659 1662 1665 1668 1671 1674 1677 1680 1683 1686 1689 1692 1695 1698 1701 1704 1707 1710 1713 1716 1719 1722 1725 1728 1731 1734 1737 1740 1743 1746 1749 1752 1755 1758 1761 1764 1767 1770 1773 1776 1779 1782 1785 1788 1791 1794 1797 1800 1803 1806 1809 1812 1815 1818 1821 1824 1827 1830 1833 1836 1839 1842 1845 1848 1851 1854 1857 1860 1863 1866 1869 1872 1875 1878 1881 1884 1887 1890 1893 1896 1899 1902 1905 1908 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
