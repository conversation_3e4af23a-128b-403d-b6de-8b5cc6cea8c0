<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="125" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="246">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.127217 -0.893207  0.431274
			-0.062785 -0.986232 -0.152984
			-0.008605 -0.849080  0.528194
			-0.163694 -0.893438  0.418298
			-0.173997 -0.984443  0.024439
			-0.206116 -0.696240  0.687580
			-0.089838 -0.433897  0.896472
			-0.470307 -0.398361  0.787477
			 0.098037 -0.976643  0.191199
			 0.476071 -0.848218 -0.232128
			-0.214730 -0.835598 -0.505635
			-0.152871 -0.821967 -0.548635
			 0.297968 -0.748457 -0.592476
			-0.104009 -0.846959 -0.521386
			 0.346851 -0.179931  0.920500
			 0.803625 -0.013910  0.594974
			 0.009121 -0.901296  0.433108
			 0.071801 -0.600508  0.796388
			-0.080930 -0.993497 -0.080088
			-0.227295 -0.949834 -0.214833
			 0.221390  0.273438  0.936065
			-0.082285  0.047074  0.995497
			-0.644552 -0.258482  0.719541
			-0.994903  0.047730  0.088822
			 0.269301  0.395087  0.878284
			-0.050989  0.552924  0.831670
			-0.417214  0.811739  0.408672
			 0.871821 -0.489788 -0.005953
			 0.791980 -0.235305 -0.563382
			 0.874464  0.455299 -0.167378
			-0.257611 -0.817314 -0.515398
			 0.269858 -0.037958 -0.962152
			-0.316922 -0.757581 -0.570641
			-0.109953 -0.101922 -0.988697
			-0.262883 -0.480053 -0.836924
			-0.109402 -0.620860 -0.776250
			 0.373298  0.083021 -0.923989
			 0.546595  0.611945 -0.571626
			 0.521048  0.779978  0.346617
			 0.429322  0.895414 -0.117967
			 0.294969 -0.586435  0.754379
			 0.304069 -0.830308  0.467045
			 0.071047 -0.986214  0.149447
			 0.565130  0.070164  0.822013
			-0.009674 -0.792367 -0.609968
			-0.071831 -0.992241 -0.101479
			-0.035597 -0.931382 -0.362298
			 0.021891 -0.992338  0.121595
			-0.136286  0.981777  0.132439
			-0.265124  0.964116 -0.013796
			 0.125926  0.949010  0.289002
			 0.366826  0.852169  0.373157
			 0.544391  0.739581  0.395801
			-0.729578 -0.503462 -0.462863
			-0.021933 -0.267568 -0.963289
			 0.357828  0.725726 -0.587606
			 0.179072  0.981555 -0.066953
			-0.073840  0.835356 -0.544728
			-0.579045  0.651704 -0.489886
			-0.099447  0.638571 -0.763110
			-0.019677  0.231213 -0.972704
			 0.176203 -0.290349 -0.940558
			 0.644016 -0.235726  0.727789
			-0.075673 -0.899795  0.429701
			 0.202609 -0.396006  0.895617
			-0.263755 -0.540062  0.799229
			 0.744330  0.202254  0.636448
			 0.685689  0.531273  0.497573
			 0.233864 -0.481079 -0.844908
			 0.184059 -0.716164 -0.673225
			-0.866308 -0.180910 -0.465599
			-0.932434 -0.253049 -0.257940
			-0.967360  0.107304  0.229565
			-0.478478 -0.799312  0.363537
			 0.114206  0.987003 -0.113061
			 0.442201  0.868138 -0.225376
			 0.573522  0.814905 -0.083683
			 0.462563  0.873847  0.149754
			-0.189156 -0.368908 -0.910015
			-0.300108  0.949882 -0.087513
			-0.402776  0.496128 -0.769174
			 0.089856  0.463310 -0.881629
			-0.210177  0.975644 -0.062806
			 0.304284  0.709268 -0.635886
			 0.534329  0.421758 -0.732539
			 0.402049 -0.221396 -0.888448
			 0.563268  0.141140  0.814131
			 0.744359  0.432259  0.509000
			-0.549877 -0.520777  0.653014
			-0.034994  0.279531  0.959499
			-0.687508  0.033917  0.725384
			-0.856784  0.057691  0.512438
			 0.156617 -0.164215 -0.973912
			-0.552526 -0.389963 -0.736644
			-0.706556  0.701369 -0.094135
			-0.973372  0.229220 -0.002322
			-0.802994  0.595987 -0.000316
			-0.969431  0.236868 -0.064013
			-0.859453  0.493652  0.132845
			 0.502175  0.763573 -0.405927
			 0.430236  0.895982 -0.110064
			-0.594818  0.803855 -0.003074
			-0.054802  0.970168  0.236158
			 0.451194  0.842446  0.294464
			 0.331592  0.398904 -0.854940
			 0.505467  0.640066  0.578635
			 0.118990  0.697798  0.706342
			-0.639442  0.349275  0.684924
			-0.992051  0.079227  0.097761
			-0.783388  0.441789 -0.437179
			-0.747312  0.047185 -0.662796
			-0.165280  0.452831 -0.876143
			-0.829319  0.556388  0.051609
			-0.933362  0.353740 -0.060860
			-0.979644  0.198603 -0.029235
			-0.979311 -0.195378 -0.052698
			-0.999537  0.017565 -0.024829
			-0.954059  0.296390  0.043856
			 0.180903  0.861278 -0.474840
			-0.145554  0.952457 -0.267657
			-0.959944  0.280191  0.000669
			 0.178936  0.862731  0.472944
			-0.585154  0.641837  0.495620
			-0.956387  0.289774  0.036822
			-0.329789  0.851520 -0.407618
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.060663 -0.162418 -0.028044
			 0.062625 -0.162587 -0.029903
			 0.061751 -0.161772 -0.026194
			 0.056047 -0.161276 -0.027507
			 0.058974 -0.162445 -0.028973
			 0.060459 -0.161124 -0.025728
			 0.057141 -0.160399 -0.026383
			 0.057786 -0.160296 -0.026170
			 0.063336 -0.162713 -0.028203
			 0.064185 -0.162522 -0.029573
			 0.060317 -0.161820 -0.030937
			 0.059537 -0.162301 -0.030051
			 0.063434 -0.162331 -0.030820
			 0.062424 -0.161933 -0.031430
			 0.062155 -0.160906 -0.025664
			 0.064163 -0.161995 -0.027275
			 0.052889 -0.160143 -0.025951
			 0.055399 -0.160444 -0.026591
			 0.048893 -0.159822 -0.030862
			 0.056645 -0.161718 -0.029505
			 0.059410 -0.159784 -0.024980
			 0.058123 -0.159411 -0.024991
			 0.057512 -0.159324 -0.025102
			 0.057813 -0.159667 -0.025377
			 0.055917 -0.159831 -0.026273
			 0.056927 -0.159878 -0.026477
			 0.057727 -0.159666 -0.026540
			 0.064730 -0.162332 -0.028770
			 0.064194 -0.162269 -0.030471
			 0.064459 -0.161852 -0.029217
			 0.058060 -0.160510 -0.031381
			 0.061525 -0.161122 -0.032219
			 0.058682 -0.160582 -0.032177
			 0.056682 -0.160433 -0.031303
			 0.057264 -0.160583 -0.031254
			 0.055131 -0.160731 -0.030866
			 0.062892 -0.161819 -0.031599
			 0.063270 -0.161427 -0.030892
			 0.062668 -0.160596 -0.026272
			 0.063084 -0.160721 -0.028432
			 0.053566 -0.159911 -0.025797
			 0.050977 -0.159556 -0.024431
			 0.050277 -0.159981 -0.025368
			 0.053355 -0.158910 -0.025270
			 0.050915 -0.159998 -0.031331
			 0.047088 -0.160084 -0.030072
			 0.049301 -0.159645 -0.031696
			 0.048466 -0.160170 -0.024386
			 0.058209 -0.158989 -0.025393
			 0.058169 -0.159491 -0.027156
			 0.057095 -0.159629 -0.026894
			 0.056025 -0.159259 -0.027052
			 0.055074 -0.158827 -0.026720
			 0.057660 -0.160127 -0.031996
			 0.059432 -0.160333 -0.032563
			 0.059524 -0.159800 -0.032356
			 0.059867 -0.159685 -0.029121
			 0.056909 -0.159885 -0.030993
			 0.057644 -0.159938 -0.031350
			 0.056288 -0.160038 -0.031198
			 0.055644 -0.160223 -0.031117
			 0.053752 -0.159656 -0.031413
			 0.051620 -0.158275 -0.023730
			 0.048846 -0.159910 -0.023384
			 0.049561 -0.158921 -0.022315
			 0.049134 -0.159425 -0.022691
			 0.052896 -0.158192 -0.025064
			 0.053506 -0.157991 -0.025970
			 0.051682 -0.158591 -0.032357
			 0.049996 -0.159049 -0.032716
			 0.046943 -0.159528 -0.031873
			 0.045933 -0.159893 -0.030274
			 0.046038 -0.159865 -0.026954
			 0.047705 -0.159944 -0.024371
			 0.057566 -0.159440 -0.028938
			 0.055723 -0.158924 -0.029558
			 0.053913 -0.157927 -0.030249
			 0.050208 -0.155316 -0.026603
			 0.058040 -0.159900 -0.032621
			 0.058099 -0.159414 -0.032295
			 0.057209 -0.159701 -0.032426
			 0.058810 -0.159824 -0.032604
			 0.058037 -0.159732 -0.031021
			 0.055729 -0.159558 -0.030859
			 0.054791 -0.159039 -0.030995
			 0.051534 -0.157374 -0.032803
			 0.050526 -0.157658 -0.022456
			 0.051540 -0.157064 -0.023667
			 0.048340 -0.159649 -0.023335
			 0.049805 -0.157403 -0.022222
			 0.048837 -0.157721 -0.022553
			 0.047995 -0.159333 -0.023588
			 0.049126 -0.157903 -0.034008
			 0.047919 -0.158865 -0.033390
			 0.047464 -0.158427 -0.028324
			 0.048987 -0.156266 -0.029745
			 0.048123 -0.157616 -0.028291
			 0.048188 -0.157617 -0.032850
			 0.048280 -0.158231 -0.025145
			 0.050661 -0.155483 -0.032947
			 0.050876 -0.155283 -0.032207
			 0.048719 -0.154913 -0.029205
			 0.049048 -0.155387 -0.024826
			 0.050380 -0.156110 -0.023833
			 0.050354 -0.155820 -0.033507
			 0.051051 -0.156847 -0.023228
			 0.049923 -0.156382 -0.023035
			 0.048799 -0.157054 -0.022719
			 0.048490 -0.157610 -0.023934
			 0.048399 -0.156450 -0.033827
			 0.048267 -0.157531 -0.033899
			 0.048702 -0.155940 -0.033684
			 0.048479 -0.157083 -0.028334
			 0.048368 -0.155497 -0.032252
			 0.048791 -0.156591 -0.028656
			 0.048621 -0.155931 -0.028263
			 0.048903 -0.156366 -0.027285
			 0.048821 -0.156655 -0.027883
			 0.050097 -0.155307 -0.032948
			 0.049301 -0.154896 -0.032157
			 0.048384 -0.155292 -0.027096
			 0.049775 -0.155905 -0.023726
			 0.048410 -0.156009 -0.023879
			 0.048251 -0.155543 -0.025509
			 0.049139 -0.155493 -0.033391
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 4 0 
					0 2 5 
					3 0 6 
					7 6 0 
					4 1 0 
					7 0 5 
					2 1 8 
					1 9 8 
					10 1 11 
					1 4 11 
					9 1 12 
					10 13 1 
					13 12 1 
					5 2 14 
					15 2 8 
					14 2 15 
					16 3 17 
					18 3 16 
					6 17 3 
					19 4 3 
					18 19 3 
					4 19 11 
					5 14 20 
					21 22 5 
					22 23 5 
					5 20 21 
					5 23 7 
					17 6 24 
					6 25 24 
					25 6 7 
					7 26 25 
					7 23 26 
					8 27 15 
					9 27 8 
					9 12 28 
					9 28 29 
					27 9 29 
					10 11 30 
					31 13 10 
					32 31 10 
					30 32 10 
					33 34 11 
					19 35 11 
					11 35 33 
					30 11 34 
					13 36 12 
					12 36 37 
					12 37 28 
					31 36 13 
					14 38 20 
					38 14 15 
					15 27 29 
					15 39 38 
					29 39 15 
					17 40 16 
					40 41 16 
					42 16 41 
					16 42 18 
					24 43 17 
					40 17 43 
					44 19 18 
					18 45 46 
					46 44 18 
					47 45 18 
					47 18 42 
					35 19 44 
					20 48 21 
					48 20 38 
					21 48 22 
					22 48 23 
					49 23 48 
					26 23 49 
					25 50 24 
					51 24 50 
					51 52 24 
					52 43 24 
					26 50 25 
					26 49 50 
					37 29 28 
					39 29 37 
					30 53 32 
					30 34 53 
					54 31 32 
					36 31 39 
					31 54 55 
					55 56 31 
					31 56 39 
					32 53 54 
					33 57 58 
					33 58 34 
					57 33 59 
					33 35 60 
					59 33 60 
					53 34 58 
					44 61 35 
					61 60 35 
					39 37 36 
					56 38 39 
					48 38 56 
					62 41 40 
					43 62 40 
					47 42 41 
					63 47 41 
					64 65 41 
					65 63 41 
					64 41 62 
					66 43 67 
					62 43 66 
					67 43 52 
					68 61 44 
					69 68 44 
					69 44 46 
					70 46 45 
					45 71 70 
					71 45 72 
					72 45 73 
					45 47 73 
					69 46 70 
					73 47 63 
					48 56 49 
					74 50 49 
					49 56 74 
					50 75 76 
					50 76 51 
					50 74 75 
					76 52 51 
					77 52 76 
					67 52 77 
					53 78 54 
					53 58 79 
					78 53 80 
					80 53 79 
					81 55 54 
					78 81 54 
					55 79 56 
					81 79 55 
					56 82 74 
					56 79 82 
					74 57 83 
					82 58 57 
					74 82 57 
					60 83 57 
					60 57 59 
					58 82 79 
					61 84 60 
					60 84 83 
					61 85 84 
					68 85 61 
					86 64 62 
					87 62 66 
					86 62 87 
					65 88 63 
					73 63 88 
					64 86 89 
					65 64 90 
					90 64 89 
					91 88 65 
					90 91 65 
					87 66 67 
					87 67 77 
					68 69 85 
					92 69 93 
					70 93 69 
					85 69 92 
					71 94 70 
					95 70 96 
					93 70 97 
					95 97 70 
					70 94 96 
					72 94 71 
					91 72 73 
					91 98 72 
					94 72 96 
					72 98 96 
					91 73 88 
					83 75 74 
					76 75 84 
					83 84 75 
					99 76 84 
					76 99 100 
					76 100 77 
					101 77 100 
					77 101 102 
					77 102 103 
					77 103 87 
					78 80 81 
					80 79 81 
					85 104 84 
					104 99 84 
					85 92 104 
					86 87 105 
					105 106 86 
					89 86 106 
					87 103 105 
					89 106 107 
					107 90 89 
					91 90 108 
					90 107 108 
					108 98 91 
					109 92 110 
					93 110 92 
					111 92 109 
					111 104 92 
					110 93 97 
					95 96 112 
					101 113 95 
					114 95 112 
					97 95 113 
					115 101 95 
					95 114 115 
					96 98 116 
					117 96 116 
					96 117 112 
					110 97 109 
					113 109 97 
					116 98 108 
					99 104 118 
					100 99 119 
					99 118 119 
					119 101 100 
					101 119 113 
					120 102 101 
					101 115 120 
					121 103 102 
					122 121 102 
					122 102 123 
					120 123 102 
					105 103 121 
					124 118 104 
					124 104 111 
					106 105 121 
					106 121 122 
					122 107 106 
					122 108 107 
					123 108 122 
					108 123 116 
					124 111 109 
					113 119 109 
					109 119 124 
					117 114 112 
					114 117 115 
					115 116 120 
					116 115 117 
					116 123 120 
					118 124 119 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
