<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="296" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="588">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.056882  0.370339 -0.927153
			-0.327794  0.439554 -0.836267
			 0.480306  0.622181 -0.618221
			 0.224467  0.124538 -0.966491
			-0.393487  0.337759 -0.855036
			-0.194948  0.587774 -0.785186
			-0.680282  0.383986 -0.624316
			 0.358836  0.769102 -0.528885
			 0.482427  0.559285 -0.674140
			 0.548947  0.741558 -0.385681
			 0.758809  0.583731 -0.288907
			 0.238247  0.754502 -0.611527
			 0.576903 -0.050438 -0.815254
			 0.206854 -0.479650 -0.852730
			-0.236223 -0.173973 -0.955998
			-0.229899  0.178927 -0.956625
			-0.571670  0.309483 -0.759878
			-0.374454  0.510193 -0.774266
			-0.745332  0.476328 -0.466468
			-0.764635  0.396916 -0.507730
			-0.818151  0.141423 -0.557340
			 0.567036  0.483133 -0.667123
			 0.187683  0.815078 -0.548109
			 0.716722  0.664970 -0.210057
			 0.136339  0.924015 -0.357223
			 0.494169  0.667721 -0.556728
			 0.744362  0.567745 -0.351555
			 0.780648 -0.022884 -0.624552
			 0.627912 -0.438785 -0.642802
			-0.270668 -0.419024 -0.866694
			 0.011883 -0.705844 -0.708268
			 0.526995 -0.695345 -0.488642
			-0.658100 -0.077070 -0.748976
			-0.396472  0.406270 -0.823259
			 0.074463  0.531103 -0.844029
			-0.910580 -0.044843 -0.410893
			-0.861920  0.222369 -0.455681
			-0.966237 -0.156543 -0.204648
			-0.894225 -0.047277 -0.445114
			-0.884687 -0.102261 -0.454831
			-0.927454  0.106478 -0.358458
			 0.694506  0.559785 -0.451998
			 0.994311  0.066166 -0.083474
			 0.898517 -0.333212 -0.285720
			 0.052409  0.986277 -0.156562
			 0.571852  0.818594 -0.053746
			 0.169518  0.923720  0.343520
			 0.582604  0.597153 -0.551345
			 0.648710  0.663348  0.373021
			 0.701917  0.603798 -0.377811
			 0.892225  0.450341  0.033583
			-0.500488 -0.523146 -0.689804
			-0.178687 -0.851294 -0.493326
			 0.179085 -0.923421 -0.339443
			 0.768644 -0.579499 -0.270865
			 0.582415 -0.791540 -0.185088
			-0.875715  0.025134 -0.482173
			 0.362601  0.636612 -0.680621
			 0.015543  0.539333 -0.841949
			-0.907019 -0.260946 -0.330490
			-0.541652  0.238561 -0.806041
			-0.635630 -0.623788  0.454821
			-0.673531 -0.495522  0.548466
			-0.982130 -0.158718 -0.101136
			-0.916351 -0.245753  0.316081
			-0.958108 -0.022871 -0.285493
			-0.762613 -0.602771 -0.234709
			-0.929588 -0.360835 -0.075265
			-0.791188 -0.466760 -0.395166
			-0.986154 -0.093275 -0.137116
			-0.925813 -0.095329 -0.365763
			 0.966358  0.233274  0.108332
			 0.667047 -0.061059  0.742510
			 0.818599 -0.459668  0.344383
			 0.856262 -0.508582  0.090336
			 0.069197  0.809732  0.582705
			 0.573658  0.403776  0.712658
			 0.596605  0.456218  0.660248
			-0.010252  0.595062  0.803615
			 0.689993  0.623737 -0.367236
			 0.863355  0.227243  0.450533
			 0.807848 -0.063698  0.585939
			 0.658903  0.530091 -0.533714
			 0.980549  0.029235  0.194083
			 0.886767  0.389850 -0.248318
			-0.878189 -0.331055 -0.345233
			-0.300070 -0.908849 -0.289743
			-0.509078 -0.767367 -0.389857
			-0.203801 -0.938937 -0.277240
			 0.378209 -0.923919 -0.057715
			-0.035895 -0.973272 -0.226832
			 0.774969 -0.626650  0.082052
			 0.680015 -0.714302  0.165387
			 0.661196 -0.748350  0.052840
			 0.464464  0.494403 -0.734738
			 0.225497  0.581889 -0.781381
			-0.323436  0.420917 -0.847477
			-0.910953 -0.377848 -0.165515
			-0.791130 -0.207872 -0.575241
			-0.445690 -0.659056  0.605810
			-0.749182 -0.621696  0.228516
			-0.732712  0.032710 -0.679752
			-0.746310  0.218736 -0.628630
			-0.472576 -0.677913  0.563122
			-0.387775 -0.626490  0.676122
			-0.476964 -0.343423  0.809053
			 0.003469 -0.412723  0.910850
			 0.010105 -0.121578  0.992530
			-0.996770 -0.077705  0.020305
			-0.903740 -0.164605  0.395170
			-0.525332 -0.836783  0.154340
			-0.395644 -0.918298 -0.013988
			-0.544458 -0.821659 -0.168647
			-0.648754 -0.426755  0.630079
			-0.859903 -0.457792 -0.225818
			-0.700679 -0.704641 -0.111940
			 0.736464 -0.243696  0.631057
			 0.094194  0.285513  0.953735
			 0.408743  0.145695  0.900945
			 0.805643 -0.489041  0.334333
			 0.822171 -0.522741  0.225336
			 0.676072 -0.084885  0.731930
			 0.228600  0.169946  0.958572
			 0.379289 -0.258622  0.888400
			 0.762968 -0.380884  0.522309
			 0.235092 -0.611037  0.755887
			 0.989274 -0.139211  0.044249
			 0.843660  0.252634 -0.473723
			-0.688693 -0.699336 -0.191393
			-0.255384 -0.920687 -0.295151
			-0.311124 -0.922334 -0.229135
			-0.124161 -0.985185 -0.118299
			 0.841569 -0.508069  0.183379
			 0.073570 -0.847145  0.526244
			 0.726502 -0.528807  0.438815
			-0.079091 -0.965091  0.249687
			 0.871394 -0.446902  0.202363
			 0.756188  0.319953 -0.570798
			-0.057962  0.905961 -0.419374
			 0.272940  0.862607 -0.425926
			 0.400282  0.657029 -0.638817
			 0.538240  0.585701 -0.606013
			-0.350139  0.749870 -0.561336
			-0.620552  0.452965 -0.640108
			-0.805032 -0.457644 -0.377473
			-0.723186 -0.688484  0.054697
			-0.757760 -0.488192 -0.432977
			-0.794173 -0.161601 -0.585811
			 0.131004 -0.548590  0.825764
			-0.164185 -0.779220  0.604863
			-0.433261 -0.826886  0.358532
			-0.037578 -0.603893  0.796179
			-0.917475  0.074931 -0.390672
			-0.838718  0.509032 -0.193488
			-0.910768  0.118304  0.395608
			-0.090615 -0.567036  0.818694
			-0.314530 -0.413094  0.854649
			-0.307605 -0.288481  0.906729
			 0.346118  0.180791  0.920607
			 0.073380  0.448934  0.890547
			-0.015840  0.149098  0.988696
			-0.129003 -0.777515  0.615490
			-0.320293 -0.877422  0.357131
			-0.392586 -0.918462  0.048004
			-0.051424 -0.411032  0.910169
			-0.561984 -0.820053 -0.108108
			-0.635588 -0.698940  0.327889
			-0.595023 -0.783879  0.177429
			 0.662173 -0.179488  0.727538
			 0.056080  0.272620  0.960486
			 0.177517  0.364940  0.913951
			 0.057615  0.464555  0.883668
			 0.090226  0.245928  0.965080
			 0.261060 -0.500927  0.825179
			 0.670314 -0.580519  0.462252
			 0.349058 -0.835384  0.424608
			 0.690240 -0.688437  0.222763
			 0.958611 -0.284073 -0.019146
			-0.385840 -0.894397 -0.226233
			-0.527442 -0.834533  0.159246
			 0.014520 -0.602214  0.798202
			-0.503092 -0.681905  0.530946
			-0.537569 -0.680647  0.497734
			-0.207417 -0.733797  0.646932
			-0.584980 -0.759375  0.284864
			-0.501445 -0.671710  0.545305
			 0.850599  0.462678 -0.249822
			-0.308798  0.949192  0.060650
			 0.142383  0.984245 -0.104829
			-0.178292  0.971979  0.153193
			 0.365986  0.884335 -0.289837
			-0.598729  0.799268 -0.051905
			-0.890442 -0.443931  0.100195
			-0.539162 -0.787481  0.298627
			 0.053932 -0.963632  0.261735
			-0.496274 -0.761273  0.417343
			-0.479562 -0.783381  0.395392
			-0.199008 -0.852120  0.484032
			-0.602798  0.592540  0.534351
			-0.570193  0.104418  0.814848
			-0.586685 -0.553753  0.590897
			-0.599648 -0.210280  0.772143
			-0.460773 -0.104695  0.881321
			-0.341990 -0.538204  0.770311
			-0.277249 -0.309883  0.909453
			-0.044379  0.562300  0.825742
			-0.191745 -0.763477  0.616714
			-0.669632 -0.541207  0.508614
			-0.612985 -0.685749  0.392425
			-0.491549 -0.574016  0.654894
			-0.489091 -0.868109  0.084711
			 0.143774  0.259732  0.954918
			 0.555272 -0.826255  0.094742
			 0.772013 -0.590931  0.234089
			 0.761072 -0.377258  0.527679
			 0.962719 -0.115787  0.244470
			-0.606311 -0.291725  0.739786
			-0.493521 -0.637410  0.591731
			-0.577971 -0.288600  0.763321
			-0.696268 -0.611628  0.375662
			 0.763683  0.404301  0.503318
			 0.348284  0.936937  0.029097
			-0.403539  0.836020  0.371789
			-0.184342  0.758122  0.625515
			-0.018013  0.996261  0.084492
			 0.223036  0.970409  0.092532
			 0.210267  0.977574  0.011715
			-0.035465  0.820804  0.570108
			 0.225260  0.970621 -0.084570
			 0.218191 -0.960225  0.174245
			-0.623720 -0.490128  0.608891
			-0.664777 -0.451172  0.595412
			-0.281873 -0.424370  0.860498
			-0.352816 -0.397544  0.847041
			-0.097976 -0.778306  0.620194
			-0.289879  0.403003  0.868078
			-0.282842  0.664497  0.691697
			-0.413699  0.352139  0.839554
			-0.528013 -0.518382  0.672668
			-0.543466 -0.395269  0.740545
			-0.604679 -0.233684  0.761417
			-0.628675 -0.311357  0.712618
			-0.608965 -0.226175  0.760268
			-0.712341 -0.407472  0.571435
			 0.454945 -0.795771  0.399717
			 0.696698 -0.626256  0.349880
			 0.466312 -0.342376  0.815679
			 0.665567 -0.053102  0.744447
			-0.110661  0.966024  0.233565
			 0.202180  0.556262  0.806037
			 0.044741  0.866308  0.497502
			 0.233041  0.968335  0.089550
			-0.128081  0.516586  0.846602
			 0.085269  0.940593  0.328656
			 0.076681  0.861289  0.502296
			 0.146068  0.962726  0.227648
			-0.064984  0.860933  0.504551
			-0.182188  0.692205  0.698326
			-0.739469 -0.299134  0.603080
			-0.795226 -0.096164  0.598639
			-0.525955 -0.318144  0.788769
			-0.311256 -0.446886  0.838697
			 0.125841 -0.567008  0.814043
			-0.024272 -0.469760  0.882461
			-0.692019 -0.048787  0.720228
			-0.546600  0.398228  0.736643
			-0.576593  0.445946  0.684597
			-0.475585  0.773836  0.418326
			-0.436088  0.613283  0.658568
			 0.602230 -0.536657  0.591032
			-0.415032 -0.229863  0.880291
			-0.370147  0.028594  0.928533
			-0.110755 -0.400724  0.909480
			-0.116790  0.005007  0.993144
			-0.286415 -0.079585  0.954795
			 0.530661 -0.173317  0.829675
			 0.721325 -0.039370  0.691477
			 0.323601  0.451317  0.831622
			 0.098589  0.954795  0.280439
			-0.417590  0.855025  0.307491
			-0.170567  0.813427  0.556097
			-0.455078  0.809842  0.370215
			-0.419217  0.776217  0.470897
			-0.416668  0.650324  0.635191
			-0.757900  0.025259  0.651882
			-0.575544 -0.151615  0.803593
			-0.445765 -0.293687  0.845601
			-0.612704  0.125488  0.780286
			-0.551587  0.149588  0.820595
			-0.723844  0.300469  0.621103
			-0.650327  0.638034  0.412295
			-0.628286  0.582629  0.515558
			-0.659460  0.621022  0.423609
			-0.081195  0.169749  0.982137
			 0.214353  0.343547  0.914346
			 0.005830  0.702403  0.711755
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.016068 -0.054392 -0.059478
			 0.015573 -0.053452 -0.058875
			 0.017092 -0.053626 -0.058636
			 0.017720 -0.055772 -0.059733
			 0.015525 -0.055870 -0.059473
			 0.014870 -0.050677 -0.056704
			 0.013908 -0.055920 -0.058298
			 0.017733 -0.051425 -0.053651
			 0.018581 -0.054825 -0.058555
			 0.015833 -0.049267 -0.054570
			 0.016297 -0.049618 -0.053280
			 0.015354 -0.049765 -0.055754
			 0.020170 -0.056298 -0.058115
			 0.016132 -0.057941 -0.059228
			 0.014751 -0.057260 -0.059467
			 0.015734 -0.056261 -0.059721
			 0.013982 -0.056479 -0.058980
			 0.011331 -0.047532 -0.052924
			 0.012513 -0.057173 -0.057087
			 0.012549 -0.057428 -0.057650
			 0.010982 -0.057344 -0.055316
			 0.020353 -0.054678 -0.057534
			 0.020166 -0.050257 -0.052956
			 0.016663 -0.048829 -0.051282
			 0.017954 -0.049835 -0.051399
			 0.015425 -0.048617 -0.053647
			 0.015970 -0.048497 -0.052091
			 0.020919 -0.055965 -0.057358
			 0.020119 -0.057906 -0.057459
			 0.013840 -0.058923 -0.058656
			 0.014246 -0.061404 -0.056957
			 0.018344 -0.060956 -0.055912
			 0.013166 -0.058117 -0.058450
			 0.008907 -0.039245 -0.047212
			 0.012986 -0.047067 -0.053063
			 0.006495 -0.051129 -0.047710
			 0.011129 -0.058211 -0.056168
			 0.007167 -0.055258 -0.049496
			 0.008102 -0.056232 -0.050940
			 0.008483 -0.058921 -0.051785
			 0.010810 -0.059232 -0.055006
			 0.022086 -0.051428 -0.052249
			 0.022991 -0.054023 -0.053152
			 0.022327 -0.056233 -0.055214
			 0.019986 -0.049656 -0.051758
			 0.021733 -0.049866 -0.050189
			 0.017705 -0.049710 -0.049827
			 0.014912 -0.047413 -0.052597
			 0.016654 -0.049488 -0.049566
			 0.015046 -0.046340 -0.051232
			 0.014891 -0.045620 -0.049485
			 0.011881 -0.061371 -0.056261
			 0.012845 -0.062159 -0.055706
			 0.015553 -0.063335 -0.053857
			 0.019449 -0.060286 -0.054709
			 0.018593 -0.061374 -0.054156
			 0.010771 -0.059716 -0.055422
			 0.013917 -0.046020 -0.052036
			 0.005947 -0.031154 -0.041498
			 0.002016 -0.042793 -0.041357
			 0.002442 -0.032247 -0.040795
			 0.004821 -0.047841 -0.043384
			 0.006447 -0.051815 -0.045831
			 0.007490 -0.056543 -0.049249
			 0.007569 -0.055505 -0.048122
			 0.007921 -0.057736 -0.050419
			 0.008682 -0.060771 -0.050994
			 0.007931 -0.059951 -0.049631
			 0.009345 -0.060745 -0.052529
			 0.007688 -0.058793 -0.049780
			 0.010332 -0.060336 -0.054410
			 0.022629 -0.051479 -0.051186
			 0.020940 -0.052091 -0.048254
			 0.021212 -0.057307 -0.050639
			 0.020321 -0.059189 -0.052950
			 0.019881 -0.049894 -0.048886
			 0.021611 -0.050486 -0.049049
			 0.016795 -0.050308 -0.048691
			 0.018227 -0.050716 -0.048524
			 0.014248 -0.045185 -0.050566
			 0.014778 -0.047434 -0.046781
			 0.013778 -0.046377 -0.045010
			 0.009494 -0.034457 -0.043002
			 0.011884 -0.038165 -0.042156
			 0.011711 -0.037547 -0.043290
			 0.010502 -0.060951 -0.055017
			 0.012375 -0.062484 -0.054736
			 0.011305 -0.061864 -0.055205
			 0.013914 -0.062918 -0.054311
			 0.016714 -0.062986 -0.052546
			 0.014647 -0.063483 -0.052895
			 0.019589 -0.061095 -0.053164
			 0.018582 -0.061320 -0.051741
			 0.017719 -0.062491 -0.051013
			 0.009943 -0.031037 -0.040117
			 0.005452 -0.024254 -0.037624
			 0.001736 -0.025592 -0.037853
			-0.002068 -0.034849 -0.035964
			-0.002138 -0.033231 -0.036203
			 0.001673 -0.040967 -0.038069
			-0.000811 -0.036584 -0.035991
			-0.001471 -0.032179 -0.037143
			-0.002301 -0.025728 -0.035165
			 0.004390 -0.045285 -0.041131
			 0.006592 -0.049546 -0.044288
			 0.008358 -0.055898 -0.047248
			 0.009893 -0.053590 -0.045622
			 0.010847 -0.054929 -0.046149
			 0.007651 -0.057718 -0.048866
			 0.007782 -0.058575 -0.048630
			 0.008607 -0.060818 -0.049630
			 0.009475 -0.061338 -0.050999
			 0.009907 -0.061319 -0.052332
			 0.008037 -0.059839 -0.048332
			 0.010786 -0.061513 -0.054189
			 0.010333 -0.061454 -0.053508
			 0.019502 -0.056565 -0.048035
			 0.018290 -0.052691 -0.047522
			 0.018425 -0.055329 -0.047429
			 0.019861 -0.058543 -0.050094
			 0.019549 -0.059276 -0.051221
			 0.015145 -0.049737 -0.047063
			 0.016177 -0.051510 -0.048091
			 0.013542 -0.052086 -0.046859
			 0.009764 -0.038061 -0.037164
			 0.007687 -0.043927 -0.039917
			 0.012589 -0.031061 -0.035007
			 0.010928 -0.030989 -0.039050
			 0.011004 -0.061964 -0.054398
			 0.013171 -0.062817 -0.053505
			 0.011940 -0.062403 -0.054051
			 0.013659 -0.063831 -0.052088
			 0.018776 -0.060412 -0.050468
			 0.016196 -0.062820 -0.047770
			 0.017205 -0.062218 -0.048164
			 0.015306 -0.063703 -0.050010
			 0.019207 -0.059691 -0.049513
			 0.011557 -0.027411 -0.036507
			 0.004400 -0.022412 -0.035854
			 0.006212 -0.022436 -0.035354
			 0.008344 -0.023796 -0.035816
			 0.011682 -0.024741 -0.034424
			 0.000364 -0.023047 -0.035985
			-0.001330 -0.023981 -0.035432
			-0.002549 -0.033924 -0.035139
			-0.002329 -0.034612 -0.034138
			-0.003168 -0.033591 -0.034761
			-0.002822 -0.032673 -0.035641
			 0.006325 -0.044668 -0.040540
			 0.002341 -0.036495 -0.033030
			-0.000622 -0.035885 -0.034275
			 0.005016 -0.045462 -0.040543
			-0.004829 -0.030621 -0.033052
			-0.002606 -0.024736 -0.034306
			-0.004387 -0.028438 -0.031233
			 0.006343 -0.047870 -0.042324
			 0.010749 -0.056307 -0.046683
			 0.008852 -0.056759 -0.047320
			 0.012232 -0.055209 -0.046898
			 0.013378 -0.056051 -0.046708
			 0.011948 -0.056244 -0.046231
			 0.009565 -0.060703 -0.048359
			 0.011760 -0.061821 -0.051132
			 0.010513 -0.061629 -0.051923
			 0.010725 -0.059466 -0.047836
			 0.011104 -0.062262 -0.053535
			 0.011275 -0.062113 -0.052283
			 0.011185 -0.062347 -0.052760
			 0.017501 -0.059251 -0.046194
			 0.017185 -0.054235 -0.047273
			 0.016593 -0.057506 -0.045325
			 0.016316 -0.055662 -0.046529
			 0.013551 -0.055098 -0.047123
			 0.007376 -0.046968 -0.042614
			 0.010073 -0.033064 -0.032842
			 0.006481 -0.034606 -0.030930
			 0.008141 -0.033232 -0.029444
			 0.012651 -0.029489 -0.033268
			 0.012636 -0.062970 -0.052870
			 0.013113 -0.063283 -0.051734
			 0.015667 -0.060975 -0.045593
			 0.014635 -0.062869 -0.048868
			 0.014743 -0.061953 -0.047295
			 0.015465 -0.061946 -0.047249
			 0.012902 -0.063097 -0.050945
			 0.014221 -0.063382 -0.049596
			 0.013477 -0.024326 -0.031013
			 0.000278 -0.022554 -0.034162
			 0.005700 -0.022064 -0.033262
			 0.003880 -0.022344 -0.030730
			 0.009897 -0.023829 -0.034634
			-0.001372 -0.023427 -0.034470
			-0.004786 -0.031762 -0.032463
			-0.001422 -0.034585 -0.031841
			 0.002392 -0.035310 -0.030815
			 0.000514 -0.034864 -0.029792
			-0.000309 -0.034625 -0.030578
			 0.003522 -0.034313 -0.025906
			-0.000850 -0.024607 -0.030016
			-0.000541 -0.028983 -0.027948
			-0.001264 -0.034238 -0.030770
			-0.001707 -0.030837 -0.029004
			 0.013351 -0.057445 -0.045898
			 0.011553 -0.056942 -0.046976
			 0.011371 -0.057917 -0.047254
			 0.014434 -0.057197 -0.046135
			 0.012339 -0.061141 -0.049363
			 0.014157 -0.061619 -0.048891
			 0.012076 -0.062541 -0.051644
			 0.012731 -0.059455 -0.047735
			 0.012209 -0.062651 -0.052177
			 0.015542 -0.058168 -0.044788
			 0.007829 -0.033383 -0.027747
			 0.010805 -0.030236 -0.027505
			 0.013063 -0.026246 -0.027389
			 0.014080 -0.025522 -0.028177
			 0.014463 -0.059605 -0.045567
			 0.014459 -0.061238 -0.046507
			 0.014598 -0.058925 -0.044791
			 0.013740 -0.060849 -0.047916
			 0.013298 -0.024366 -0.027042
			 0.013036 -0.023581 -0.029460
			 0.000317 -0.023554 -0.030702
			 0.003759 -0.022716 -0.030012
			 0.005791 -0.022038 -0.031725
			 0.007173 -0.022189 -0.031220
			 0.008592 -0.022714 -0.030572
			 0.004910 -0.022312 -0.030404
			 0.010544 -0.023176 -0.030586
			 0.005866 -0.034398 -0.026305
			-0.000242 -0.034077 -0.029736
			 0.002204 -0.033796 -0.026504
			 0.005198 -0.033378 -0.024854
			 0.003782 -0.033651 -0.025219
			 0.005003 -0.034186 -0.025433
			 0.000224 -0.026768 -0.028045
			 0.001125 -0.024001 -0.029509
			 0.001628 -0.029241 -0.026943
			-0.000331 -0.031829 -0.028704
			-0.000511 -0.031234 -0.028156
			 0.000658 -0.031238 -0.027461
			 0.013318 -0.057806 -0.046477
			 0.012841 -0.058697 -0.046737
			 0.013827 -0.059817 -0.046000
			 0.006146 -0.033938 -0.025085
			 0.009124 -0.032074 -0.026582
			 0.008109 -0.029825 -0.023056
			 0.011834 -0.024744 -0.025654
			 0.011324 -0.023548 -0.027935
			 0.011081 -0.024428 -0.025084
			 0.011577 -0.023900 -0.025563
			 0.011555 -0.023480 -0.029651
			 0.005627 -0.025838 -0.028031
			 0.006609 -0.022227 -0.030560
			 0.007965 -0.022789 -0.030016
			 0.010109 -0.022891 -0.029711
			 0.009589 -0.023010 -0.029497
			 0.008517 -0.023596 -0.029146
			 0.000820 -0.031810 -0.027839
			 0.001588 -0.032244 -0.026451
			 0.002611 -0.032895 -0.025663
			 0.005322 -0.032505 -0.024623
			 0.006518 -0.033042 -0.024169
			 0.006484 -0.032176 -0.023850
			 0.001727 -0.031122 -0.026168
			 0.003109 -0.030473 -0.025443
			 0.004117 -0.030004 -0.025072
			 0.004893 -0.029025 -0.025964
			 0.005113 -0.028365 -0.026776
			 0.007475 -0.032043 -0.024457
			 0.005791 -0.030284 -0.023263
			 0.006465 -0.029637 -0.022920
			 0.006341 -0.031529 -0.023195
			 0.007264 -0.028923 -0.022708
			 0.006175 -0.030856 -0.023343
			 0.008671 -0.028301 -0.022976
			 0.010185 -0.026241 -0.024276
			 0.010347 -0.025020 -0.024621
			 0.010864 -0.023325 -0.028861
			 0.010434 -0.024618 -0.025273
			 0.009844 -0.023553 -0.028741
			 0.008574 -0.025761 -0.024329
			 0.008129 -0.025420 -0.027314
			 0.006712 -0.026085 -0.027459
			 0.001766 -0.031749 -0.026416
			 0.002846 -0.032068 -0.025068
			 0.004284 -0.031824 -0.024340
			 0.003150 -0.031065 -0.025114
			 0.004263 -0.030810 -0.024244
			 0.004717 -0.030005 -0.024146
			 0.006127 -0.028137 -0.026000
			 0.007839 -0.027403 -0.023007
			 0.006657 -0.026847 -0.026437
			 0.008124 -0.027671 -0.022668
			 0.008932 -0.026396 -0.023009
			 0.009624 -0.025609 -0.023823
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					1 0 4 
					4 0 3 
					1 5 2 
					6 1 4 
					5 1 6 
					7 8 2 
					2 8 3 
					2 9 10 
					7 2 10 
					2 5 11 
					2 11 9 
					8 12 3 
					3 13 14 
					15 3 14 
					4 3 15 
					13 3 12 
					16 4 15 
					4 16 6 
					17 11 5 
					6 17 5 
					18 6 19 
					20 17 6 
					20 6 18 
					16 19 6 
					7 21 8 
					7 22 21 
					7 23 24 
					22 7 24 
					7 10 23 
					8 21 12 
					10 9 25 
					9 11 17 
					17 25 9 
					23 10 26 
					26 10 25 
					21 27 12 
					13 12 28 
					28 12 27 
					29 14 13 
					13 30 29 
					28 31 13 
					30 13 31 
					15 14 16 
					32 16 14 
					29 32 14 
					16 32 19 
					17 33 34 
					34 25 17 
					17 20 35 
					33 17 35 
					18 19 36 
					18 36 20 
					19 32 36 
					35 20 37 
					38 37 20 
					39 38 20 
					20 40 39 
					40 20 36 
					21 22 41 
					21 41 42 
					21 43 27 
					21 42 43 
					22 24 44 
					45 22 44 
					45 41 22 
					24 23 46 
					47 23 26 
					48 46 23 
					47 49 23 
					50 48 23 
					23 49 50 
					44 24 46 
					26 25 47 
					47 25 34 
					43 28 27 
					31 28 43 
					29 51 32 
					29 30 51 
					51 30 52 
					53 30 31 
					52 30 53 
					54 31 43 
					54 55 31 
					55 53 31 
					51 56 32 
					32 56 36 
					34 33 57 
					57 33 58 
					33 35 59 
					33 59 60 
					60 58 33 
					34 57 47 
					59 35 61 
					62 61 35 
					62 35 37 
					56 40 36 
					37 63 64 
					64 62 37 
					38 63 37 
					39 65 38 
					38 65 63 
					39 66 67 
					39 68 66 
					69 65 39 
					70 68 39 
					67 69 39 
					39 40 70 
					70 40 56 
					71 41 45 
					71 42 41 
					42 72 73 
					73 43 42 
					72 42 71 
					43 73 74 
					54 43 74 
					75 45 44 
					44 46 75 
					71 45 76 
					76 45 75 
					77 46 48 
					46 77 78 
					75 46 78 
					49 47 57 
					50 77 48 
					50 49 79 
					79 49 57 
					77 50 80 
					80 50 81 
					82 50 79 
					83 81 50 
					50 82 84 
					83 50 84 
					56 51 85 
					51 52 86 
					87 85 51 
					87 51 86 
					86 52 88 
					88 52 53 
					55 89 53 
					90 53 89 
					90 88 53 
					91 54 74 
					91 55 54 
					91 92 55 
					93 89 55 
					92 93 55 
					85 70 56 
					58 82 57 
					82 79 57 
					58 94 82 
					58 95 94 
					58 96 95 
					58 60 96 
					97 98 59 
					59 99 100 
					60 59 101 
					59 98 101 
					59 61 99 
					100 97 59 
					102 60 101 
					60 102 96 
					61 103 99 
					104 61 62 
					104 103 61 
					64 105 62 
					62 106 104 
					62 107 106 
					62 105 107 
					63 108 64 
					65 108 63 
					108 109 64 
					64 109 105 
					108 65 69 
					67 66 110 
					66 111 110 
					66 112 111 
					66 68 112 
					113 109 67 
					67 109 69 
					67 110 113 
					114 115 68 
					70 114 68 
					68 115 112 
					109 108 69 
					114 70 85 
					72 71 76 
					72 116 73 
					117 118 72 
					72 118 116 
					72 76 117 
					116 119 73 
					73 119 120 
					74 73 120 
					74 120 91 
					76 75 117 
					75 78 117 
					77 121 122 
					121 77 80 
					122 78 77 
					122 117 78 
					81 121 80 
					121 81 123 
					124 125 81 
					123 81 125 
					81 83 124 
					126 84 82 
					126 82 127 
					94 127 82 
					126 124 83 
					84 126 83 
					85 87 128 
					114 85 128 
					86 129 130 
					87 86 130 
					129 86 88 
					130 128 87 
					90 129 88 
					131 90 89 
					131 89 93 
					131 129 90 
					132 91 120 
					91 132 92 
					92 132 93 
					133 93 134 
					93 133 135 
					134 93 136 
					132 136 93 
					131 93 135 
					94 95 137 
					137 127 94 
					95 96 138 
					95 138 139 
					140 95 139 
					141 95 140 
					141 137 95 
					138 96 142 
					96 102 143 
					96 143 142 
					98 97 144 
					97 100 145 
					145 144 97 
					146 147 98 
					98 147 101 
					98 144 146 
					99 148 125 
					125 149 99 
					100 99 150 
					149 150 99 
					148 99 151 
					99 103 151 
					145 100 150 
					101 152 102 
					147 152 101 
					153 102 154 
					154 102 152 
					143 102 153 
					155 103 104 
					103 155 151 
					104 106 155 
					105 156 107 
					105 157 156 
					157 105 109 
					106 158 123 
					106 123 155 
					158 106 107 
					107 159 158 
					159 107 160 
					107 156 160 
					113 157 109 
					113 110 161 
					110 111 162 
					161 110 162 
					111 163 162 
					112 163 111 
					112 115 163 
					157 113 164 
					164 113 161 
					165 115 114 
					165 114 128 
					115 166 163 
					115 167 166 
					115 165 167 
					116 136 119 
					136 116 134 
					118 168 116 
					134 116 168 
					118 117 169 
					117 122 169 
					168 118 170 
					118 169 171 
					170 118 171 
					136 120 119 
					136 132 120 
					122 121 123 
					122 123 172 
					172 169 122 
					158 172 123 
					155 123 173 
					173 123 125 
					124 174 125 
					174 124 126 
					125 148 173 
					149 125 175 
					125 174 175 
					176 126 177 
					126 176 174 
					177 126 127 
					127 137 177 
					128 130 165 
					130 129 178 
					129 131 178 
					130 178 165 
					131 179 178 
					135 179 131 
					134 180 133 
					133 181 135 
					181 133 182 
					183 133 180 
					133 183 182 
					180 134 168 
					184 179 135 
					135 181 185 
					184 135 185 
					177 137 186 
					186 137 141 
					142 187 138 
					188 138 189 
					188 139 138 
					187 189 138 
					139 188 190 
					190 140 139 
					141 140 190 
					186 141 190 
					191 187 142 
					143 191 142 
					153 191 143 
					145 146 144 
					192 146 145 
					145 150 193 
					192 145 193 
					147 146 192 
					192 152 147 
					173 148 151 
					150 149 194 
					149 175 194 
					195 196 150 
					194 197 150 
					150 196 193 
					150 197 195 
					173 151 155 
					152 192 154 
					154 198 153 
					153 198 191 
					198 154 199 
					200 201 154 
					154 192 200 
					199 154 201 
					156 202 160 
					156 203 202 
					156 157 203 
					204 203 157 
					204 157 164 
					158 159 172 
					170 159 205 
					171 159 170 
					171 172 159 
					205 159 160 
					205 160 202 
					161 162 206 
					164 161 206 
					206 162 207 
					162 166 208 
					207 162 208 
					163 166 162 
					209 164 206 
					209 204 164 
					210 167 165 
					165 179 210 
					165 178 179 
					167 208 166 
					167 210 208 
					168 211 180 
					211 168 170 
					169 172 171 
					205 211 170 
					174 176 175 
					194 175 212 
					175 176 212 
					213 176 177 
					213 212 176 
					177 214 213 
					186 215 177 
					214 177 215 
					179 184 210 
					180 216 217 
					217 183 180 
					216 180 218 
					180 211 218 
					207 185 181 
					207 181 182 
					217 219 182 
					182 219 207 
					183 217 182 
					207 184 185 
					207 208 184 
					210 184 208 
					220 186 221 
					186 220 215 
					190 221 186 
					222 187 191 
					223 189 187 
					187 222 223 
					189 224 188 
					224 225 188 
					188 225 226 
					190 188 226 
					224 189 227 
					189 223 227 
					228 190 226 
					228 221 190 
					191 198 222 
					192 193 200 
					196 200 193 
					194 229 197 
					229 194 212 
					230 196 195 
					231 230 195 
					197 231 195 
					200 196 230 
					197 232 233 
					197 234 232 
					234 197 229 
					231 197 233 
					199 235 198 
					235 236 198 
					236 222 198 
					201 237 199 
					199 237 235 
					200 238 201 
					230 238 200 
					238 239 201 
					201 239 240 
					240 237 201 
					202 211 205 
					211 202 218 
					241 218 202 
					241 202 203 
					204 241 203 
					242 241 204 
					242 204 209 
					209 206 219 
					219 206 207 
					242 209 243 
					243 209 219 
					244 229 212 
					212 213 245 
					212 245 244 
					246 213 214 
					213 246 245 
					247 246 214 
					214 220 247 
					220 214 215 
					217 216 243 
					242 216 241 
					241 216 218 
					216 242 243 
					243 219 217 
					248 220 221 
					249 220 250 
					249 247 220 
					220 248 250 
					221 251 248 
					228 251 221 
					236 223 222 
					223 252 227 
					235 223 236 
					235 252 223 
					253 225 224 
					224 227 253 
					226 225 254 
					225 253 254 
					255 228 226 
					256 255 226 
					226 254 256 
					227 254 253 
					227 257 254 
					252 257 227 
					255 251 228 
					244 234 229 
					238 230 258 
					258 230 231 
					258 231 259 
					231 233 260 
					231 260 259 
					261 233 232 
					234 262 232 
					263 232 262 
					263 261 232 
					260 233 261 
					262 234 244 
					252 235 237 
					264 265 237 
					240 264 237 
					266 237 265 
					267 237 266 
					267 268 237 
					252 237 268 
					240 239 238 
					258 240 238 
					264 240 258 
					269 262 244 
					245 269 244 
					245 246 269 
					270 246 271 
					272 269 246 
					271 246 273 
					246 270 274 
					246 274 272 
					275 273 246 
					246 247 276 
					246 276 275 
					277 247 249 
					276 247 277 
					248 251 278 
					248 279 250 
					280 248 278 
					281 279 248 
					282 281 248 
					282 248 280 
					279 249 250 
					281 277 249 
					279 281 249 
					251 255 278 
					252 268 283 
					252 283 257 
					257 256 254 
					255 256 280 
					280 278 255 
					256 257 280 
					280 257 282 
					282 257 283 
					258 259 284 
					258 284 264 
					259 260 285 
					285 284 259 
					285 260 261 
					272 261 263 
					261 272 286 
					285 261 286 
					269 263 262 
					269 272 263 
					265 264 287 
					287 264 284 
					288 266 265 
					265 287 288 
					266 288 270 
					266 270 289 
					266 289 267 
					268 267 290 
					267 289 291 
					290 267 291 
					292 283 268 
					290 292 268 
					289 270 271 
					270 288 274 
					273 291 271 
					271 291 289 
					288 286 272 
					274 288 272 
					273 275 293 
					291 273 293 
					293 275 294 
					276 294 275 
					294 276 295 
					295 276 277 
					277 281 295 
					295 281 291 
					292 291 281 
					282 292 281 
					283 292 282 
					285 287 284 
					286 287 285 
					287 286 288 
					291 292 290 
					291 294 295 
					291 293 294 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 1275 1278 1281 1284 1287 1290 1293 1296 1299 1302 1305 1308 1311 1314 1317 1320 1323 1326 1329 1332 1335 1338 1341 1344 1347 1350 1353 1356 1359 1362 1365 1368 1371 1374 1377 1380 1383 1386 1389 1392 1395 1398 1401 1404 1407 1410 1413 1416 1419 1422 1425 1428 1431 1434 1437 1440 1443 1446 1449 1452 1455 1458 1461 1464 1467 1470 1473 1476 1479 1482 1485 1488 1491 1494 1497 1500 1503 1506 1509 1512 1515 1518 1521 1524 1527 1530 1533 1536 1539 1542 1545 1548 1551 1554 1557 1560 1563 1566 1569 1572 1575 1578 1581 1584 1587 1590 1593 1596 1599 1602 1605 1608 1611 1614 1617 1620 1623 1626 1629 1632 1635 1638 1641 1644 1647 1650 1653 1656 1659 1662 1665 1668 1671 1674 1677 1680 1683 1686 1689 1692 1695 1698 1701 1704 1707 1710 1713 1716 1719 1722 1725 1728 1731 1734 1737 1740 1743 1746 1749 1752 1755 1758 1761 1764 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
