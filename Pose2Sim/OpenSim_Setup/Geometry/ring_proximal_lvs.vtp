<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="132" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="260">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.195994 -0.245294 -0.949430
			 0.101121 -0.299078 -0.948856
			-0.417953 -0.535714 -0.733707
			-0.699365 -0.447590 -0.557271
			-0.126201  0.113597 -0.985479
			-0.396344 -0.181581 -0.899967
			-0.298059  0.128913 -0.945802
			 0.333511 -0.568180 -0.752291
			 0.831680  0.053656 -0.552657
			 0.881003 -0.272577 -0.386698
			 0.306434 -0.004396 -0.951882
			-0.096761 -0.859421 -0.502030
			-0.696156 -0.716941  0.036912
			-0.190262 -0.981691  0.009129
			-0.806230 -0.384179 -0.449890
			-0.807005 -0.306932  0.504515
			-0.223415  0.768584 -0.599469
			 0.254351  0.888073 -0.382925
			 0.675644  0.549500 -0.491482
			-0.452849  0.279438 -0.846665
			 0.385002 -0.902989 -0.190745
			 0.804586 -0.563749 -0.186625
			 0.978669  0.200278  0.045785
			 0.934292 -0.322989  0.150921
			-0.703978 -0.584123  0.403999
			-0.106441 -0.990500  0.087061
			 0.567631 -0.802054  0.185756
			-0.163033 -0.204285 -0.965240
			-0.851466 -0.516408  0.091261
			-0.772308 -0.482409 -0.413306
			-0.267807  0.193773  0.943786
			-0.370753 -0.095405  0.923818
			-0.725941 -0.387556  0.568164
			-0.434236  0.205421  0.877064
			-0.408768 -0.113191  0.905592
			-0.416147  0.073446  0.906326
			 0.183291  0.672859 -0.716705
			 0.765042  0.643160 -0.032488
			 0.632584  0.767760 -0.101891
			 0.999644  0.026667 -0.001094
			 0.844273 -0.532171  0.063221
			-0.242340 -0.967782  0.068337
			-0.249370 -0.705220  0.663687
			-0.366814 -0.305573  0.878676
			 0.489397 -0.842557  0.224918
			 0.525346 -0.844560  0.103585
			-0.811435 -0.507091 -0.290572
			-0.170197 -0.263051 -0.949651
			 0.462192  0.048650 -0.885444
			 0.760304  0.302177 -0.575002
			-0.850287 -0.464812  0.246903
			 0.707726  0.424690  0.564590
			 0.221804  0.720700  0.656806
			-0.252463  0.572105  0.780358
			-0.670101 -0.292106  0.682377
			-0.307788 -0.141890  0.940816
			 0.481456  0.196257  0.854215
			-0.251661  0.470788  0.845592
			 0.189172 -0.247729  0.950181
			-0.090857  0.312313  0.945624
			 0.211042  0.317246  0.924563
			 0.196129  0.087566  0.976660
			 0.981715  0.187670 -0.031867
			 0.767513  0.640829  0.016173
			 0.505298  0.862528  0.026812
			 0.634353  0.733356  0.244510
			 0.923848 -0.373266 -0.084726
			 0.854559 -0.306935  0.418950
			-0.213804 -0.455628 -0.864113
			-0.843635 -0.460369  0.276297
			-0.731973 -0.440433  0.519841
			-0.912033 -0.316326  0.261024
			-0.951748 -0.283129 -0.118380
			-0.697920 -0.425084 -0.576378
			 0.340656 -0.164564 -0.925674
			 0.893016  0.338785 -0.296221
			 0.891684  0.152794 -0.426090
			-0.675614 -0.331416  0.658566
			 0.889085  0.410414  0.202702
			 0.024654  0.817568  0.575304
			-0.095658 -0.122717  0.987821
			 0.884362  0.098377  0.456317
			 0.588637  0.110065  0.800870
			 0.876181  0.361975  0.318246
			 0.810652  0.257942  0.525652
			 0.426303  0.764038  0.484264
			 0.636423  0.623751  0.453762
			 0.375481 -0.268805 -0.886994
			-0.169274 -0.175443 -0.969828
			-0.713088 -0.111609  0.692134
			-0.938437  0.344136 -0.030124
			-0.962143  0.243240  0.122942
			-0.918562  0.009199 -0.395171
			-0.707608  0.083623 -0.701639
			 0.623948  0.015427 -0.781313
			 0.993251  0.100826 -0.057330
			 0.926371  0.330927  0.179786
			-0.348505  0.211576  0.913116
			-0.159074  0.324292  0.932486
			 0.580749 -0.100627  0.807840
			 0.331093  0.099959  0.938289
			 0.016291  0.360748  0.932521
			 0.984480 -0.045874  0.169398
			 0.854518 -0.143345  0.499250
			 0.979678  0.017419  0.199821
			-0.245742  0.309405 -0.918629
			 0.036162  0.313818 -0.948794
			-0.822699  0.563525 -0.074871
			-0.738851  0.581118  0.341176
			-0.738228  0.673601 -0.035793
			-0.589623  0.779767 -0.210496
			-0.544523  0.835564  0.072989
			-0.366394  0.653783 -0.662060
			 0.856620  0.499321 -0.129925
			 0.423156  0.495104 -0.758822
			 0.992325  0.119064  0.033375
			 0.999347 -0.011399 -0.034284
			-0.273690  0.817245  0.507154
			-0.213159  0.483229  0.849148
			 0.881767  0.135437  0.451823
			 0.555416  0.639420  0.531652
			 0.233918  0.552296  0.800157
			-0.008960  0.618121  0.786032
			-0.091419  0.400728 -0.911625
			 0.050023  0.630007 -0.774977
			-0.481231  0.768824 -0.421103
			-0.158287  0.976860 -0.143841
			-0.592033  0.768606 -0.242365
			 0.352496  0.923664  0.150303
			 0.028763  0.998768 -0.040435
			 0.110228  0.895901  0.430361
			-0.343012  0.923285 -0.172881
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.022458 -0.138517  0.021596
			 0.025034 -0.139125  0.020639
			 0.021265 -0.141519  0.022207
			 0.019282 -0.139529  0.022141
			 0.021487 -0.134385  0.020179
			 0.019237 -0.136079  0.020938
			 0.019741 -0.134191  0.020685
			 0.024638 -0.140979  0.021391
			 0.026101 -0.137579  0.021157
			 0.026131 -0.138686  0.021472
			 0.024622 -0.137040  0.020013
			 0.022149 -0.141916  0.022124
			 0.020107 -0.141010  0.023920
			 0.021880 -0.142144  0.023804
			 0.018020 -0.136276  0.022307
			 0.017849 -0.135059  0.028388
			 0.020474 -0.133904  0.020560
			 0.022549 -0.133536  0.021099
			 0.024960 -0.135212  0.020659
			 0.018857 -0.134441  0.021095
			 0.024661 -0.141766  0.022827
			 0.025439 -0.140850  0.022311
			 0.026346 -0.137626  0.023487
			 0.026071 -0.139533  0.024088
			 0.020951 -0.140513  0.029832
			 0.021985 -0.141572  0.026450
			 0.024672 -0.141700  0.023711
			 0.016815 -0.130866  0.020821
			 0.013779 -0.129479  0.025319
			 0.012740 -0.127234  0.022320
			 0.018592 -0.133175  0.029488
			 0.014818 -0.125887  0.028232
			 0.013227 -0.127168  0.026861
			 0.020019 -0.134342  0.030413
			 0.020080 -0.136661  0.030335
			 0.020495 -0.134957  0.030732
			 0.020600 -0.133397  0.021627
			 0.024846 -0.135452  0.025825
			 0.021264 -0.132925  0.023394
			 0.025728 -0.137459  0.025992
			 0.024986 -0.140214  0.026178
			 0.022374 -0.141935  0.028884
			 0.022421 -0.141624  0.030204
			 0.022151 -0.140333  0.030864
			 0.024420 -0.141374  0.030391
			 0.024291 -0.141039  0.026249
			 0.002104 -0.109936  0.020569
			 0.009961 -0.117323  0.018846
			 0.013471 -0.120273  0.019564
			 0.016795 -0.125353  0.020981
			 0.007032 -0.118814  0.024936
			 0.018774 -0.129593  0.028922
			 0.020115 -0.132449  0.029181
			 0.019791 -0.133107  0.029651
			 0.007330 -0.118646  0.025990
			 0.008414 -0.114314  0.027148
			 0.009490 -0.111167  0.027440
			 0.020576 -0.133326  0.030153
			 0.025041 -0.139101  0.031531
			 0.022052 -0.134453  0.031080
			 0.024458 -0.136236  0.031511
			 0.025044 -0.137185  0.031613
			 0.026295 -0.137167  0.028366
			 0.025559 -0.135869  0.028411
			 0.022635 -0.133231  0.028088
			 0.020632 -0.132420  0.028487
			 0.026097 -0.139498  0.028436
			 0.025660 -0.139621  0.030856
			 0.007294 -0.111250  0.017456
			 0.005533 -0.116082  0.024518
			 0.002686 -0.110957  0.025105
			 0.000557 -0.105481  0.026281
			-0.000034 -0.105851  0.020776
			 0.001356 -0.107346  0.018258
			 0.009330 -0.110873  0.017564
			 0.014386 -0.119624  0.020604
			 0.010823 -0.110735  0.018941
			 0.005984 -0.115719  0.025445
			 0.011004 -0.112349  0.025443
			 0.020955 -0.132649  0.029700
			 0.002660 -0.102577  0.028845
			 0.008510 -0.107439  0.026743
			 0.008276 -0.108342  0.027474
			 0.010003 -0.110776  0.026308
			 0.025929 -0.137302  0.030931
			 0.023118 -0.133775  0.030436
			 0.024343 -0.134752  0.030646
			 0.008170 -0.107274  0.015829
			 0.004401 -0.104010  0.014096
			 0.001016 -0.103641  0.027754
			-0.000401 -0.103559  0.022298
			-0.000207 -0.102877  0.025420
			 0.000887 -0.103063  0.016860
			 0.001612 -0.102240  0.015848
			 0.008804 -0.103296  0.015315
			 0.009747 -0.105433  0.017170
			 0.009725 -0.109909  0.025522
			 0.002443 -0.101502  0.028778
			 0.003836 -0.100113  0.028737
			 0.007888 -0.105701  0.027877
			 0.007947 -0.101925  0.028293
			 0.004818 -0.099708  0.028635
			 0.009574 -0.106012  0.025089
			 0.009138 -0.105837  0.026441
			 0.009300 -0.107684  0.025276
			 0.003765 -0.101454  0.014775
			 0.006174 -0.101189  0.014445
			 0.000473 -0.101372  0.026470
			 0.001528 -0.099713  0.027893
			 0.001080 -0.101456  0.017848
			 0.000826 -0.102131  0.024598
			 0.000669 -0.102672  0.021366
			 0.003247 -0.099667  0.015444
			 0.009299 -0.099955  0.017378
			 0.007153 -0.101021  0.014824
			 0.009163 -0.103094  0.023999
			 0.009457 -0.106047  0.024064
			 0.003103 -0.098899  0.027981
			 0.003599 -0.099690  0.028295
			 0.008599 -0.101702  0.027831
			 0.007679 -0.099670  0.027561
			 0.005920 -0.099566  0.028379
			 0.004505 -0.099158  0.028165
			 0.004685 -0.100510  0.015285
			 0.005469 -0.099357  0.015524
			 0.002130 -0.100784  0.025881
			 0.007707 -0.098457  0.018271
			 0.004737 -0.099911  0.023549
			 0.006281 -0.098554  0.026561
			 0.006560 -0.098252  0.023328
			 0.005022 -0.098517  0.027429
			 0.004566 -0.098544  0.026740
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					1 0 4 
					0 3 5 
					0 6 4 
					0 5 6 
					2 1 7 
					8 9 1 
					10 1 4 
					1 10 8 
					1 9 7 
					2 7 11 
					3 2 12 
					13 2 11 
					13 12 2 
					5 3 14 
					3 12 15 
					15 14 3 
					6 16 4 
					4 16 17 
					4 17 18 
					18 10 4 
					14 19 5 
					6 5 19 
					6 19 16 
					11 7 20 
					21 20 7 
					21 7 9 
					22 9 8 
					8 18 22 
					18 8 10 
					9 22 23 
					23 21 9 
					13 11 20 
					15 12 24 
					24 12 25 
					25 12 13 
					25 13 26 
					20 26 13 
					19 14 27 
					14 15 28 
					27 14 29 
					28 29 14 
					15 30 31 
					32 28 15 
					31 32 15 
					33 30 15 
					15 24 34 
					35 15 34 
					35 33 15 
					17 16 36 
					19 36 16 
					17 37 18 
					37 17 38 
					38 17 36 
					22 18 37 
					19 27 36 
					26 20 21 
					26 21 23 
					39 23 22 
					39 22 37 
					23 40 26 
					23 39 40 
					41 42 24 
					41 24 25 
					24 43 34 
					24 42 43 
					44 25 45 
					41 25 44 
					26 45 25 
					40 45 26 
					46 47 27 
					27 29 46 
					27 47 48 
					27 48 49 
					27 49 36 
					28 46 29 
					50 46 28 
					32 50 28 
					30 51 31 
					30 52 51 
					30 53 52 
					30 33 53 
					54 31 55 
					54 32 31 
					31 51 56 
					56 55 31 
					50 32 54 
					33 57 53 
					33 35 57 
					34 43 58 
					59 34 60 
					60 34 61 
					34 58 61 
					34 59 35 
					59 57 35 
					38 36 49 
					37 62 39 
					63 37 64 
					62 37 63 
					37 38 64 
					38 51 65 
					38 49 51 
					38 65 64 
					40 39 66 
					39 62 66 
					40 44 45 
					40 66 44 
					41 44 42 
					44 58 42 
					42 58 43 
					58 44 67 
					44 66 67 
					46 68 47 
					46 50 69 
					46 69 70 
					46 71 72 
					46 70 71 
					46 73 68 
					46 72 73 
					74 47 68 
					47 74 48 
					49 48 75 
					48 76 75 
					76 48 74 
					49 75 51 
					77 69 50 
					54 77 50 
					56 51 78 
					65 51 52 
					78 51 75 
					52 64 65 
					64 52 79 
					53 79 52 
					53 57 79 
					55 77 54 
					56 80 55 
					77 55 80 
					56 81 82 
					56 83 81 
					80 56 82 
					78 83 56 
					59 79 57 
					58 84 61 
					84 58 67 
					60 85 59 
					59 85 79 
					86 85 60 
					60 84 86 
					61 84 60 
					84 67 62 
					62 67 66 
					62 63 84 
					63 64 85 
					63 85 86 
					63 86 84 
					64 79 85 
					87 74 68 
					88 68 73 
					68 88 87 
					77 70 69 
					89 70 80 
					70 77 80 
					70 89 71 
					90 72 71 
					91 90 71 
					89 91 71 
					90 92 72 
					73 72 92 
					92 93 73 
					73 93 88 
					74 87 76 
					78 75 76 
					76 94 95 
					76 95 96 
					78 76 96 
					94 76 87 
					83 78 96 
					80 97 89 
					97 80 98 
					80 82 99 
					80 99 100 
					80 100 101 
					80 101 98 
					82 81 99 
					81 102 103 
					103 99 81 
					81 83 96 
					102 81 104 
					104 81 96 
					88 94 87 
					88 105 106 
					105 88 93 
					106 94 88 
					107 89 108 
					89 107 91 
					108 89 97 
					90 109 92 
					110 90 91 
					110 111 90 
					90 111 109 
					110 91 107 
					93 92 109 
					93 112 105 
					93 109 112 
					94 113 95 
					94 114 113 
					114 94 106 
					115 116 95 
					95 116 104 
					96 95 104 
					113 115 95 
					117 108 97 
					97 98 118 
					97 118 117 
					101 118 98 
					99 119 100 
					119 99 103 
					100 120 121 
					120 100 119 
					101 100 121 
					121 122 101 
					101 122 118 
					103 102 119 
					104 116 102 
					102 115 119 
					102 116 115 
					123 106 105 
					105 112 123 
					123 124 106 
					124 114 106 
					125 107 108 
					107 125 110 
					125 108 117 
					109 126 112 
					126 109 111 
					110 127 126 
					111 110 126 
					110 125 127 
					123 112 124 
					126 124 112 
					115 113 119 
					113 128 120 
					113 120 119 
					129 128 113 
					129 113 126 
					126 113 114 
					114 124 126 
					117 122 130 
					122 117 118 
					131 125 117 
					117 130 131 
					130 121 120 
					128 130 120 
					130 122 121 
					127 125 131 
					127 129 126 
					131 129 127 
					131 130 128 
					128 129 131 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
