<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="142" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="216">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.020927  0.999781  0.000000
			 0.987336 -0.158646  0.000000
			 0.071143  0.957425  0.279778
			 0.024535  0.999178 -0.032283
			 0.066738  0.959576 -0.273423
			 0.578031 -0.815705 -0.022478
			 0.916176 -0.202356  0.345940
			 0.343372 -0.939199  0.000000
			 0.916176 -0.202357 -0.345938
			 0.526278 -0.847194 -0.072756
			 0.518410  0.852947  0.061085
			-0.607122  0.702721  0.370926
			-0.715267  0.504437 -0.483669
			-0.185508  0.606711 -0.772974
			 0.014883  0.466824 -0.884225
			 0.071006  0.520771 -0.850738
			 0.121825  0.992428 -0.015689
			 0.092967  0.534015  0.840348
			 0.041148  0.499205  0.865507
			-0.122518  0.417646  0.900312
			-0.754440  0.316642  0.574941
			 0.473755  0.802431 -0.362851
			-0.693691  0.648618 -0.313191
			 0.696785 -0.629468  0.343891
			-0.460591 -0.608901 -0.645829
			-0.793365  0.186606 -0.579439
			-0.300567 -0.953761 -0.000006
			-0.402973 -0.762588  0.506035
			 0.395486 -0.648772 -0.650143
			-0.876257  0.157651  0.455324
			 0.041311  0.936713  0.347652
			 0.409760  0.886175  0.216312
			-0.502097  0.858759 -0.102136
			-0.239981 -0.587467 -0.772846
			-0.019564 -0.594822 -0.803620
			-0.373750 -0.236466 -0.896880
			-0.366799  0.472338 -0.801470
			-0.956742  0.286791  0.048948
			-0.347399  0.410733  0.842978
			-0.373748 -0.236465  0.896882
			 0.007725 -0.615749  0.787904
			-0.086884 -0.762657  0.640942
			-0.559419  0.815159  0.150221
			 0.253384  0.821143 -0.511391
			 0.018934  0.563850 -0.825660
			-0.386676  0.537408  0.749449
			-0.701438 -0.576493 -0.419094
			 0.337890 -0.881361 -0.330201
			-0.726780  0.022155 -0.686513
			-0.000822 -1.000000 -0.000003
			-0.735921  0.565159 -0.372849
			-0.777527  0.608259 -0.159602
			-0.262753  0.326489 -0.907946
			-0.701718 -0.576276  0.418924
			 0.326431 -0.900203  0.288233
			-0.782640  0.005237  0.622452
			-0.735920  0.565160  0.372849
			-0.777533  0.608251  0.159604
			-0.508917  0.794918  0.330317
			-0.330629  0.912070  0.242515
			-0.283666  0.957956  0.043054
			 0.095960  0.938722 -0.331047
			 0.409513  0.804306  0.430571
			-0.677460  0.719545 -0.152655
			 0.461771  0.790464  0.402410
			 0.046030 -0.998940  0.000008
			-0.694373 -0.719615  0.000006
			-0.998032 -0.062334 -0.006850
			-0.998032 -0.062334  0.006850
			-0.704888  0.685977  0.180469
			 0.356129  0.869815 -0.341459
			 0.239398  0.903700 -0.354986
			-0.379099  0.920266  0.096925
			-0.066100  0.926175  0.371257
			-0.594688  0.775199 -0.213103
			-0.615640  0.702758 -0.356537
			-0.912392 -0.385381  0.137921
			 0.443860 -0.820187 -0.360945
			 0.256198 -0.707051 -0.659122
			 0.097952 -0.452359 -0.886440
			 0.156997 -0.510115 -0.845657
			 0.230779  0.769674 -0.595267
			-0.173811  0.546381 -0.819303
			 0.727712  0.623633  0.285512
			 0.669002  0.502115 -0.548011
			-0.310405 -0.459890 -0.831955
			-0.202447 -0.907962 -0.366906
			 0.371406  0.831545 -0.413026
			 0.557972  0.828806 -0.041808
			 0.013716 -0.931945 -0.362341
			 0.297003 -0.840183 -0.453743
			-0.575274  0.476661 -0.664721
			-0.910487 -0.406613 -0.075356
			 0.236022 -0.723880  0.648299
			 0.432669 -0.834524  0.341127
			 0.100541 -0.449900  0.887401
			 0.157313 -0.508239  0.846727
			 0.195401  0.763391  0.615673
			-0.210526  0.735579  0.643897
			 0.641819  0.668578  0.375595
			 0.674022  0.662616 -0.326549
			-0.310409 -0.459888  0.831955
			-0.096375 -0.929942  0.354851
			 0.354187  0.933464 -0.056547
			 0.248810  0.852691  0.459359
			 0.247603 -0.805064  0.539041
			 0.154598 -0.901424  0.404394
			-0.575276  0.476664  0.664717
			 0.429778  0.796031  0.426176
			 0.360660  0.899188  0.247761
			 0.462331  0.886014  0.035054
			-0.208098  0.880353  0.426231
			-0.995492 -0.082271  0.047201
			-0.160802  0.681919 -0.713533
			-0.171427  0.614010  0.770457
			 0.139134  0.978205  0.154130
			 0.185287  0.836888 -0.515061
			 0.367410  0.894485 -0.254769
			-0.235261  0.897487 -0.373055
			 0.764136 -0.629809 -0.139415
			 0.519899 -0.851909 -0.062894
			 0.920978  0.355524 -0.159380
			 0.749803  0.451572 -0.483611
			 0.790607  0.432687 -0.433270
			 0.701001  0.297202 -0.648281
			 0.864978  0.420836 -0.273331
			 0.780988  0.614319  0.112559
			 0.502908  0.117031 -0.856381
			 0.862192  0.296110 -0.411028
			 0.776791 -0.626971  0.059185
			 0.923385  0.335594  0.186380
			 0.753044  0.433148  0.495285
			 0.821348  0.415687  0.390631
			 0.630773  0.337395  0.698778
			 0.729206  0.650688  0.211809
			 0.708286  0.662610 -0.243473
			 0.578708  0.163379  0.799002
			 0.858518  0.451888  0.242371
			 0.713947  0.609483 -0.344689
			 0.124071  0.991289  0.044182
			 0.879626  0.475067  0.023864
			 0.742104  0.601325  0.296125
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.019495  0.041165  0.000150
			-0.008393  0.041518  0.000150
			-0.007746  0.041565 -0.004180
			-0.020456  0.041238  0.000150
			-0.007746  0.041565  0.004480
			-0.014592  0.026954 -0.002122
			-0.008682  0.040887 -0.001485
			-0.014594  0.026952  0.000150
			-0.008682  0.040887  0.001786
			-0.014592  0.026954  0.002422
			-0.002206  0.042833 -0.012190
			-0.008703  0.041686 -0.004960
			-0.011746  0.036555 -0.011813
			-0.023938  0.037140 -0.005270
			-0.031296  0.039028 -0.003752
			-0.036785  0.040326 -0.003527
			-0.036448  0.042339  0.000150
			-0.036785  0.040326  0.003828
			-0.031296  0.039028  0.004053
			-0.023938  0.037140  0.005571
			-0.011746  0.036555  0.012113
			-0.002206  0.042833  0.012491
			-0.008703  0.041686  0.005260
			-0.008079  0.030870 -0.009838
			-0.024844  0.032131 -0.006679
			-0.014810  0.029535 -0.012267
			-0.024849  0.032127  0.000150
			-0.024844  0.032131  0.006979
			-0.008079  0.030870  0.010139
			-0.014810  0.029535  0.012567
			 0.001252  0.037510 -0.012269
			 0.000253  0.038379 -0.013732
			-0.004716  0.039888 -0.022884
			-0.030817  0.032992 -0.005751
			-0.037503  0.032039 -0.005593
			-0.041608  0.032242 -0.005446
			-0.044123  0.040093 -0.004316
			-0.042630  0.044151  0.000150
			-0.044123  0.040093  0.004616
			-0.041608  0.032242  0.005746
			-0.037503  0.032039  0.005893
			-0.030817  0.032992  0.006052
			-0.004716  0.039888  0.023185
			 0.000253  0.038379  0.014032
			 0.001252  0.037510  0.012570
			 0.003817  0.031120 -0.010931
			-0.000374  0.026520 -0.009825
			 0.003446  0.037123 -0.021423
			-0.012261  0.029979 -0.023082
			-0.030821  0.032988  0.000150
			-0.008493  0.035308 -0.024598
			-0.013368  0.029939 -0.017400
			 0.003817  0.031120  0.011231
			-0.000374  0.026520  0.010126
			 0.003446  0.037123  0.021724
			-0.012261  0.029979  0.023383
			-0.008493  0.035308  0.024898
			-0.013368  0.029939  0.017700
			 0.007145  0.040704 -0.009892
			 0.007418  0.042090 -0.010987
			 0.004480  0.037822 -0.013145
			 0.005914  0.037915 -0.016647
			 0.002642  0.041462 -0.018870
			-0.002032  0.040403 -0.025530
			 0.005964  0.048380 -0.026828
			-0.037507  0.032035  0.000150
			-0.041612  0.032238  0.000150
			-0.045628  0.035160 -0.004210
			-0.045628  0.035160  0.004511
			-0.002032  0.040403  0.025830
			 0.005964  0.048380  0.027129
			 0.002642  0.041462  0.019171
			 0.004480  0.037822  0.013446
			 0.005914  0.037915  0.016948
			 0.007418  0.042090  0.011288
			 0.007145  0.040704  0.010193
			 0.000697  0.025128 -0.004101
			 0.018819  0.019468 -0.008543
			 0.016109  0.021535 -0.015003
			 0.012599  0.022757 -0.016757
			 0.010362  0.028717 -0.018399
			 0.005609  0.035115 -0.018276
			 0.003711  0.046731 -0.029154
			 0.003493  0.036789 -0.025087
			 0.005043  0.044161 -0.031712
			 0.001822  0.036232 -0.034068
			 0.004887  0.033744 -0.030447
			 0.008631  0.034286 -0.019549
			 0.008863  0.033219 -0.023495
			 0.007753  0.032280 -0.027731
			 0.010055  0.031169 -0.022693
			-0.000191  0.039465 -0.029666
			 0.000697  0.025128  0.004401
			 0.016109  0.021535  0.015303
			 0.018819  0.019468  0.008844
			 0.012599  0.022757  0.017058
			 0.010362  0.028717  0.018699
			 0.005609  0.035115  0.018576
			 0.003711  0.046731  0.029454
			 0.005043  0.044161  0.032012
			 0.003493  0.036789  0.025388
			 0.001822  0.036232  0.034369
			 0.004887  0.033744  0.030748
			 0.008863  0.033219  0.023796
			 0.008631  0.034286  0.019850
			 0.010055  0.031169  0.022994
			 0.007753  0.032280  0.028032
			-0.000191  0.039465  0.029967
			 0.010163  0.044140 -0.011666
			 0.009401  0.039242 -0.005159
			 0.011340  0.042878 -0.014329
			 0.008378  0.039196 -0.002390
			 0.000531  0.024452  0.000150
			 0.010258  0.042287 -0.015713
			 0.010258  0.042287  0.016013
			 0.011340  0.042878  0.014630
			 0.010163  0.044140  0.011966
			 0.009401  0.039242  0.005460
			 0.008378  0.039196  0.002691
			 0.020089  0.019193 -0.002163
			 0.020101  0.019342  0.000150
			 0.021905  0.033097 -0.007528
			 0.018946  0.035033 -0.011725
			 0.016955  0.036964 -0.013341
			 0.012715  0.035154 -0.017268
			 0.010309  0.032475 -0.023287
			 0.006278  0.035464 -0.025842
			 0.004308  0.036858 -0.033396
			 0.007782  0.033584 -0.026808
			 0.020089  0.019193  0.002464
			 0.021905  0.033097  0.007829
			 0.018946  0.035033  0.012025
			 0.016955  0.036964  0.013641
			 0.012715  0.035154  0.017569
			 0.010309  0.032475  0.023587
			 0.006278  0.035464  0.026143
			 0.004308  0.036858  0.033696
			 0.007782  0.033584  0.027108
			 0.013217  0.041305 -0.013436
			 0.010813  0.037542  0.000150
			 0.022762  0.032774  0.000150
			 0.013217  0.041305  0.013736
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					4 1 0 
					0 3 4 
					5 6 1 
					7 5 1 
					1 6 2 
					1 8 9 
					1 9 7 
					4 8 1 
					10 2 6 
					10 11 2 
					2 12 13 3 
					12 2 11 
					3 14 15 16 
					13 14 3 
					16 17 18 3 
					3 18 19 
					3 19 20 4 
					8 4 21 
					4 22 21 
					22 4 20 
					23 6 5 
					23 5 24 25 
					24 5 7 26 
					10 6 23 
					26 7 9 27 
					28 8 21 
					9 8 28 
					29 27 9 28 
					30 31 10 23 
					10 32 12 11 
					10 31 32 
					13 12 24 33 
					12 25 24 
					12 32 25 
					14 13 33 34 
					14 34 35 
					15 14 35 
					16 15 36 37 
					35 36 15 
					37 38 17 16 
					39 18 17 
					17 38 39 
					39 40 18 
					40 41 19 18 
					41 27 20 19 
					22 20 42 21 
					27 29 20 
					29 42 20 
					28 21 43 44 
					42 43 21 
					30 23 45 
					45 23 46 
					47 46 23 
					47 23 25 48 
					49 33 24 26 
					25 32 50 
					25 50 51 
					25 51 48 
					26 27 41 49 
					52 28 44 
					53 28 52 
					28 53 54 
					55 29 28 54 
					56 42 29 
					57 56 29 
					55 57 29 
					58 59 30 45 
					59 60 30 
					60 31 30 
					61 62 31 60 
					62 32 31 
					32 63 50 
					32 62 64 63 
					65 34 33 49 
					66 35 34 65 
					66 37 35 
					67 35 37 
					67 36 35 
					36 67 37 
					39 37 66 
					37 68 38 
					37 39 68 
					39 38 68 
					65 40 39 66 
					49 41 40 65 
					56 69 42 
					69 70 71 42 
					43 42 71 
					72 43 71 73 
					44 43 72 
					52 44 74 75 
					44 72 74 
					58 45 46 
					58 46 76 
					77 76 46 78 
					78 46 79 
					79 46 80 
					46 47 80 
					81 47 62 61 
					47 64 62 
					47 82 64 
					47 83 84 82 
					47 48 85 
					47 85 86 
					83 47 87 88 
					47 81 87 
					89 90 47 
					90 80 47 
					47 86 89 
					51 50 48 
					50 91 48 
					91 85 48 
					63 91 50 
					53 52 75 
					92 53 75 
					93 53 92 94 
					95 53 93 
					96 53 95 
					96 54 53 
					73 71 54 97 
					71 70 54 
					70 98 54 
					98 99 100 54 
					101 55 54 
					102 101 54 
					103 104 54 100 
					104 97 54 
					54 105 106 
					54 96 105 
					106 102 54 
					55 56 57 
					55 107 56 
					55 101 107 
					56 107 69 
					108 58 109 
					110 59 58 108 
					109 58 111 
					111 58 76 112 
					110 113 60 59 
					113 61 60 
					81 61 113 
					82 63 64 
					91 63 82 
					70 69 98 
					98 69 107 
					74 72 114 115 
					72 73 114 
					114 73 97 
					116 75 74 115 
					117 75 116 
					118 75 117 
					112 92 75 118 
					76 77 119 120 112 
					77 121 119 
					78 122 121 77 
					79 122 78 
					80 123 122 79 
					80 124 123 
					80 90 125 124 
					87 81 113 
					82 84 91 
					126 84 83 
					126 83 88 
					84 127 91 
					126 127 84 
					91 127 85 
					86 85 127 
					89 86 127 128 
					125 88 87 124 
					124 87 113 
					128 126 88 125 
					90 89 128 125 
					112 120 129 94 92 
					94 130 131 93 
					93 131 95 
					129 130 94 
					95 131 132 96 
					132 133 96 
					133 134 105 96 
					114 97 104 
					107 99 98 
					100 99 135 
					107 136 99 
					99 136 135 
					103 100 135 
					101 136 107 
					136 101 102 
					137 136 102 106 
					133 104 103 134 
					134 103 135 137 
					114 104 133 
					134 137 106 105 
					123 138 108 109 
					138 110 108 
					123 109 122 
					122 109 121 
					109 139 140 121 
					109 111 139 
					124 113 110 138 
					139 111 112 
					112 118 139 
					141 115 114 133 
					116 115 141 
					117 116 141 132 
					131 117 132 
					130 117 131 
					130 140 139 117 
					139 118 117 
					121 140 120 119 
					129 120 140 130 
					123 124 138 
					127 126 128 
					141 133 132 
					137 135 136 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 40 43 47 50 54 57 61 64 67 70 73 77 81 84 88 91 94 98 102 106 109 113 116 119 123 126 129 133 136 140 143 146 149 153 157 161 164 167 171 174 177 180 183 187 191 194 197 200 204 207 210 213 217 220 223 226 230 233 236 240 243 246 250 254 258 261 264 267 270 273 276 279 282 286 290 293 297 300 304 307 311 314 317 320 324 327 330 333 337 340 343 347 350 353 357 360 363 366 369 372 375 378 381 384 387 391 394 397 400 404 407 410 414 417 420 424 427 430 433 436 439 442 445 448 451 455 458 462 466 469 472 475 478 481 484 488 491 494 498 501 504 508 513 516 520 523 527 530 534 537 540 543 546 549 552 555 558 562 566 569 573 577 582 586 589 592 596 599 603 606 609 612 615 618 621 624 627 631 635 639 642 646 650 653 656 659 663 666 670 673 676 680 683 687 690 693 697 700 704 708 711 714 717 720 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
