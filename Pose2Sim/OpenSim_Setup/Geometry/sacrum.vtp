<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="192" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="200">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.332352  0.943155  0.000361
			 0.864367  0.502850 -0.003229
			 0.973319  0.042555 -0.225474
			 0.698427  0.337576 -0.631064
			 0.764758  0.184327 -0.617389
			 0.920612  0.333687 -0.202799
			 0.825849  0.563878  0.004001
			 0.991846 -0.100568 -0.078275
			 0.569797 -0.821784 -0.001764
			 0.380376 -0.911910  0.154058
			 0.552371 -0.816283  0.169021
			 0.616804 -0.769969  0.163404
			 0.761394 -0.623950  0.175969
			 0.979720 -0.032198  0.197768
			 0.298276  0.934201  0.195706
			-0.549118 -0.631008  0.547995
			 0.016744 -0.885617  0.464115
			 0.988548 -0.150863 -0.003619
			 0.996957 -0.064871  0.043225
			 0.958246 -0.284838  0.025136
			 0.322400 -0.946589 -0.005358
			 0.847682  0.492581 -0.196973
			 0.943284 -0.259482 -0.207085
			 0.927129 -0.362151 -0.096327
			 0.876837 -0.480760 -0.005273
			 0.820121 -0.506496  0.266202
			 0.675609 -0.228099  0.701087
			 0.623231 -0.408377  0.666942
			 0.514143 -0.829293  0.218931
			 0.644165 -0.757876  0.103322
			 0.804369 -0.592780  0.040026
			 0.757480 -0.587818 -0.284066
			 0.747160 -0.432381 -0.504776
			 0.712919 -0.232347 -0.661636
			 0.582050 -0.137319 -0.801475
			-0.106314 -0.107932 -0.988457
			 0.070285 -0.298575 -0.951795
			 0.220537 -0.528120 -0.820032
			 0.353930 -0.691448 -0.629788
			 0.750556 -0.504594 -0.426674
			 0.616823  0.718858  0.320581
			 0.680823  0.081559  0.727893
			 0.549117 -0.205958  0.809970
			 0.400751 -0.523096  0.752176
			 0.119748 -0.859186  0.497453
			 0.103516 -0.648937  0.753767
			 0.171268 -0.379487  0.909207
			 0.108202 -0.760445  0.640325
			 0.286002 -0.481712  0.828346
			 0.394715 -0.282790  0.874202
			 0.414274 -0.618157  0.668027
			 0.445136 -0.742630  0.500355
			 0.377339 -0.915263  0.141097
			 0.626167 -0.737262  0.253693
			 0.406360  0.780472  0.475116
			 0.413042  0.666606  0.620510
			-0.078082 -0.792431  0.604943
			-0.124968 -0.788872  0.601718
			 0.399739 -0.604891  0.688706
			 0.088420  0.947349  0.307751
			-0.380869  0.589059  0.712705
			-0.411964  0.393920  0.821653
			-0.533288  0.081379  0.842010
			-0.666787 -0.284240  0.688914
			-0.441116  0.030634  0.896927
			-0.299802  0.074904  0.951056
			-0.488230 -0.434092  0.757097
			-0.357429 -0.184381  0.915559
			-0.200291  0.028189  0.979331
			-0.341591 -0.272987  0.899330
			-0.329252 -0.375526  0.866357
			-0.178142 -0.578363  0.796091
			-0.005195 -0.389318  0.921089
			 0.102673  0.307889  0.945866
			-0.007943  0.340983  0.940036
			-0.179614 -0.519034  0.835669
			-0.147438 -0.601866  0.784869
			-0.073769 -0.341862  0.936850
			-0.221615  0.954345 -0.200284
			-0.816463  0.521673 -0.247479
			-0.729695  0.683650 -0.012929
			-0.775498  0.622771  0.103730
			-0.889116  0.428964  0.159569
			-0.473475  0.767143  0.432797
			-0.499094  0.638385  0.585978
			-0.850097 -0.010918  0.526514
			-0.760036  0.139770  0.634673
			-0.545004  0.370272  0.752243
			-0.764370 -0.110332  0.635268
			-0.681784 -0.112443  0.722860
			-0.275409  0.117253  0.954150
			-0.270588 -0.194818  0.942777
			-0.503278 -0.153119  0.850450
			-0.527757  0.131058  0.839223
			-0.411159 -0.390143  0.823855
			-0.318311 -0.450733  0.833977
			-0.289901 -0.283575  0.914080
			-0.318062  0.948036  0.008072
			-0.882149  0.470809  0.012305
			-0.750113  0.653134  0.103670
			-0.778895  0.620213  0.093050
			-0.876897  0.480299  0.019114
			-0.588143  0.808688  0.010581
			-0.680217  0.732837  0.015935
			-0.996533  0.071442  0.042634
			-0.965877  0.257257  0.029996
			-0.848125  0.528617  0.035314
			-0.999260 -0.019764 -0.032985
			-0.999795 -0.016749 -0.011376
			-0.939022  0.334476  0.079771
			-0.992491 -0.084731 -0.088211
			-0.997075 -0.028903 -0.070752
			-0.947506  0.319321  0.016338
			-0.871777 -0.487076  0.052555
			-0.779237 -0.625754  0.034961
			-0.909724 -0.414943 -0.014956
			-0.267678  0.938976  0.216038
			-0.783173  0.568554  0.251768
			-0.844256  0.524238  0.111384
			-0.932568  0.360920  0.007280
			-0.998541  0.032353 -0.043222
			-0.882760  0.263814 -0.388763
			-0.777048  0.313528 -0.545799
			-0.882659 -0.216469 -0.417198
			-0.796988  0.054899 -0.601495
			-0.651625  0.243676 -0.718337
			-0.768438 -0.099394 -0.632159
			-0.789179 -0.195734 -0.582138
			-0.713202 -0.387859 -0.583874
			-0.485949 -0.319382 -0.813541
			-0.307761  0.491327 -0.814789
			-0.433935  0.564804 -0.701924
			-0.366614 -0.508025 -0.779426
			-0.210745 -0.634152 -0.743934
			-0.261241 -0.357477 -0.896640
			 0.073347  0.947999 -0.309707
			-0.382422  0.603781 -0.699430
			-0.504148  0.314193 -0.804436
			-0.616545 -0.029416 -0.786770
			-0.641322 -0.536152 -0.548859
			-0.530735 -0.381786 -0.756677
			-0.413984 -0.097483 -0.905049
			-0.513803 -0.498944 -0.697898
			-0.384144 -0.212067 -0.898588
			-0.254320 -0.020781 -0.966897
			-0.350630 -0.275585 -0.895048
			-0.425107 -0.445037 -0.788179
			-0.443993 -0.778088 -0.444353
			-0.163855 -0.544696 -0.822470
			 0.325442  0.639219 -0.696769
			 0.140916  0.652772 -0.744333
			-0.173154 -0.566597 -0.805596
			-0.109346 -0.691130 -0.714411
			-0.064671 -0.378730 -0.923245
			 0.614025  0.718145 -0.327477
			 0.665938  0.170051 -0.726367
			 0.549590 -0.029962 -0.834897
			 0.429198 -0.277983 -0.859369
			 0.201423 -0.365569 -0.908729
			 0.082295 -0.065455 -0.994456
			 0.125636 -0.198219 -0.972072
			 0.070337 -0.782920 -0.618133
			 0.250168 -0.810311 -0.529917
			 0.386909 -0.638249 -0.665538
			 0.322469 -0.805953 -0.496440
			 0.305224 -0.905449 -0.294958
			 0.188855 -0.981450  0.033012
			 0.488443 -0.862361 -0.133253
			 0.822122  0.493024 -0.284681
			 0.815848  0.432117 -0.384276
			 0.506832 -0.752724 -0.420153
			 0.451444 -0.775206 -0.441875
			 0.465368 -0.554012 -0.690292
			 0.841976  0.506980  0.184521
			 0.989109  0.146973  0.007937
			 0.723552  0.433184 -0.537423
			 0.666395  0.245909 -0.703880
			 0.666686  0.337104 -0.664749
			 0.428616  0.484886 -0.762347
			 0.546630  0.002247 -0.837371
			 0.500818 -0.845374 -0.185805
			 0.322240 -0.889529  0.323883
			 0.457176 -0.808797  0.369916
			 0.420785 -0.685396  0.594283
			 0.385091 -0.527734  0.757101
			 0.367099 -0.373989  0.851687
			 0.360794 -0.251751  0.898025
			 0.568224  0.100769  0.816681
			 0.697040  0.075785  0.713016
			 0.647255 -0.314640  0.694307
			 0.741928 -0.260236  0.617916
			 0.804145 -0.333951  0.491759
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.105266  0.073567  0.000000
			-0.101060  0.071406 -0.000038
			-0.103110  0.062801 -0.000055
			-0.099288  0.059866 -0.000094
			-0.094881  0.061953 -0.000118
			-0.090675  0.055615 -0.000171
			-0.078261  0.041328 -0.000314
			-0.073511  0.038329 -0.000359
			-0.075244  0.034855 -0.000359
			-0.089330  0.033272 -0.000262
			-0.105368  0.029386 -0.000160
			-0.115167  0.024836 -0.000105
			-0.127076  0.023767 -0.000022
			-0.139719  0.014799  0.000038
			-0.150280  0.008083  0.000090
			-0.160832 -0.007603  0.000110
			-0.159688 -0.013014  0.000082
			-0.162464 -0.021788  0.000070
			-0.161226 -0.028937  0.000035
			-0.159814 -0.034455  0.000004
			-0.163070 -0.042263  0.000000
			-0.087502  0.065933  0.014170
			-0.088506  0.059611  0.015582
			-0.089636  0.050559  0.017809
			-0.091154  0.045295  0.018536
			-0.093050  0.039799  0.019093
			-0.091732  0.032772  0.022260
			-0.092168  0.027460  0.023715
			-0.095793  0.023202  0.022731
			-0.104983  0.021112  0.017380
			-0.112892  0.014795  0.014255
			-0.121302  0.008912  0.010659
			-0.129428  0.004384  0.006807
			-0.138165 -0.001368  0.002954
			-0.145949 -0.004952 -0.000985
			-0.152393 -0.016035 -0.001584
			-0.149741 -0.019734  0.001370
			-0.151942 -0.025186  0.001712
			-0.154476 -0.028569  0.001156
			-0.156278 -0.033486  0.001584
			-0.092880  0.068104  0.041630
			-0.093749  0.064434  0.044285
			-0.100269  0.049439  0.046938
			-0.106924  0.036507  0.046380
			-0.111593  0.030506  0.041770
			-0.115160  0.028235  0.035065
			-0.119482  0.020083  0.034366
			-0.123470  0.014427  0.031153
			-0.127042  0.011539  0.025286
			-0.130006  0.004076  0.027381
			-0.135742 -0.003659  0.022212
			-0.140590 -0.011445  0.019558
			-0.144099 -0.014511  0.014110
			-0.147112 -0.015770  0.007543
			-0.150066 -0.021999  0.007962
			-0.148296 -0.023022  0.014250
			-0.149536 -0.025306  0.013970
			-0.152250 -0.026375  0.007962
			-0.155560 -0.031379  0.005727
			-0.117438  0.078016  0.041845
			-0.119873  0.074978  0.044514
			-0.127957  0.060614  0.047182
			-0.134283  0.047551  0.046620
			-0.136231  0.040450  0.041986
			-0.135845  0.036584  0.035246
			-0.139753  0.028267  0.034543
			-0.141848  0.021844  0.031314
			-0.141958  0.017558  0.025417
			-0.146158  0.010595  0.027522
			-0.148845  0.001629  0.022327
			-0.152128 -0.006787  0.019659
			-0.152422 -0.011153  0.014182
			-0.151561 -0.013974  0.007583
			-0.154765 -0.020103  0.008004
			-0.156701 -0.019629  0.014323
			-0.157777 -0.021979  0.014042
			-0.156948 -0.024478  0.008004
			-0.158939 -0.030015  0.005757
			-0.124120  0.079940  0.014964
			-0.128867  0.075593  0.016714
			-0.133878  0.065168  0.016582
			-0.143240  0.056733  0.020083
			-0.144275  0.049109  0.018202
			-0.147549  0.042545  0.018202
			-0.153668  0.037129  0.020521
			-0.155529  0.031464  0.019864
			-0.153917  0.026560  0.017108
			-0.160375  0.019813  0.019207
			-0.161506  0.012383  0.017458
			-0.161877  0.005574  0.015401
			-0.162516  0.000034  0.013957
			-0.164225 -0.005846  0.013126
			-0.164220 -0.012033  0.011026
			-0.167359 -0.015870  0.011857
			-0.169295 -0.022202  0.011026
			-0.164135 -0.026062  0.006213
			-0.165309 -0.032933  0.004682
			-0.111968  0.074262  0.000051
			-0.118300  0.068313  0.000075
			-0.118386  0.061080  0.000050
			-0.133247  0.058266  0.000148
			-0.135318  0.050905  0.000137
			-0.146728  0.043450  0.000192
			-0.156903  0.041682  0.000261
			-0.160282  0.037480  0.000270
			-0.159215  0.032873  0.000246
			-0.167790  0.026284  0.000284
			-0.169767  0.021362  0.000281
			-0.167767  0.016378  0.000248
			-0.171523  0.010783  0.000254
			-0.179688  0.003407  0.000287
			-0.174941 -0.003455  0.000227
			-0.175586 -0.013090  0.000198
			-0.178071 -0.023529  0.000178
			-0.169716 -0.028294  0.000100
			-0.170651 -0.036265  0.000077
			-0.124333  0.080026 -0.014642
			-0.129105  0.075689 -0.016353
			-0.134112  0.065263 -0.016225
			-0.143525  0.056847 -0.019649
			-0.144531  0.049215 -0.017809
			-0.147808  0.042650 -0.017809
			-0.153958  0.037247 -0.020078
			-0.155810  0.031577 -0.019436
			-0.154159  0.026658 -0.016738
			-0.160648  0.019924 -0.018794
			-0.161754  0.012483 -0.017081
			-0.162094  0.005663 -0.015069
			-0.162713  0.000114 -0.013656
			-0.164412 -0.005771 -0.012842
			-0.164376 -0.011971 -0.010788
			-0.167527 -0.015802 -0.011602
			-0.169452 -0.022139 -0.010788
			-0.164223 -0.026026 -0.006079
			-0.165374 -0.032906 -0.004580
			-0.118037  0.078258 -0.041630
			-0.120510  0.075235 -0.044285
			-0.128634  0.060888 -0.046938
			-0.134952  0.047820 -0.046380
			-0.136834  0.040695 -0.041770
			-0.136349  0.036788 -0.035065
			-0.140249  0.028466 -0.034366
			-0.142296  0.022024 -0.031153
			-0.142322  0.017705 -0.025286
			-0.146552  0.010754 -0.027381
			-0.149165  0.001758 -0.022211
			-0.152409 -0.006674 -0.019558
			-0.152626 -0.011070 -0.014110
			-0.151669 -0.013930 -0.007543
			-0.154878 -0.020056 -0.007962
			-0.156907 -0.019547 -0.014250
			-0.157978 -0.021900 -0.013970
			-0.157062 -0.024433 -0.007962
			-0.159020 -0.029982 -0.005727
			-0.093479  0.068346 -0.041845
			-0.094386  0.064690 -0.044514
			-0.100944  0.049711 -0.047182
			-0.107592  0.036777 -0.046620
			-0.112193  0.030748 -0.041986
			-0.115664  0.028440 -0.035246
			-0.119977  0.020284 -0.034542
			-0.123920  0.014607 -0.031314
			-0.127404  0.011686 -0.025417
			-0.130400  0.004235 -0.027522
			-0.136062 -0.003530 -0.022327
			-0.140872 -0.011331 -0.019659
			-0.144301 -0.014431 -0.014182
			-0.147220 -0.015726 -0.007583
			-0.150182 -0.021953 -0.008004
			-0.148502 -0.022940 -0.014323
			-0.149738 -0.025225 -0.014042
			-0.152364 -0.026328 -0.008004
			-0.155641 -0.031345 -0.005757
			-0.087708  0.066016 -0.014482
			-0.088732  0.059704 -0.015926
			-0.089895  0.050662 -0.018202
			-0.091423  0.045403 -0.018945
			-0.093327  0.039911 -0.019514
			-0.092055  0.032903 -0.022751
			-0.092513  0.027599 -0.024239
			-0.096122  0.023334 -0.023234
			-0.105235  0.021214 -0.017765
			-0.113098  0.014879 -0.014570
			-0.121456  0.008974 -0.010894
			-0.129528  0.004424 -0.006957
			-0.138208 -0.001350 -0.003019
			-0.145935 -0.004957  0.001006
			-0.152370 -0.016045  0.001619
			-0.149760 -0.019726 -0.001400
			-0.151966 -0.025175 -0.001750
			-0.154493 -0.028561 -0.001182
			-0.156301 -0.033476 -0.001619
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					21 1 0 
					21 22 2 1 
					22 23 3 2 
					23 24 4 3 
					24 25 5 4 
					25 26 6 5 
					26 27 7 6 
					27 28 8 7 
					28 29 9 8 
					29 30 10 9 
					30 31 11 10 
					31 32 12 11 
					32 33 13 12 
					33 34 14 13 
					34 35 15 14 
					35 36 16 15 
					36 37 17 16 
					37 38 18 17 
					38 39 19 18 
					39 20 19 
					40 21 0 
					40 41 22 21 
					41 42 23 22 
					42 43 24 23 
					43 44 25 24 
					44 45 26 25 
					45 46 27 26 
					46 47 28 27 
					47 48 29 28 
					48 49 30 29 
					49 50 31 30 
					50 51 32 31 
					51 52 33 32 
					52 53 34 33 
					53 54 35 34 
					54 55 36 35 
					55 56 37 36 
					56 57 38 37 
					57 58 39 38 
					58 20 39 
					59 40 0 
					59 60 41 40 
					60 61 42 41 
					61 62 43 42 
					62 63 44 43 
					63 64 45 44 
					64 65 46 45 
					65 66 47 46 
					66 67 48 47 
					67 68 49 48 
					68 69 50 49 
					69 70 51 50 
					70 71 52 51 
					71 72 53 52 
					72 73 54 53 
					73 74 55 54 
					74 75 56 55 
					75 76 57 56 
					76 77 58 57 
					77 20 58 
					78 59 0 
					78 79 60 59 
					79 80 61 60 
					80 81 62 61 
					81 82 63 62 
					82 83 64 63 
					83 84 65 64 
					84 85 66 65 
					85 86 67 66 
					86 87 68 67 
					87 88 69 68 
					88 89 70 69 
					89 90 71 70 
					90 91 72 71 
					91 92 73 72 
					92 93 74 73 
					93 94 75 74 
					94 95 76 75 
					95 96 77 76 
					96 20 77 
					97 78 0 
					97 98 79 78 
					98 99 80 79 
					99 100 81 80 
					100 101 82 81 
					101 102 83 82 
					102 103 84 83 
					103 104 85 84 
					104 105 86 85 
					105 106 87 86 
					106 107 88 87 
					107 108 89 88 
					108 109 90 89 
					109 110 91 90 
					110 111 92 91 
					111 112 93 92 
					112 113 94 93 
					113 114 95 94 
					114 115 96 95 
					115 20 96 
					116 97 0 
					116 117 98 97 
					117 118 99 98 
					118 119 100 99 
					119 120 101 100 
					120 121 102 101 
					121 122 103 102 
					122 123 104 103 
					123 124 105 104 
					124 125 106 105 
					125 126 107 106 
					126 127 108 107 
					127 128 109 108 
					128 129 110 109 
					129 130 111 110 
					130 131 112 111 
					131 132 113 112 
					132 133 114 113 
					133 134 115 114 
					134 20 115 
					135 116 0 
					135 136 117 116 
					136 137 118 117 
					137 138 119 118 
					138 139 120 119 
					139 140 121 120 
					140 141 122 121 
					141 142 123 122 
					142 143 124 123 
					143 144 125 124 
					144 145 126 125 
					145 146 127 126 
					146 147 128 127 
					147 148 129 128 
					148 149 130 129 
					149 150 131 130 
					150 151 132 131 
					151 152 133 132 
					152 153 134 133 
					153 20 134 
					154 135 0 
					154 155 136 135 
					155 156 137 136 
					156 157 138 137 
					157 158 139 138 
					158 159 140 139 
					159 160 141 140 
					160 161 142 141 
					161 162 143 142 
					162 163 144 143 
					163 164 145 144 
					164 165 146 145 
					165 166 147 146 
					166 167 148 147 
					167 168 149 148 
					168 169 150 149 
					169 170 151 150 
					170 171 152 151 
					171 172 153 152 
					172 20 153 
					173 154 0 
					173 174 155 154 
					174 175 156 155 
					175 176 157 156 
					176 177 158 157 
					177 178 159 158 
					178 179 160 159 
					179 180 161 160 
					180 181 162 161 
					181 182 163 162 
					182 183 164 163 
					183 184 165 164 
					184 185 166 165 
					185 186 167 166 
					186 187 168 167 
					187 188 169 168 
					188 189 170 169 
					189 190 171 170 
					190 191 172 171 
					191 20 172 
					1 173 0 
					1 2 174 173 
					2 3 175 174 
					3 4 176 175 
					4 5 177 176 
					5 6 178 177 
					6 7 179 178 
					7 8 180 179 
					8 9 181 180 
					9 10 182 181 
					10 11 183 182 
					11 12 184 183 
					12 13 185 184 
					13 14 186 185 
					14 15 187 186 
					15 16 188 187 
					16 17 189 188 
					17 18 190 189 
					18 19 191 190 
					19 20 191 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 7 11 15 19 23 27 31 35 39 43 47 51 55 59 63 67 71 75 78 81 85 89 93 97 101 105 109 113 117 121 125 129 133 137 141 145 149 153 156 159 163 167 171 175 179 183 187 191 195 199 203 207 211 215 219 223 227 231 234 237 241 245 249 253 257 261 265 269 273 277 281 285 289 293 297 301 305 309 312 315 319 323 327 331 335 339 343 347 351 355 359 363 367 371 375 379 383 387 390 393 397 401 405 409 413 417 421 425 429 433 437 441 445 449 453 457 461 465 468 471 475 479 483 487 491 495 499 503 507 511 515 519 523 527 531 535 539 543 546 549 553 557 561 565 569 573 577 581 585 589 593 597 601 605 609 613 617 621 624 627 631 635 639 643 647 651 655 659 663 667 671 675 679 683 687 691 695 699 702 705 709 713 717 721 725 729 733 737 741 745 749 753 757 761 765 769 773 777 780 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
