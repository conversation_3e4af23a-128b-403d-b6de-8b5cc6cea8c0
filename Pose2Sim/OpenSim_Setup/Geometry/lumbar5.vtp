<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.297086 -0.954851  0.000000
			 0.069036 -0.966683 -0.246491
			 0.429621 -0.903009  0.000000
			 0.517627 -0.708372 -0.479866
			 0.416427 -0.435535 -0.798059
			-0.967171 -0.254127  0.000000
			-0.868843 -0.494790  0.017174
			-0.955839 -0.139879 -0.258470
			-0.363070  0.044160 -0.930715
			 0.069036 -0.966683  0.246491
			 0.517627 -0.708372  0.479866
			 0.416427 -0.435535  0.798059
			-0.868843 -0.494790 -0.017174
			-0.955839 -0.139879  0.258470
			-0.363070  0.044160  0.930715
			 0.895971 -0.343282 -0.281769
			 0.993271 -0.115817  0.000000
			 0.895971 -0.343282  0.281769
			 0.747101 -0.062899 -0.661728
			 0.398804  0.257689 -0.880086
			-0.989785  0.127984  0.062812
			-0.976705  0.214587  0.000000
			-0.989785  0.127984 -0.062812
			-0.821460  0.061090 -0.566985
			-0.232828  0.507157 -0.829809
			-0.989544  0.095647  0.107954
			 0.747101 -0.062899  0.661728
			 0.398804  0.257689  0.880086
			-0.232828  0.507157  0.829809
			-0.821460  0.061090  0.566985
			-0.989544  0.095647 -0.107954
			 0.935213 -0.225409 -0.273072
			 0.990082 -0.140494  0.000000
			 0.935213 -0.225409  0.273072
			 0.684617 -0.186826 -0.704554
			 0.226729  0.077359 -0.970881
			-0.959154  0.093747  0.266898
			-0.998297  0.058334  0.000000
			-0.959154  0.093747 -0.266898
			-0.259451 -0.547727 -0.795412
			-0.111494 -0.040967 -0.992920
			-0.810633 -0.365796  0.457238
			 0.684617 -0.186826  0.704554
			 0.226729  0.077359  0.970881
			-0.111494 -0.040967  0.992920
			-0.259451 -0.547727  0.795412
			-0.810633 -0.365796 -0.457238
			 0.853607 -0.451630 -0.259585
			 0.913986 -0.405746  0.000000
			 0.853607 -0.451630  0.259585
			 0.539673 -0.408757 -0.735983
			 0.155303 -0.301564 -0.940713
			-0.957761  0.093357  0.271989
			-0.392285  0.066593  0.917430
			-0.991579  0.129503  0.000000
			-0.957761  0.093357 -0.271989
			-0.392285  0.066593 -0.917430
			 0.131350 -0.048216 -0.990163
			-0.048959 -0.272996 -0.960769
			-0.292066 -0.709866 -0.640927
			-0.236878 -0.947588 -0.214396
			 0.431641 -0.536878 -0.724878
			 0.746939 -0.013062  0.664764
			 0.539673 -0.408757  0.735983
			 0.155303 -0.301564  0.940713
			-0.048959 -0.272996  0.960769
			 0.131350 -0.048217  0.990163
			-0.236878 -0.947588  0.214396
			-0.292066 -0.709866  0.640927
			 0.431641 -0.536879  0.724877
			 0.746939 -0.013062 -0.664764
			 0.886627 -0.348665 -0.303850
			 0.845402 -0.534130  0.000000
			 0.886627 -0.348665  0.303850
			 0.554305 -0.306984 -0.773632
			 0.113599 -0.325121 -0.938825
			-0.253806  0.879229  0.403162
			-0.199140  0.017848  0.979809
			-0.520744  0.853713  0.000000
			-0.253806  0.879229 -0.403162
			-0.199140  0.017848 -0.979809
			-0.133286  0.241875 -0.961109
			-0.118393 -0.158455 -0.980242
			 0.553577  0.566972 -0.609997
			-0.623857 -0.523414 -0.580379
			-0.711351 -0.177436 -0.680071
			-0.573757 -0.683450 -0.451330
			 0.708216 -0.618912 -0.339673
			-0.363451 -0.924859 -0.111977
			 0.777296 -0.138162 -0.613776
			 0.804051 -0.434969  0.405343
			 0.998969 -0.045395  0.000000
			-0.177733  0.984079  0.000000
			-0.251850  0.571964  0.780659
			 0.554305 -0.306984  0.773632
			 0.113599 -0.325121  0.938825
			-0.118393 -0.158455  0.980242
			-0.133285  0.241875  0.961110
			 0.553575  0.566972  0.609998
			-0.711351 -0.177436  0.680071
			-0.623857 -0.523414  0.580379
			-0.573757 -0.683450  0.451330
			 0.708216 -0.618912  0.339673
			 0.777295 -0.138164  0.613778
			-0.363451 -0.924859  0.111977
			 0.804051 -0.434969 -0.405343
			-0.251850  0.571964 -0.780659
			 0.795738  0.525058 -0.301852
			 0.870510  0.492151  0.000000
			 0.795738  0.525058  0.301852
			 0.615450  0.604129 -0.506211
			 0.329212  0.445155 -0.832740
			-0.029145  0.475388 -0.879293
			 0.481059  0.786770 -0.386750
			 0.232834  0.950292  0.206718
			-0.419582  0.696033  0.582657
			 0.215958  0.976403  0.000000
			 0.007844  0.909951 -0.414642
			 0.232834  0.950292 -0.206718
			 0.481057  0.786772  0.386748
			-0.419582  0.696033 -0.582657
			 0.007844  0.909951  0.414643
			-0.058401  0.784836 -0.616945
			 0.567591  0.633538 -0.525804
			-0.031322  0.010125 -0.999458
			-0.680523  0.515480 -0.520738
			-0.552970 -0.451448 -0.700299
			-0.742682 -0.274801  0.610662
			-0.824112  0.440146  0.356525
			 0.051262  0.597849 -0.799968
			-0.122781  0.658703 -0.742318
			 0.863896 -0.460745 -0.203464
			 0.072939  0.127636 -0.989135
			-0.894270 -0.407249 -0.185553
			 0.238146 -0.229985 -0.943607
			-0.885294  0.410856  0.217834
			 0.654863 -0.588537  0.474109
			 0.754592 -0.656194  0.000000
			-0.348314  0.690694 -0.633735
			-0.481756  0.876305  0.000000
			-0.597786  0.766804 -0.233802
			-0.718764  0.661344  0.214484
			-0.348314  0.690694  0.633735
			-0.597786  0.766804  0.233802
			-0.718764  0.661344 -0.214484
			-0.608738  0.499363  0.616502
			 0.615450  0.604129  0.506211
			 0.329212  0.445155  0.832740
			-0.029145  0.475388  0.879293
			-0.058403  0.784837  0.616945
			 0.567588  0.633542  0.525803
			 0.051262  0.597849  0.799968
			-0.552970 -0.451448  0.700299
			-0.122781  0.658703  0.742318
			-0.680523  0.515480  0.520738
			-0.031322  0.010125  0.999458
			-0.742682 -0.274801 -0.610662
			-0.824112  0.440146 -0.356525
			 0.863896 -0.460745  0.203464
			 0.072939  0.127636  0.989135
			 0.238146 -0.229985  0.943607
			-0.894270 -0.407249  0.185553
			-0.885294  0.410856 -0.217834
			 0.654863 -0.588537 -0.474109
			-0.608738  0.499363 -0.616502
			-0.558102  0.623917  0.547037
			-0.245521  0.346117  0.905496
			-0.558102  0.623917 -0.547037
			-0.245521  0.346117 -0.905496
			-0.873058 -0.217115 -0.436614
			-0.522164  0.620924 -0.584635
			-0.916688 -0.143941 -0.372779
			-0.570128  0.762782 -0.305153
			-0.592584  0.244617 -0.767468
			-0.951113 -0.298392 -0.079659
			 0.241119 -0.959432 -0.146126
			 0.026267 -0.878179  0.477610
			 0.275857 -0.961199  0.000000
			 0.026267 -0.878179 -0.477610
			-0.448482  0.566017 -0.691729
			-0.330002  0.700207 -0.633095
			-0.374630  0.927174  0.000000
			-0.448482  0.566017  0.691729
			-0.330002  0.700207  0.633095
			-0.570128  0.762782  0.305153
			-0.592584  0.244617  0.767468
			-0.951113 -0.298392  0.079659
			-0.522164  0.620924  0.584635
			-0.873058 -0.217115  0.436614
			-0.916688 -0.143941  0.372779
			 0.241119 -0.959432  0.146126
			-0.396904  0.345087 -0.850518
			-0.702553 -0.669616  0.240902
			-0.438857 -0.769912 -0.463292
			-0.112523 -0.993649  0.000000
			-0.702553 -0.669616 -0.240902
			-0.438857 -0.769912  0.463292
			-0.581330  0.813668  0.000000
			-0.396904  0.345087  0.850518
			 0.016737  0.283994 -0.958680
			-0.081909 -0.128292 -0.988348
			 0.006583 -0.756498 -0.653963
			-0.246357 -0.969179  0.000001
			 0.006583 -0.756498  0.653963
			-0.081909 -0.128292  0.988348
			-0.810156  0.185581 -0.556064
			-0.977763 -0.209714  0.000000
			-0.810153  0.185571  0.556072
			 0.016739  0.283995  0.958680
			-0.417466 -0.608736 -0.674657
			-0.794453 -0.607325  0.000005
			-0.417459 -0.608738  0.674660
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.010403  0.005803  0.000000
			 0.024133  0.003806 -0.009271
			 0.023094  0.000720  0.000000
			 0.019226  0.005192 -0.018973
			 0.015007  0.005053 -0.023697
			-0.003309  0.012044  0.000000
			-0.003286  0.011643 -0.005568
			-0.003586  0.012097 -0.016493
			 0.002178  0.012102 -0.023835
			 0.024133  0.003806  0.009271
			 0.019226  0.005192  0.018973
			 0.015007  0.005053  0.023697
			-0.003286  0.011643  0.005568
			-0.003586  0.012097  0.016493
			 0.002178  0.012102  0.023835
			 0.025086  0.004806 -0.011135
			 0.025125  0.005552  0.000000
			 0.025086  0.004806  0.011135
			 0.018299  0.008560 -0.018324
			 0.015013  0.011333 -0.022277
			-0.002783  0.015628 -0.005208
			-0.002779  0.015469  0.000000
			-0.002783  0.015628  0.005208
			-0.002396  0.017520 -0.016841
			 0.003238  0.012924 -0.022444
			-0.003481  0.017482 -0.009944
			 0.018299  0.008560  0.018324
			 0.015013  0.011333  0.022277
			 0.003238  0.012924  0.022444
			-0.002396  0.017520  0.016841
			-0.003481  0.017482  0.009944
			 0.026472  0.011890 -0.010839
			 0.024180  0.011207  0.000000
			 0.026472  0.011890  0.010839
			 0.022407  0.012868 -0.017864
			 0.016008  0.016303 -0.020258
			-0.001266  0.022655 -0.006689
			-0.000721  0.022594  0.000000
			-0.001266  0.022655  0.006689
			-0.000289  0.020861 -0.020082
			 0.005926  0.020012 -0.021237
			-0.003167  0.023670 -0.010260
			 0.022407  0.012868  0.017864
			 0.016008  0.016303  0.020258
			 0.005926  0.020012  0.021237
			-0.000289  0.020861  0.020082
			-0.003167  0.023670  0.010260
			 0.026454  0.020092 -0.010455
			 0.028245  0.021101  0.000000
			 0.026454  0.020092  0.010455
			 0.023804  0.021767 -0.018550
			 0.018454  0.022884 -0.020348
			-0.001804  0.030128 -0.006911
			-0.003865  0.031019 -0.010728
			-0.000988  0.030103  0.000000
			-0.001804  0.030128  0.006911
			-0.003865  0.031019  0.010728
			 0.002481  0.027402 -0.021807
			 0.007462  0.025323 -0.021386
			-0.003297  0.024253 -0.022188
			-0.007070  0.021567 -0.018091
			-0.004695  0.028504 -0.026540
			-0.011612  0.027977 -0.003911
			 0.023804  0.021767  0.018550
			 0.018454  0.022884  0.020348
			 0.007462  0.025323  0.021386
			 0.002481  0.027402  0.021807
			-0.007070  0.021567  0.018091
			-0.003297  0.024253  0.022188
			-0.004695  0.028504  0.026540
			-0.011612  0.027977  0.003911
			 0.032627  0.025106 -0.011570
			 0.033388  0.025440  0.000000
			 0.032627  0.025106  0.011570
			 0.026829  0.025690 -0.019637
			 0.019984  0.026311 -0.022504
			-0.000670  0.038502 -0.008823
			-0.001905  0.036689 -0.011520
			 0.000922  0.037118  0.000000
			-0.000670  0.038502  0.008823
			-0.001905  0.036689  0.011520
			 0.002415  0.032592 -0.021070
			 0.010895  0.029779 -0.023599
			-0.004188  0.035266 -0.021124
			-0.013253  0.027757 -0.018198
			-0.013928  0.032995 -0.024502
			-0.010350  0.024563 -0.026213
			-0.010013  0.021635 -0.011135
			-0.010829  0.027288 -0.030672
			-0.008545  0.030301 -0.033740
			-0.014419  0.018072 -0.005388
			-0.012278  0.026431  0.000000
			-0.020555  0.033571  0.000000
			-0.012230  0.035328 -0.008145
			 0.026829  0.025690  0.019637
			 0.019984  0.026311  0.022504
			 0.010895  0.029779  0.023599
			 0.002415  0.032592  0.021070
			-0.004188  0.035266  0.021124
			-0.013928  0.032995  0.024502
			-0.013253  0.027757  0.018198
			-0.010350  0.024563  0.026213
			-0.010013  0.021635  0.011135
			-0.008545  0.030301  0.033740
			-0.010829  0.027288  0.030672
			-0.014419  0.018072  0.005388
			-0.012230  0.035328  0.008145
			 0.031829  0.030668 -0.011782
			 0.032790  0.030773  0.000000
			 0.031829  0.030668  0.011782
			 0.027773  0.031211 -0.019907
			 0.020198  0.032834 -0.023593
			 0.010237  0.034163 -0.023745
			-0.003445  0.037274 -0.014915
			-0.008293  0.045312 -0.016931
			-0.008835  0.042223 -0.010630
			 0.017597  0.033824  0.000000
			 0.001298  0.037567 -0.018112
			-0.008293  0.045312  0.016931
			-0.003445  0.037274  0.014915
			-0.008835  0.042223  0.010630
			 0.001298  0.037567  0.018112
			-0.009414  0.037038 -0.030109
			-0.007459  0.040271 -0.019473
			-0.018214  0.023287 -0.016388
			-0.023369  0.022672 -0.013384
			-0.019138  0.036050 -0.022347
			-0.020015  0.034760 -0.015006
			-0.018074  0.032625 -0.013416
			-0.016483  0.045552 -0.020620
			-0.012087  0.039547 -0.022534
			-0.012691  0.015000 -0.012693
			-0.024479  0.020448 -0.018062
			-0.014942  0.029631 -0.034038
			-0.012573  0.031668 -0.037326
			-0.013994  0.037810 -0.034516
			-0.016133  0.014103 -0.007075
			-0.017430  0.019983  0.000000
			-0.022730  0.029921 -0.005224
			-0.032407  0.028151  0.000000
			-0.018879  0.027758 -0.008771
			-0.017486  0.030574 -0.011001
			-0.022730  0.029921  0.005224
			-0.018879  0.027758  0.008771
			-0.017486  0.030574  0.011001
			-0.013239  0.036189 -0.012003
			 0.027773  0.031211  0.019907
			 0.020198  0.032834  0.023593
			 0.010237  0.034163  0.023745
			-0.009414  0.037038  0.030109
			-0.007459  0.040271  0.019473
			-0.016483  0.045552  0.020620
			-0.019138  0.036050  0.022347
			-0.012087  0.039547  0.022534
			-0.023369  0.022672  0.013384
			-0.018214  0.023287  0.016388
			-0.020015  0.034760  0.015006
			-0.018074  0.032625  0.013416
			-0.012691  0.015000  0.012693
			-0.024479  0.020448  0.018062
			-0.012573  0.031668  0.037326
			-0.014942  0.029631  0.034038
			-0.013994  0.037810  0.034516
			-0.016133  0.014103  0.007075
			-0.013239  0.036189  0.012003
			-0.023378  0.043496 -0.017281
			-0.016920  0.039227 -0.014735
			-0.023378  0.043496  0.017281
			-0.016920  0.039227  0.014735
			-0.028441  0.018527 -0.010277
			-0.026879  0.022450 -0.010861
			-0.026399  0.014468 -0.017807
			-0.022614  0.024911 -0.009358
			-0.021851  0.041388 -0.022856
			-0.024438  0.039307 -0.017753
			-0.019870  0.006125 -0.014104
			-0.019622  0.012291 -0.005620
			-0.021740  0.014945  0.000000
			-0.019622  0.012291  0.005620
			-0.028060  0.026258 -0.004411
			-0.032454  0.022910 -0.004293
			-0.038871  0.027085  0.000000
			-0.028060  0.026258  0.004411
			-0.032454  0.022910  0.004293
			-0.022614  0.024911  0.009358
			-0.021851  0.041388  0.022856
			-0.024438  0.039307  0.017753
			-0.026879  0.022450  0.010861
			-0.028441  0.018527  0.010277
			-0.026399  0.014468  0.017807
			-0.019870  0.006125  0.014104
			-0.033252  0.022455 -0.004482
			-0.025102  0.013634 -0.008077
			-0.030876  0.020296 -0.005259
			-0.031698  0.017480  0.000000
			-0.025102  0.013634  0.008077
			-0.030876  0.020296  0.005259
			-0.041071  0.024710  0.000000
			-0.033252  0.022455  0.004482
			-0.036180  0.021192 -0.004884
			-0.034478  0.019484 -0.004902
			-0.036042  0.017514 -0.003805
			-0.037359  0.017382  0.000000
			-0.036042  0.017514  0.003805
			-0.034478  0.019484  0.004902
			-0.040686  0.021779 -0.004946
			-0.041531  0.021082  0.000000
			-0.040686  0.021779  0.004946
			-0.036180  0.021192  0.004884
			-0.040171  0.019549 -0.005763
			-0.039858  0.018370  0.000000
			-0.040171  0.019549  0.005763
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 3 1 
					0 4 3 
					0 5 6 
					6 7 0 
					0 7 8 
					8 4 0 
					2 9 0 
					9 10 0 
					10 11 0 
					12 5 0 
					0 13 12 
					14 13 0 
					0 11 14 
					2 1 15 
					15 1 3 
					16 2 15 
					17 9 2 
					17 2 16 
					18 15 3 
					18 3 4 
					19 18 4 
					19 4 8 
					20 6 5 
					21 20 5 
					5 12 22 
					5 22 21 
					6 20 7 
					7 23 24 
					8 7 24 
					25 23 7 
					20 25 7 
					24 19 8 
					10 9 17 
					10 17 26 
					11 10 26 
					11 26 27 
					14 11 27 
					13 22 12 
					28 29 13 
					28 13 14 
					13 29 30 
					13 30 22 
					14 27 28 
					15 31 32 
					16 15 32 
					31 15 18 
					32 17 16 
					32 33 17 
					26 17 33 
					34 31 18 
					34 18 19 
					35 34 19 
					35 19 24 
					20 21 36 
					25 20 36 
					37 36 21 
					21 38 37 
					38 21 22 
					38 22 30 
					23 39 40 
					24 23 40 
					41 23 25 
					41 39 23 
					40 35 24 
					41 25 36 
					26 33 42 
					27 26 42 
					27 42 43 
					28 27 43 
					28 43 44 
					44 29 28 
					44 45 29 
					30 29 46 
					29 45 46 
					38 30 46 
					31 47 48 
					32 31 48 
					47 31 34 
					48 33 32 
					48 49 33 
					42 33 49 
					50 47 34 
					50 34 35 
					51 50 35 
					51 35 40 
					52 36 37 
					52 53 36 
					53 41 36 
					54 52 37 
					37 38 55 
					37 55 54 
					38 56 55 
					38 46 56 
					39 57 58 
					40 39 58 
					59 39 60 
					39 59 61 
					57 39 61 
					41 60 39 
					58 51 40 
					41 62 60 
					62 41 53 
					42 49 63 
					43 42 63 
					43 63 64 
					44 43 64 
					44 64 65 
					65 45 44 
					65 66 45 
					67 45 68 
					69 68 45 
					69 45 66 
					45 67 46 
					67 70 46 
					56 46 70 
					47 71 72 
					48 47 72 
					71 47 50 
					72 49 48 
					72 73 49 
					63 49 73 
					74 71 50 
					74 50 51 
					75 74 51 
					75 51 58 
					76 52 54 
					77 53 52 
					76 77 52 
					53 77 62 
					78 76 54 
					54 55 79 
					54 79 78 
					55 56 80 
					55 80 79 
					70 80 56 
					57 81 82 
					58 57 82 
					57 61 83 
					81 57 83 
					82 75 58 
					61 59 60 
					60 84 85 
					86 60 85 
					86 61 60 
					87 84 60 
					62 87 60 
					88 89 61 
					86 88 61 
					61 89 83 
					90 62 91 
					92 91 62 
					93 92 62 
					62 90 87 
					77 93 62 
					63 73 94 
					64 63 94 
					64 94 95 
					65 64 95 
					65 95 96 
					96 66 65 
					96 97 66 
					98 69 66 
					98 66 97 
					99 100 67 
					99 67 101 
					67 68 69 
					67 69 101 
					67 100 102 
					67 102 70 
					69 103 104 
					69 104 101 
					98 103 69 
					91 70 105 
					70 91 92 
					70 92 106 
					102 105 70 
					70 106 80 
					71 107 108 
					72 71 108 
					107 71 74 
					108 73 72 
					108 109 73 
					94 73 109 
					110 107 74 
					74 75 111 
					110 74 111 
					75 82 112 
					111 75 112 
					76 113 114 
					114 115 76 
					115 93 76 
					77 76 93 
					116 76 78 
					116 117 76 
					76 117 113 
					78 79 116 
					118 119 79 
					79 120 118 
					79 106 120 
					106 79 80 
					79 121 116 
					119 121 79 
					82 81 112 
					81 83 117 
					81 117 112 
					83 113 117 
					89 122 83 
					123 83 122 
					113 83 123 
					84 124 125 
					85 84 126 
					84 127 126 
					87 124 84 
					125 128 84 
					128 127 84 
					85 126 129 
					122 86 85 
					122 85 130 
					130 85 129 
					122 88 86 
					131 87 90 
					87 131 132 
					124 87 132 
					88 133 134 
					89 88 134 
					88 135 133 
					122 135 88 
					89 134 122 
					136 90 137 
					137 90 91 
					136 131 90 
					91 105 137 
					92 138 139 
					140 138 92 
					141 140 92 
					92 93 141 
					139 142 92 
					92 142 143 
					92 143 144 
					144 106 92 
					93 145 141 
					115 145 93 
					94 109 146 
					147 95 94 
					147 94 146 
					148 96 95 
					148 95 147 
					148 97 96 
					121 98 97 
					148 121 97 
					121 119 98 
					98 149 103 
					149 98 150 
					150 98 119 
					151 152 99 
					152 100 99 
					99 101 149 
					153 99 149 
					151 99 153 
					154 155 100 
					152 156 100 
					100 155 102 
					100 157 154 
					100 156 157 
					101 104 149 
					105 102 158 
					159 158 102 
					159 102 155 
					149 160 103 
					160 104 103 
					160 161 104 
					161 162 104 
					104 162 149 
					137 105 163 
					105 158 163 
					144 164 106 
					106 164 120 
					116 107 110 
					116 108 107 
					109 108 116 
					146 109 116 
					116 110 111 
					116 111 112 
					116 112 117 
					123 114 113 
					114 123 129 
					114 129 165 
					166 114 165 
					166 145 114 
					115 114 145 
					147 146 116 
					148 147 116 
					121 148 116 
					119 118 150 
					151 150 118 
					167 151 118 
					167 118 168 
					118 164 168 
					164 118 120 
					134 135 122 
					130 123 122 
					123 130 129 
					125 124 132 
					169 170 125 
					171 169 125 
					132 171 125 
					172 125 170 
					125 172 128 
					126 173 129 
					174 173 126 
					174 126 127 
					165 174 127 
					166 165 127 
					127 128 145 
					166 127 145 
					145 128 141 
					140 128 172 
					141 128 140 
					165 129 173 
					131 175 132 
					136 175 131 
					175 171 132 
					133 135 134 
					176 136 137 
					176 175 136 
					177 176 137 
					137 163 178 
					137 178 177 
					139 138 179 
					138 140 179 
					180 181 139 
					179 180 139 
					182 142 139 
					139 181 183 
					139 183 182 
					140 172 179 
					182 143 142 
					182 184 143 
					184 157 143 
					143 157 144 
					144 157 164 
					149 162 160 
					149 150 153 
					151 153 150 
					151 185 152 
					185 151 167 
					152 185 186 
					156 152 186 
					159 155 154 
					154 187 188 
					154 188 189 
					154 189 159 
					187 154 184 
					157 184 154 
					156 186 167 
					156 167 168 
					164 157 156 
					164 156 168 
					159 190 158 
					158 190 163 
					159 189 190 
					160 162 161 
					163 190 178 
					174 165 173 
					185 167 186 
					170 169 191 
					169 192 193 
					191 169 193 
					169 171 192 
					172 170 180 
					180 170 191 
					171 175 192 
					179 172 180 
					192 175 176 
					193 192 176 
					194 193 176 
					194 176 177 
					177 178 194 
					178 190 195 
					178 195 196 
					178 196 194 
					180 191 181 
					191 197 181 
					181 197 198 
					181 198 183 
					183 184 182 
					183 187 184 
					198 187 183 
					198 188 187 
					196 195 188 
					196 188 198 
					195 189 188 
					195 190 189 
					191 199 197 
					191 193 200 
					199 191 200 
					193 194 201 
					200 193 201 
					202 201 194 
					194 203 202 
					203 194 196 
					204 196 198 
					203 196 204 
					199 205 197 
					197 205 206 
					197 207 208 
					197 208 198 
					206 207 197 
					204 198 208 
					199 200 201 
					209 199 201 
					199 209 205 
					209 201 202 
					210 209 202 
					202 203 211 
					202 211 210 
					203 204 208 
					203 208 211 
					205 209 210 
					206 205 210 
					210 207 206 
					210 211 207 
					207 211 208 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
