<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="142" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="280">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.077522 -0.996757 -0.021606
			-0.171433 -0.929340  0.327014
			-0.010570 -0.891635  0.452632
			 0.101143 -0.950194  0.294792
			 0.161520 -0.986810 -0.010848
			-0.144365 -0.984818 -0.096396
			-0.146671 -0.896851 -0.417308
			 0.201441 -0.955833 -0.214019
			 0.009791 -0.789457  0.613728
			-0.235794 -0.812468  0.533195
			-0.061597 -0.809691  0.583615
			 0.778590 -0.625613  0.049061
			 0.876039 -0.190971  0.442817
			 0.321572 -0.725733  0.608197
			-0.115144 -0.981157  0.155154
			-0.212032 -0.748968 -0.627765
			-0.217929 -0.897318 -0.383832
			-0.082705 -0.808054 -0.583274
			 0.436640 -0.635281 -0.636995
			-0.269355 -0.739543 -0.616866
			-0.432281 -0.735607 -0.521551
			 0.881007 -0.335990 -0.333072
			 0.744167 -0.181834 -0.642769
			 0.531201 -0.339659  0.776181
			 0.368400  0.158649  0.916031
			 0.299134 -0.716806  0.629847
			-0.010087  0.119003  0.992843
			-0.334154 -0.662441  0.670457
			-0.144539 -0.770497  0.620840
			-0.406579 -0.508090  0.759301
			 0.312488 -0.430400  0.846822
			 0.042943  0.297438  0.953775
			 0.785161  0.603877 -0.137311
			 0.641619 -0.366245  0.673936
			 0.620870  0.749249  0.230535
			 0.333785 -0.850513  0.406466
			-0.025462 -0.999666  0.004367
			-0.139801 -0.960871 -0.239129
			-0.391725 -0.668880 -0.631784
			-0.112190 -0.673499 -0.730625
			-0.054480 -0.497745 -0.865611
			-0.051023 -0.472144 -0.880044
			 0.040910 -0.362447 -0.931106
			 0.410007 -0.278576 -0.868499
			 0.669925  0.620948 -0.406969
			 0.752553  0.313624 -0.579055
			-0.456586 -0.794198 -0.400973
			-0.151148 -0.253375 -0.955487
			 0.305696  0.639316  0.705567
			 0.414413  0.838130  0.354683
			-0.643830  0.233485  0.728676
			 0.248088  0.889354  0.384059
			-0.300293  0.899838  0.316409
			-0.499808 -0.475777  0.723760
			-0.541618  0.727710  0.420818
			 0.449755 -0.458780  0.766316
			 0.615619  0.001572  0.788042
			 0.530988  0.711310  0.460533
			 0.047783  0.921069  0.386457
			 0.381961  0.710576 -0.590921
			 0.152900  0.985474 -0.073912
			 0.572920 -0.311727  0.758017
			 0.495375 -0.596842  0.631176
			 0.186048 -0.827314  0.530036
			-0.065670 -0.975021  0.212181
			-0.356449 -0.913548  0.195894
			-0.964074 -0.260788  0.050497
			-0.125666 -0.781442 -0.611193
			-0.036451 -0.776110 -0.629543
			-0.914294 -0.263253 -0.307838
			-0.876279 -0.291507 -0.383613
			-0.491832  0.364517 -0.790714
			-0.955751 -0.293535  0.019437
			-0.018400  0.026871 -0.999470
			 0.044871 -0.313724 -0.948453
			 0.181674  0.506309 -0.842998
			 0.225164  0.279329 -0.933422
			 0.009156  0.349740 -0.936802
			-0.794730 -0.243846 -0.555827
			-0.222326  0.974204  0.038710
			 0.588273  0.392328  0.707116
			 0.514259  0.813138 -0.272661
			 0.545887  0.833413  0.086202
			 0.356825  0.847333  0.393322
			 0.109065  0.990609 -0.082459
			-0.375689  0.926583  0.017345
			-0.296800  0.951255 -0.083811
			 0.169025  0.219436  0.960874
			 0.201840 -0.371987  0.906028
			-0.243292 -0.593331  0.767312
			-0.447731 -0.697203  0.559862
			-0.810376 -0.363704  0.459358
			-0.935603  0.173437  0.307516
			-0.868626 -0.024595  0.494858
			-0.754863  0.653879  0.051221
			-0.738597  0.661302 -0.130972
			-0.815172  0.577168  0.048700
			-0.757886 -0.461384 -0.461231
			 0.067841 -0.374708 -0.924657
			-0.838322 -0.327032 -0.436195
			-0.949848  0.312579  0.009124
			-0.023947  0.799464 -0.600236
			 0.401588  0.406632 -0.820596
			 0.464162  0.261084 -0.846398
			 0.355933  0.750117 -0.557347
			 0.047936  0.702649  0.709920
			 0.391061  0.868084 -0.305781
			 0.212983  0.977039  0.005701
			 0.108477  0.987159  0.117263
			-0.704022  0.652767  0.279728
			-0.321449  0.097389  0.941905
			-0.798828  0.311312  0.514742
			-0.537986 -0.132224  0.832519
			-0.921611 -0.013780  0.387870
			-0.889240  0.441874  0.118318
			-0.889027  0.452807  0.067808
			-0.966959  0.240710  0.083960
			-0.904806  0.359456  0.228293
			-0.732426 -0.086390 -0.675344
			-0.990533  0.134890  0.025474
			-0.952952  0.032885  0.301331
			-0.056780  0.112864 -0.991987
			 0.249790  0.114551 -0.961500
			 0.420655  0.485829 -0.766173
			-0.616611  0.572297  0.540618
			 0.050391  0.626217 -0.778019
			-0.323114  0.935481 -0.143084
			-0.293399  0.952633  0.080043
			-0.274209  0.954444  0.117674
			-0.957221  0.235183  0.168575
			-0.999896 -0.005848  0.013177
			-0.964559  0.030695  0.262076
			-0.980521 -0.169661  0.098959
			-0.931779  0.231124  0.279945
			-0.950640 -0.289919  0.110592
			-0.994874  0.100251 -0.013260
			-0.699464  0.591463 -0.401150
			-0.903010  0.407688  0.135511
			-0.950781 -0.203562  0.233618
			-0.290189  0.727049 -0.622246
			-0.458312  0.846517 -0.270850
			-0.973572  0.187739  0.130042
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.061452 -0.170771 -0.012174
			 0.060256 -0.170253 -0.010058
			 0.058993 -0.170264 -0.009820
			 0.061592 -0.170769 -0.010243
			 0.062248 -0.170902 -0.011754
			 0.057861 -0.170777 -0.012484
			 0.058004 -0.170592 -0.013045
			 0.062414 -0.170597 -0.013366
			 0.060613 -0.170055 -0.008789
			 0.058681 -0.169426 -0.009075
			 0.057091 -0.170229 -0.010259
			 0.063367 -0.170386 -0.011962
			 0.062950 -0.170104 -0.010159
			 0.062078 -0.170120 -0.009632
			 0.051142 -0.169351 -0.010325
			 0.056759 -0.170268 -0.012984
			 0.054495 -0.169765 -0.012685
			 0.060831 -0.169963 -0.014977
			 0.061869 -0.170381 -0.014311
			 0.059028 -0.169498 -0.015193
			 0.056958 -0.168994 -0.014266
			 0.063165 -0.170362 -0.012969
			 0.062661 -0.170272 -0.013932
			 0.061945 -0.169477 -0.008803
			 0.059883 -0.168560 -0.007645
			 0.061803 -0.169829 -0.009246
			 0.057770 -0.167831 -0.007434
			 0.057646 -0.168431 -0.007843
			 0.057148 -0.168632 -0.008724
			 0.056768 -0.168307 -0.008340
			 0.053678 -0.168291 -0.008108
			 0.055754 -0.168515 -0.008471
			 0.063064 -0.169825 -0.012451
			 0.062648 -0.169908 -0.009641
			 0.061568 -0.168652 -0.008881
			 0.049910 -0.168094 -0.006320
			 0.047070 -0.168727 -0.008464
			 0.050987 -0.169244 -0.012005
			 0.056464 -0.168631 -0.014218
			 0.055247 -0.169172 -0.013774
			 0.056033 -0.168990 -0.014002
			 0.052914 -0.168450 -0.014276
			 0.059878 -0.169063 -0.015767
			 0.061287 -0.169793 -0.015205
			 0.061736 -0.169347 -0.014363
			 0.061371 -0.169286 -0.015054
			 0.056373 -0.168340 -0.015052
			 0.058490 -0.168581 -0.015789
			 0.059102 -0.167910 -0.007735
			 0.059784 -0.167912 -0.008187
			 0.057251 -0.167732 -0.007587
			 0.058114 -0.167352 -0.007987
			 0.057368 -0.167240 -0.007925
			 0.056824 -0.167770 -0.007847
			 0.056553 -0.167918 -0.008900
			 0.050504 -0.167408 -0.006007
			 0.051761 -0.167180 -0.006767
			 0.053906 -0.167153 -0.008831
			 0.055898 -0.167904 -0.009196
			 0.060006 -0.168551 -0.015591
			 0.058477 -0.167697 -0.011918
			 0.050015 -0.167211 -0.005542
			 0.049600 -0.167790 -0.005569
			 0.048073 -0.168560 -0.005527
			 0.047590 -0.168820 -0.006305
			 0.046138 -0.168728 -0.007345
			 0.044665 -0.168404 -0.009794
			 0.051017 -0.168606 -0.013538
			 0.048819 -0.168241 -0.013680
			 0.044838 -0.168384 -0.012032
			 0.045753 -0.167978 -0.013633
			 0.055811 -0.168353 -0.014211
			 0.055943 -0.168023 -0.014571
			 0.055084 -0.168601 -0.014124
			 0.050412 -0.167374 -0.014341
			 0.054712 -0.168195 -0.013984
			 0.053153 -0.167726 -0.014194
			 0.057043 -0.168082 -0.015605
			 0.055753 -0.168034 -0.015189
			 0.056941 -0.167673 -0.009616
			 0.049107 -0.164891 -0.004956
			 0.054308 -0.167386 -0.012856
			 0.049566 -0.163561 -0.008380
			 0.048661 -0.163684 -0.006378
			 0.056174 -0.167574 -0.011533
			 0.056445 -0.167396 -0.015105
			 0.056483 -0.167794 -0.013768
			 0.048271 -0.165753 -0.004117
			 0.047917 -0.167276 -0.004268
			 0.047665 -0.167854 -0.004639
			 0.046996 -0.168133 -0.005248
			 0.046415 -0.168372 -0.006363
			 0.045068 -0.168441 -0.008703
			 0.045597 -0.168415 -0.007596
			 0.046812 -0.166777 -0.010174
			 0.045087 -0.168103 -0.011627
			 0.045977 -0.167297 -0.013214
			 0.046000 -0.167088 -0.014897
			 0.047429 -0.166272 -0.015530
			 0.045935 -0.167568 -0.014354
			 0.045824 -0.167420 -0.013736
			 0.055349 -0.168014 -0.013720
			 0.052320 -0.166609 -0.014205
			 0.051337 -0.166020 -0.014395
			 0.054191 -0.167712 -0.013594
			 0.048633 -0.164701 -0.004856
			 0.049106 -0.163342 -0.014058
			 0.048922 -0.163289 -0.009224
			 0.049021 -0.163260 -0.008370
			 0.047631 -0.163818 -0.006205
			 0.047556 -0.166264 -0.004240
			 0.047465 -0.165381 -0.004570
			 0.047307 -0.166944 -0.004289
			 0.046878 -0.167294 -0.004896
			 0.047348 -0.166280 -0.008653
			 0.047142 -0.166374 -0.010753
			 0.047321 -0.165575 -0.010164
			 0.046132 -0.166954 -0.013629
			 0.045566 -0.165515 -0.015386
			 0.046021 -0.167017 -0.014255
			 0.045716 -0.164988 -0.014874
			 0.046926 -0.165397 -0.015715
			 0.048790 -0.164868 -0.015405
			 0.048966 -0.164189 -0.015124
			 0.047473 -0.164790 -0.005028
			 0.048308 -0.163910 -0.015170
			 0.047371 -0.163055 -0.013748
			 0.047244 -0.163073 -0.010900
			 0.047371 -0.163212 -0.010001
			 0.047095 -0.163494 -0.008769
			 0.047690 -0.164699 -0.007954
			 0.047580 -0.165044 -0.009686
			 0.047651 -0.164614 -0.009046
			 0.047152 -0.165368 -0.012365
			 0.047248 -0.164184 -0.009995
			 0.047547 -0.165006 -0.010829
			 0.045820 -0.164494 -0.015182
			 0.046342 -0.163713 -0.013689
			 0.047445 -0.164629 -0.011785
			 0.046325 -0.164043 -0.014986
			 0.046925 -0.163341 -0.014241
			 0.046962 -0.163506 -0.010082
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					4 3 0 
					5 0 2 
					5 6 0 
					6 7 0 
					4 0 7 
					8 9 1 
					3 8 1 
					1 9 2 
					5 2 10 
					2 9 10 
					11 3 4 
					12 3 11 
					13 8 3 
					12 13 3 
					7 11 4 
					14 5 10 
					15 6 5 
					16 15 5 
					16 5 14 
					17 18 6 
					19 17 6 
					19 6 20 
					6 15 20 
					18 7 6 
					21 7 22 
					22 7 18 
					11 7 21 
					23 24 8 
					23 8 25 
					26 8 24 
					27 9 8 
					27 8 26 
					25 8 13 
					10 9 28 
					27 29 9 
					29 28 9 
					14 10 30 
					30 10 31 
					28 29 10 
					29 31 10 
					12 11 32 
					11 21 32 
					33 13 12 
					12 32 34 
					33 12 34 
					25 13 33 
					14 30 35 
					14 35 36 
					37 16 14 
					37 14 36 
					20 15 38 
					15 16 39 
					40 38 15 
					39 40 15 
					16 37 41 
					41 39 16 
					42 43 17 
					19 42 17 
					17 43 18 
					18 44 22 
					18 45 44 
					43 45 18 
					20 46 19 
					19 47 42 
					19 46 47 
					46 20 38 
					21 22 32 
					44 32 22 
					24 23 34 
					23 25 33 
					34 23 33 
					48 26 24 
					24 34 49 
					24 49 48 
					26 50 27 
					51 26 48 
					26 52 50 
					52 26 51 
					50 53 27 
					27 53 29 
					53 50 29 
					54 29 50 
					29 54 31 
					55 35 30 
					56 55 30 
					56 30 57 
					57 30 31 
					57 31 58 
					54 58 31 
					34 32 44 
					34 44 59 
					60 49 34 
					60 34 59 
					61 62 35 
					35 62 63 
					35 55 61 
					35 63 64 
					35 64 36 
					65 36 64 
					66 37 36 
					66 36 65 
					67 41 37 
					68 67 37 
					66 69 37 
					69 70 37 
					70 68 37 
					38 71 72 
					72 46 38 
					71 38 40 
					40 39 73 
					39 41 73 
					71 40 73 
					41 67 74 
					73 41 75 
					41 74 76 
					41 76 75 
					42 59 43 
					42 47 59 
					45 43 59 
					45 59 44 
					77 46 78 
					46 77 47 
					78 46 72 
					59 47 77 
					48 49 51 
					51 49 60 
					54 50 52 
					52 51 60 
					60 79 52 
					79 54 52 
					54 79 58 
					55 56 61 
					80 56 57 
					61 56 80 
					81 82 57 
					80 57 83 
					57 82 83 
					58 84 57 
					84 81 57 
					84 58 79 
					77 60 59 
					77 85 60 
					79 60 84 
					60 85 86 
					86 84 60 
					87 88 61 
					80 87 61 
					88 62 61 
					63 62 88 
					89 90 63 
					64 63 90 
					88 89 63 
					65 64 91 
					64 90 91 
					65 92 66 
					91 93 65 
					65 93 92 
					92 94 66 
					95 69 66 
					95 66 96 
					94 96 66 
					67 68 74 
					68 97 98 
					70 99 68 
					99 97 68 
					74 68 98 
					95 70 69 
					70 95 96 
					70 96 100 
					70 100 99 
					73 101 71 
					72 71 85 
					86 71 101 
					71 86 85 
					85 78 72 
					101 73 75 
					102 76 74 
					74 98 103 
					74 103 102 
					75 76 104 
					75 104 101 
					102 104 76 
					78 85 77 
					80 83 105 
					80 105 87 
					104 102 81 
					102 103 81 
					106 81 103 
					104 81 84 
					106 82 81 
					82 106 107 
					82 107 108 
					82 108 83 
					83 108 109 
					109 105 83 
					84 86 101 
					84 101 104 
					110 87 111 
					88 87 110 
					105 111 87 
					110 112 88 
					89 88 112 
					90 89 113 
					89 112 113 
					113 91 90 
					91 113 114 
					93 91 114 
					92 93 114 
					114 94 92 
					94 115 96 
					94 114 116 
					115 94 116 
					100 96 117 
					115 117 96 
					97 118 98 
					119 97 99 
					97 119 120 
					118 97 120 
					98 118 121 
					122 103 98 
					122 98 121 
					119 99 100 
					117 119 100 
					123 103 122 
					123 106 103 
					109 124 105 
					124 111 105 
					125 106 123 
					106 125 126 
					126 127 106 
					107 106 127 
					107 128 108 
					128 107 127 
					109 108 129 
					129 108 128 
					124 109 111 
					111 109 130 
					130 109 129 
					110 111 112 
					130 113 111 
					111 113 112 
					113 130 114 
					131 114 132 
					130 132 114 
					114 131 116 
					115 133 117 
					115 116 133 
					131 134 116 
					116 134 135 
					135 133 116 
					119 117 120 
					133 120 117 
					120 136 118 
					136 121 118 
					120 137 136 
					138 137 120 
					138 120 133 
					125 122 121 
					136 125 121 
					123 122 125 
					125 136 139 
					139 140 125 
					125 140 126 
					136 126 140 
					137 126 136 
					127 126 137 
					137 141 127 
					127 141 128 
					141 129 128 
					129 132 130 
					134 132 129 
					141 134 129 
					132 134 131 
					135 138 133 
					134 141 138 
					134 138 135 
					140 139 136 
					138 141 137 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
