<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.894539 -0.446990  0.000365
			 0.760484 -0.616964  0.202534
			 0.390963 -0.920406  0.000017
			 0.914015  0.256140  0.314593
			 0.990177  0.139821  0.000238
			 0.760581 -0.616929 -0.202274
			 0.914095  0.256237 -0.314281
			 0.704034 -0.497507  0.506777
			 0.430427 -0.503163  0.749373
			-0.226073 -0.973931 -0.018689
			-0.269740 -0.962933  0.000062
			-0.193626 -0.969530  0.150072
			 0.098597 -0.660667  0.744175
			 0.704032 -0.497514 -0.506773
			 0.430425 -0.503163 -0.749374
			-0.226070 -0.973932  0.018666
			-0.193619 -0.969531 -0.150068
			 0.098606 -0.660667 -0.744174
			 0.846533  0.532336  0.000194
			 0.762241  0.526323  0.376792
			 0.532474  0.345999  0.772500
			 0.762345  0.526409 -0.376463
			 0.532471  0.346000 -0.772502
			 0.147513  0.199998  0.968628
			-0.798161 -0.593451  0.103702
			-0.895222 -0.445621  0.000143
			-0.798178 -0.593455 -0.103553
			-0.240356 -0.081449  0.967262
			-0.733914 -0.520957  0.435861
			-0.801038 -0.491145 -0.342220
			 0.147512  0.199996 -0.968629
			-0.733914 -0.520957 -0.435861
			-0.240352 -0.081451 -0.967262
			-0.801038 -0.491145  0.342220
			 0.975011  0.222157  0.000385
			 0.914532  0.108961  0.389563
			 0.486923  0.356160  0.797531
			 0.914648  0.108993 -0.389281
			 0.486922  0.356156 -0.797533
			 0.151147  0.166955  0.974310
			-0.944188 -0.253361 -0.210515
			-0.976993 -0.213270 -0.000082
			-0.944186 -0.253353  0.210535
			-0.173985  0.020729  0.984530
			 0.037358 -0.428804  0.902625
			-0.607431 -0.590708 -0.531123
			 0.151150  0.166950 -0.974311
			 0.037359 -0.428802 -0.902625
			-0.173988  0.020733 -0.984529
			-0.607431 -0.590708  0.531122
			 0.990932  0.134366  0.000216
			 0.941594  0.076337  0.327985
			 0.599070 -0.103895  0.793928
			 0.941647  0.076360 -0.327827
			 0.599068 -0.103890 -0.793929
			 0.276835 -0.219808  0.935439
			-0.924429 -0.269418 -0.269897
			-0.402190 -0.065223 -0.913230
			-0.960817 -0.277185 -0.000160
			-0.924451 -0.269446  0.269795
			-0.402190 -0.065223  0.913230
			 0.068273 -0.254302  0.964712
			 0.136332  0.046804  0.989557
			 0.010430 -0.972394  0.233110
			-0.002841 -0.587052  0.809544
			 0.051182 -0.013218  0.998602
			 0.616224  0.280467 -0.735939
			 0.276833 -0.219803 -0.935441
			 0.136339  0.046801 -0.989556
			 0.068274 -0.254304 -0.964711
			-0.002839 -0.587051 -0.809545
			 0.010429 -0.972394 -0.233111
			 0.051184 -0.013218 -0.998602
			 0.616398  0.280568  0.735754
			 0.724479  0.689297  0.000192
			 0.560815  0.749827  0.351063
			 0.628636 -0.067248  0.774786
			 0.560855  0.749865 -0.350920
			 0.628638 -0.067247 -0.774786
			 0.244789 -0.248536  0.937181
			-0.634580  0.671635 -0.382380
			-0.217953 -0.072255 -0.973281
			-0.840529  0.541767 -0.000237
			-0.634644  0.671661  0.382229
			-0.217953 -0.072255  0.973281
			-0.040193 -0.200483  0.978872
			-0.228248  0.161496  0.960116
			-0.077247  0.656086  0.750723
			-0.553917 -0.371762  0.744963
			-0.432471 -0.560329  0.706399
			 0.302444 -0.067941  0.950743
			 0.808083 -0.258551  0.529295
			 0.099677  0.268128  0.958213
			-0.459732 -0.581051  0.671584
			 0.939200  0.343370 -0.000278
			 0.941093 -0.185897 -0.282465
			-0.736618  0.676310  0.000035
			-0.467817  0.427249 -0.773696
			 0.244791 -0.248537 -0.937180
			-0.228248  0.161496 -0.960116
			-0.040189 -0.200490 -0.978871
			-0.077247  0.656086 -0.750723
			-0.432473 -0.560329 -0.706398
			-0.553921 -0.371760 -0.744961
			 0.302445 -0.067942 -0.950742
			 0.808081 -0.258550 -0.529298
			-0.459729 -0.581043 -0.671594
			 0.099670  0.268126 -0.958214
			 0.941123 -0.185943  0.282333
			-0.467741  0.427298  0.773715
			-0.222749  0.974876  0.000000
			 0.271496  0.797868  0.538234
			 0.110672  0.546042  0.830415
			 0.271492  0.797871 -0.538233
			 0.110675  0.546045 -0.830413
			-0.232041  0.415170  0.879654
			-0.234334  0.949440 -0.208931
			-0.047500  0.917710  0.394402
			-0.714641  0.406908 -0.568959
			-0.383603  0.827218  0.410560
			-0.047502  0.917710 -0.394400
			-0.234340  0.949437  0.208936
			-0.714638  0.406908  0.568962
			-0.383593  0.827227 -0.410551
			 0.241762  0.599889  0.762683
			 0.191484  0.708264  0.679482
			-0.224920  0.529892  0.817695
			 0.031665 -0.425736  0.904293
			 0.085154  0.456068  0.885861
			-0.859153 -0.018153  0.511397
			-0.094698  0.010381  0.995452
			-0.876234 -0.216796 -0.430363
			-0.884108 -0.101700 -0.456082
			 0.944634  0.028480  0.326888
			-0.339287  0.214861  0.915816
			 0.106214  0.027857  0.993953
			-0.741769 -0.584368  0.329077
			-0.983875 -0.038343 -0.174697
			-0.052603 -0.998615 -0.000165
			 0.175010 -0.878004 -0.445511
			-0.978279  0.207291  0.000204
			-0.809306  0.401064  0.429152
			-0.944837  0.229708  0.233489
			-0.952501  0.203775 -0.226315
			-0.809274  0.401305 -0.428987
			-0.944821  0.229911 -0.233355
			-0.952485  0.203760  0.226395
			-0.741674  0.201059 -0.639918
			-0.232037  0.415179 -0.879651
			 0.241761  0.599886 -0.762685
			 0.191481  0.708268 -0.679479
			-0.094698  0.010381 -0.995452
			-0.859153 -0.018153 -0.511397
			 0.031666 -0.425733 -0.904294
			-0.876235 -0.216809  0.430355
			-0.884108 -0.101708  0.456081
			-0.224928  0.529897 -0.817690
			 0.085147  0.456069 -0.885862
			 0.944632  0.028476 -0.326892
			-0.339287  0.214861 -0.915816
			-0.741762 -0.584367 -0.329096
			 0.106215  0.027857 -0.993953
			-0.983877 -0.038346  0.174685
			 0.175143 -0.878011  0.445446
			-0.741676  0.201054  0.639918
			-0.862206  0.482742 -0.153498
			-0.351807  0.197371 -0.915028
			-0.862206  0.482739  0.153501
			-0.351814  0.197371  0.915026
			-0.346832 -0.365906  0.863609
			-0.090349 -0.324647  0.941510
			-0.900318 -0.011969  0.435068
			-0.732594 -0.540383  0.413875
			-0.695847 -0.630391  0.344098
			-0.954432  0.163768  0.249478
			 0.319899 -0.886837  0.333445
			-0.026860 -0.944459 -0.327529
			 0.507085 -0.861896 -0.000215
			-0.026982 -0.944523  0.327335
			-0.832866  0.124990  0.539176
			-0.963875  0.266353 -0.000910
			-0.823863 -0.027598  0.566117
			-0.832931  0.124963 -0.539083
			-0.823624 -0.026990 -0.566494
			-0.954433  0.163765 -0.249477
			-0.732594 -0.540383 -0.413875
			-0.900319 -0.011968 -0.435066
			-0.695847 -0.630391 -0.344098
			-0.346831 -0.365902 -0.863611
			-0.090349 -0.324647 -0.941510
			 0.319899 -0.886837 -0.333445
			-0.659478 -0.058272  0.749462
			 0.019052 -0.892359  0.450923
			-0.202840 -0.970069 -0.133498
			 0.816973 -0.576676  0.000007
			-0.202840 -0.970069  0.133498
			 0.019039 -0.892340 -0.450962
			-0.880800  0.473486 -0.001401
			-0.658358 -0.058404 -0.750436
			-0.168466  0.171732  0.970632
			-0.010137 -0.041538  0.999085
			 0.716499 -0.527795  0.456138
			 0.372808 -0.927908 -0.000522
			 0.716486 -0.527777 -0.456179
			-0.010162 -0.041536 -0.999085
			-0.830222 -0.114266  0.545595
			-0.928345 -0.371718 -0.000977
			-0.167548  0.171102 -0.970903
			-0.829175 -0.114478 -0.547142
			-0.153992 -0.691792  0.705486
			-0.464339 -0.885657 -0.000990
			-0.153232 -0.691315 -0.706119
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.021288  0.206686  0.000025
			-0.022191  0.206718  0.008244
			-0.036340  0.201009  0.000003
			-0.022166  0.211611  0.009890
			-0.020912  0.208715  0.000027
			-0.022191  0.206718 -0.008244
			-0.022166  0.211611 -0.009890
			-0.026927  0.205617  0.016798
			-0.034295  0.203129  0.020958
			-0.049509  0.195038  0.004957
			-0.049753  0.195293  0.000003
			-0.049998  0.195161  0.014592
			-0.045713  0.198416  0.021071
			-0.026927  0.205617 -0.016798
			-0.034295  0.203129 -0.020958
			-0.049509  0.195038 -0.004957
			-0.049998  0.195161 -0.014592
			-0.045713  0.198416 -0.021071
			-0.023249  0.213446  0.000027
			-0.024862  0.213430  0.010588
			-0.028160  0.210433  0.017846
			-0.024862  0.213430 -0.010588
			-0.028160  0.210433 -0.017846
			-0.037198  0.208041  0.021676
			-0.052671  0.198822  0.005102
			-0.054813  0.197069  0.000003
			-0.052671  0.198822 -0.005102
			-0.048974  0.203696  0.021829
			-0.053429  0.194638  0.016388
			-0.054054  0.200114  0.009696
			-0.037198  0.208041 -0.021676
			-0.053429  0.194638 -0.016388
			-0.048974  0.203696 -0.021829
			-0.054054  0.200114 -0.009696
			-0.025989  0.223289  0.000030
			-0.027103  0.221700  0.010219
			-0.031213  0.212798  0.017400
			-0.027103  0.221700 -0.010219
			-0.031213  0.212798 -0.017400
			-0.038781  0.212561  0.019719
			-0.054579  0.206836  0.006542
			-0.054082  0.207008  0.000005
			-0.054579  0.206836 -0.006542
			-0.048098  0.208917  0.020660
			-0.052993  0.205696  0.019536
			-0.056637  0.206928  0.010005
			-0.038781  0.212561 -0.019719
			-0.052993  0.205696 -0.019536
			-0.048098  0.208917 -0.020660
			-0.056637  0.206928 -0.010005
			-0.026560  0.227660  0.000031
			-0.027077  0.227060  0.011304
			-0.033229  0.220655  0.018069
			-0.027077  0.227060 -0.011304
			-0.033229  0.220655 -0.018069
			-0.038284  0.219442  0.019809
			-0.058684  0.217428  0.006760
			-0.060826  0.217350  0.010461
			-0.057970  0.217741  0.000008
			-0.058684  0.217428 -0.006760
			-0.060826  0.217350 -0.010461
			-0.048728  0.217076  0.020809
			-0.053871  0.216831  0.021214
			-0.059097  0.203559  0.017599
			-0.057001  0.207363  0.021577
			-0.060485  0.214867  0.025800
			-0.066906  0.210372  0.003840
			-0.038284  0.219442 -0.019809
			-0.053871  0.216831 -0.021214
			-0.048728  0.217076 -0.020809
			-0.057001  0.207363 -0.021577
			-0.059097  0.203559 -0.017599
			-0.060485  0.214867 -0.025800
			-0.066906  0.210372 -0.003840
			-0.027950  0.232343  0.000032
			-0.028736  0.231858  0.011511
			-0.032294  0.225210  0.019126
			-0.028736  0.231858 -0.011511
			-0.032294  0.225210 -0.019126
			-0.038420  0.222971  0.021903
			-0.061246  0.225002  0.008618
			-0.061540  0.222960  0.011233
			-0.059294  0.224474  0.000009
			-0.061246  0.225002 -0.008618
			-0.061540  0.222960 -0.011233
			-0.047669  0.222248  0.022959
			-0.056117  0.221216  0.020500
			-0.062901  0.220823  0.020548
			-0.071129  0.213458  0.023816
			-0.068188  0.209435  0.017700
			-0.063657  0.203667  0.025475
			-0.060370  0.200154  0.010848
			-0.065403  0.213669  0.024502
			-0.065292  0.205704  0.022274
			-0.066103  0.204356  0.000004
			-0.061713  0.188282  0.005268
			-0.076274  0.211154  0.000005
			-0.070753  0.216170  0.007949
			-0.038420  0.222971 -0.021903
			-0.056117  0.221216 -0.020500
			-0.047669  0.222248 -0.022959
			-0.062901  0.220823 -0.020548
			-0.068188  0.209435 -0.017700
			-0.071129  0.213458 -0.023816
			-0.063657  0.203667 -0.025475
			-0.060370  0.200154 -0.010848
			-0.065292  0.205704 -0.022274
			-0.065403  0.213669 -0.024502
			-0.061713  0.188282 -0.005268
			-0.070753  0.216170 -0.007949
			-0.043622  0.228409  0.000010
			-0.033814  0.230284  0.019390
			-0.040989  0.228602  0.022961
			-0.033814  0.230284 -0.019390
			-0.040989  0.228602 -0.022961
			-0.050082  0.225708  0.023101
			-0.071980  0.226211  0.016478
			-0.063108  0.222834  0.014526
			-0.071032  0.223413  0.010364
			-0.059171  0.224996  0.017631
			-0.063108  0.222834 -0.014526
			-0.071980  0.226211 -0.016478
			-0.071032  0.223413 -0.010364
			-0.059171  0.224996 -0.017631
			-0.069179  0.218851  0.021870
			-0.069001  0.222417  0.018944
			-0.078104  0.224432  0.019845
			-0.076734  0.215182  0.014623
			-0.072551  0.219715  0.021910
			-0.073349  0.198998  0.013020
			-0.069313  0.201847  0.015939
			-0.082987  0.212857  0.017561
			-0.074734  0.213053  0.013058
			-0.061726  0.201545  0.012357
			-0.072127  0.194779  0.017556
			-0.069397  0.212971  0.027100
			-0.070458  0.210212  0.024714
			-0.073367  0.217409  0.025062
			-0.065769  0.192958  0.000003
			-0.061167  0.187279  0.006903
			-0.079851  0.196828  0.000004
			-0.076124  0.205343  0.005106
			-0.071910  0.205282  0.008549
			-0.073313  0.211624  0.010714
			-0.076124  0.205343 -0.005106
			-0.071910  0.205282 -0.008549
			-0.073313  0.211624 -0.010714
			-0.071990  0.216422  0.011691
			-0.050082  0.225708 -0.023101
			-0.069179  0.218851 -0.021870
			-0.069001  0.222417 -0.018944
			-0.069313  0.201847 -0.015939
			-0.073349  0.198998 -0.013020
			-0.076734  0.215182 -0.014623
			-0.082987  0.212857 -0.017561
			-0.074734  0.213053 -0.013058
			-0.078104  0.224432 -0.019844
			-0.072551  0.219715 -0.021910
			-0.061726  0.201545 -0.012357
			-0.072127  0.194779 -0.017556
			-0.070458  0.210212 -0.024714
			-0.069397  0.212971 -0.027100
			-0.073367  0.217409 -0.025062
			-0.061167  0.187279 -0.006903
			-0.071990  0.216422 -0.011691
			-0.082947  0.219606  0.016633
			-0.076757  0.219038  0.014339
			-0.082947  0.219606 -0.016633
			-0.076757  0.219038 -0.014339
			-0.080718  0.218549  0.021988
			-0.081938  0.215659  0.017086
			-0.075060  0.195384  0.010569
			-0.074085  0.190603  0.010001
			-0.070364  0.187877  0.017306
			-0.073738  0.201211  0.009115
			-0.061771  0.184599  0.013718
			-0.064408  0.189913  0.005489
			-0.068097  0.192728  0.000001
			-0.064408  0.189913 -0.005489
			-0.078915  0.199869  0.004312
			-0.082825  0.189732 -0.000016
			-0.077441  0.195262  0.004195
			-0.078915  0.199869 -0.004312
			-0.077441  0.195262 -0.004195
			-0.073738  0.201211 -0.009115
			-0.074085  0.190603 -0.010001
			-0.075060  0.195384 -0.010569
			-0.070364  0.187877 -0.017306
			-0.080718  0.218549 -0.021988
			-0.081938  0.215659 -0.017086
			-0.061771  0.184599 -0.013718
			-0.076529  0.190718  0.004376
			-0.075006  0.188764  0.005130
			-0.069495  0.188761  0.007867
			-0.073585  0.183656 -0.000000
			-0.069495  0.188761 -0.007867
			-0.075006  0.188764 -0.005130
			-0.087652  0.179951 -0.000021
			-0.076529  0.190718 -0.004376
			-0.079682  0.181786  0.004761
			-0.075850  0.180255  0.004779
			-0.078068  0.172393  0.003711
			-0.079726  0.171244 -0.000003
			-0.078068  0.172393 -0.003711
			-0.075850  0.180255 -0.004779
			-0.088170  0.175932  0.004814
			-0.088683  0.174213 -0.000023
			-0.079682  0.181786 -0.004761
			-0.088170  0.175932 -0.004814
			-0.085473  0.172717  0.005606
			-0.083988  0.171049 -0.000021
			-0.085473  0.172717 -0.005606
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
