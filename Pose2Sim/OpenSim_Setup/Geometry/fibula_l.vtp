<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="122" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="240">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.874624 -0.483803 -0.031095
			 0.462950 -0.726467  0.507860
			 0.739011 -0.611425 -0.282882
			 0.805211 -0.566507 -0.175228
			 0.388228 -0.078713  0.918196
			 0.962290 -0.064110  0.264362
			-0.340104 -0.891005  0.300732
			-0.173158 -0.174114  0.969382
			 0.831213 -0.552385 -0.062902
			 0.762595 -0.512592 -0.394585
			 0.116839 -0.669675 -0.733406
			-0.365405 -0.753059 -0.547157
			-0.349077 -0.915140 -0.201655
			-0.333797 -0.939614  0.075537
			 0.949009 -0.099350 -0.299185
			 0.609866  0.032438  0.791840
			 0.910591  0.194119  0.364887
			 0.907746  0.273024 -0.318521
			-0.495861 -0.431239  0.753761
			 0.216712 -0.107925  0.970252
			 0.670458 -0.001802 -0.741945
			-0.036091  0.046725 -0.998256
			-0.661758 -0.223937 -0.715491
			-0.805812 -0.569942 -0.160727
			-0.753272 -0.574857  0.319563
			 0.443777  0.237370 -0.864128
			 0.562964  0.091838  0.821363
			 0.956803  0.069733  0.282250
			 0.978611  0.061742 -0.196237
			 0.550153  0.026474 -0.834644
			-0.291843 -0.229082  0.928628
			 0.069846  0.124544  0.989753
			 0.197321  0.216189 -0.956205
			-0.285661  0.100437 -0.953053
			-0.835616 -0.285923 -0.469035
			-0.800669 -0.388844  0.455774
			 0.214304  0.004253 -0.976758
			 0.575258  0.037114  0.817130
			 0.954343  0.008071  0.298604
			 0.950571  0.011304 -0.310303
			 0.626111  0.015200 -0.779586
			-0.012599  0.020243 -0.999716
			-0.461269  0.218500  0.859935
			 0.013965  0.130804  0.991310
			-0.131165  0.098905 -0.986414
			-0.828542  0.484420 -0.280813
			-0.925422  0.343884  0.159182
			-0.817014  0.066685 -0.572748
			 0.763383 -0.029934  0.645252
			 0.982090 -0.025882  0.186626
			 0.800155 -0.030492 -0.599018
			-0.105164 -0.022763 -0.994194
			-0.017778 -0.010255 -0.999789
			-0.826457  0.022396 -0.562554
			-0.723692  0.316268  0.613388
			 0.105305 -0.025243  0.994120
			-0.905356  0.332351  0.264335
			-0.834341  0.539746 -0.112019
			-0.981496  0.064544  0.180277
			 0.674140 -0.000804  0.738603
			 0.967818  0.002486  0.251638
			 0.921309 -0.009960 -0.388703
			 0.028045 -0.051002 -0.998305
			-0.001017 -0.085925 -0.996301
			-0.311666 -0.032840 -0.949624
			-0.991105  0.004597 -0.133006
			-0.852619  0.025560  0.521907
			-0.161308 -0.002356  0.986901
			-0.987422  0.095254 -0.126188
			-0.996683  0.010880 -0.080651
			 0.651729  0.016196  0.758279
			 0.961144  0.019571  0.275352
			 0.988198 -0.001504 -0.153177
			 0.459000 -0.121475 -0.880093
			 0.059381 -0.229897 -0.971402
			-0.586248 -0.093527 -0.804715
			-0.994831 -0.008079 -0.101226
			-0.999926 -0.001359 -0.012102
			-0.947002  0.005145  0.321187
			-0.221880  0.015042  0.974958
			-0.939857  0.005890  0.341517
			 0.635461  0.005886  0.772110
			 0.986137 -0.040125  0.161006
			 0.999090 -0.027276  0.032803
			 0.878127 -0.042553 -0.476531
			-0.044540 -0.188968 -0.980973
			-0.857359 -0.163448 -0.488079
			-0.996661 -0.038206  0.072159
			-0.992107 -0.020587  0.123695
			-0.874833  0.018099  0.484086
			-0.331484  0.041670  0.942540
			 0.422186 -0.068717  0.903901
			 0.893265 -0.446980 -0.047815
			 0.934286 -0.316762 -0.163619
			 0.908787  0.137555 -0.393934
			 0.120203  0.311293 -0.942681
			-0.827570 -0.062120 -0.557915
			-0.989511 -0.139064 -0.039106
			-0.912036 -0.122135  0.391502
			-0.518298 -0.025839  0.854810
			-0.290190  0.029088  0.956527
			 0.040617  0.077433  0.996170
			 0.958637 -0.003749  0.284608
			 0.886074 -0.208859 -0.413825
			 0.643435  0.303959 -0.702566
			 0.301970  0.622448 -0.722061
			-0.435767  0.440396 -0.784958
			-0.902566  0.232166 -0.362594
			-0.756193  0.126022  0.642099
			 0.065018  0.034749  0.997279
			-0.077646  0.013155  0.996894
			-0.197135  0.385944  0.901213
			 0.428832  0.683736  0.590430
			 0.686613  0.726228 -0.033985
			 0.647534  0.742204 -0.172724
			 0.602214  0.770883 -0.207550
			 0.418362  0.821878 -0.386640
			 0.026117  0.925136 -0.378736
			-0.012660  0.870732  0.491595
			 0.435106  0.354724  0.827559
			 0.288435  0.257293  0.922283
			 0.518056  0.739163  0.430415
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.000350 -0.424433 -0.024976
			-0.004000 -0.431823 -0.024600
			-0.004000 -0.425441 -0.030000
			 0.002676 -0.417044 -0.027831
			 0.001713 -0.415363 -0.022137
			 0.008839 -0.408645 -0.025828
			-0.006222 -0.430480 -0.026942
			-0.004000 -0.422082 -0.024060
			 0.002676 -0.417044 -0.032169
			 0.001476 -0.419899 -0.037536
			-0.004000 -0.422753 -0.041610
			-0.008047 -0.425945 -0.035570
			-0.006055 -0.429136 -0.030668
			-0.006055 -0.429136 -0.029332
			 0.008839 -0.408645 -0.034171
			 0.001237 -0.404111 -0.022792
			 0.003447 -0.397560 -0.027580
			 0.003447 -0.397560 -0.032420
			-0.008285 -0.420571 -0.024102
			-0.004000 -0.410660 -0.020010
			 0.004094 -0.409821 -0.041140
			-0.004000 -0.410996 -0.044040
			-0.010665 -0.415029 -0.039175
			-0.012217 -0.419059 -0.032670
			-0.012217 -0.419059 -0.027330
			-0.000270 -0.394706 -0.035134
			 0.000126 -0.387819 -0.024321
			 0.002162 -0.383116 -0.027997
			 0.002162 -0.383116 -0.032002
			-0.000509 -0.370015 -0.034805
			-0.011221 -0.411500 -0.020061
			-0.004000 -0.392520 -0.022440
			-0.004000 -0.391850 -0.034860
			-0.009713 -0.402096 -0.037863
			-0.017866 -0.412340 -0.034505
			-0.017866 -0.412340 -0.025495
			-0.004000 -0.356915 -0.035400
			-0.000826 -0.365481 -0.025631
			 0.000879 -0.348518 -0.028414
			 0.000879 -0.348518 -0.031586
			-0.000985 -0.301996 -0.034150
			-0.004000 -0.255471 -0.035130
			-0.010745 -0.398736 -0.020716
			-0.004000 -0.382444 -0.024330
			-0.010110 -0.380934 -0.038410
			-0.018637 -0.404950 -0.034756
			-0.018637 -0.404950 -0.025244
			-0.008285 -0.328532 -0.035898
			 0.000840 -0.301155 -0.023337
			 0.003190 -0.279321 -0.027664
			 0.003190 -0.279321 -0.032336
			-0.000905 -0.218354 -0.034260
			-0.004000 -0.157387 -0.032970
			-0.006460 -0.269580 -0.033386
			-0.008443 -0.392018 -0.023884
			-0.004000 -0.322989 -0.021090
			-0.012987 -0.401592 -0.032921
			-0.012987 -0.401592 -0.027079
			-0.009135 -0.381773 -0.031669
			 0.001555 -0.226584 -0.022354
			 0.004474 -0.197696 -0.027247
			 0.004474 -0.197696 -0.032753
			 0.000364 -0.160746 -0.036007
			-0.004000 -0.123797 -0.035940
			-0.006777 -0.219362 -0.033822
			-0.007338 -0.314928 -0.031084
			-0.008206 -0.352382 -0.024211
			-0.004000 -0.255471 -0.020010
			-0.009135 -0.381773 -0.028331
			-0.007338 -0.314928 -0.028916
			 0.001237 -0.176366 -0.022792
			 0.003447 -0.147310 -0.027580
			 0.003447 -0.147310 -0.032420
			 0.001555 -0.121949 -0.037645
			-0.004000 -0.096588 -0.041070
			-0.008443 -0.156043 -0.036116
			-0.007852 -0.215499 -0.031252
			-0.007852 -0.215499 -0.028748
			-0.007967 -0.285200 -0.024539
			-0.004000 -0.205421 -0.020010
			-0.008126 -0.210460 -0.024321
			 0.000443 -0.125979 -0.023884
			 0.002420 -0.097595 -0.027914
			 0.002420 -0.097595 -0.032086
			 0.003538 -0.091717 -0.040376
			-0.004000 -0.085840 -0.048900
			-0.010745 -0.117079 -0.039284
			-0.007852 -0.148318 -0.031252
			-0.007852 -0.148318 -0.028748
			-0.007650 -0.151340 -0.024976
			-0.004000 -0.154364 -0.021630
			 0.000126 -0.101459 -0.024321
			 0.003960 -0.082480 -0.027413
			 0.003960 -0.082480 -0.032587
			 0.003856 -0.077777 -0.040813
			-0.004000 -0.073075 -0.048360
			-0.011221 -0.094405 -0.039939
			-0.009906 -0.115734 -0.031919
			-0.009906 -0.115734 -0.028081
			-0.007491 -0.118086 -0.025195
			-0.004000 -0.120437 -0.024330
			 0.001158 -0.081305 -0.022901
			 0.009352 -0.078786 -0.025661
			 0.009352 -0.078786 -0.034339
			 0.003141 -0.072571 -0.039830
			-0.004000 -0.066356 -0.040260
			-0.011935 -0.069043 -0.040922
			-0.019921 -0.071731 -0.035173
			-0.019921 -0.071731 -0.024827
			-0.009952 -0.077777 -0.021809
			-0.004000 -0.083823 -0.026490
			-0.000429 -0.068708 -0.025085
			 0.003704 -0.067028 -0.027497
			 0.003704 -0.067028 -0.032503
			-0.000191 -0.064341 -0.035242
			-0.004000 -0.061653 -0.034860
			-0.007095 -0.059135 -0.034260
			-0.009393 -0.056615 -0.031752
			-0.009393 -0.056615 -0.028248
			-0.006857 -0.063501 -0.026068
			-0.004000 -0.070387 -0.025950
			-0.004000 -0.065685 -0.030000
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 0 2 
					0 4 1 
					5 4 0 
					3 5 0 
					1 6 2 
					4 7 1 
					1 7 6 
					8 3 2 
					9 8 2 
					10 9 2 
					11 10 2 
					12 11 2 
					13 12 2 
					6 13 2 
					14 5 3 
					8 14 3 
					4 15 7 
					16 15 4 
					5 16 4 
					17 16 5 
					14 17 5 
					6 18 13 
					7 18 6 
					15 19 7 
					7 19 18 
					20 14 8 
					9 20 8 
					21 20 9 
					10 21 9 
					22 21 10 
					11 22 10 
					23 22 11 
					12 23 11 
					24 23 12 
					13 24 12 
					18 24 13 
					25 17 14 
					20 25 14 
					15 26 19 
					27 26 15 
					16 27 15 
					28 27 16 
					17 28 16 
					29 28 17 
					25 29 17 
					18 30 24 
					19 30 18 
					26 31 19 
					19 31 30 
					32 25 20 
					21 32 20 
					33 32 21 
					22 33 21 
					34 33 22 
					23 34 22 
					35 34 23 
					24 35 23 
					30 35 24 
					36 29 25 
					32 36 25 
					26 37 31 
					38 37 26 
					27 38 26 
					39 38 27 
					28 39 27 
					40 39 28 
					29 40 28 
					41 40 29 
					36 41 29 
					30 42 35 
					31 42 30 
					37 43 31 
					31 43 42 
					44 36 32 
					33 44 32 
					45 44 33 
					34 45 33 
					46 45 34 
					35 46 34 
					42 46 35 
					47 41 36 
					44 47 36 
					37 48 43 
					49 48 37 
					38 49 37 
					50 49 38 
					39 50 38 
					51 50 39 
					40 51 39 
					52 51 40 
					41 52 40 
					53 52 41 
					47 53 41 
					42 54 46 
					43 54 42 
					48 55 43 
					43 55 54 
					56 47 44 
					45 56 44 
					57 56 45 
					46 57 45 
					54 57 46 
					58 53 47 
					56 58 47 
					48 59 55 
					60 59 48 
					49 60 48 
					61 60 49 
					50 61 49 
					62 61 50 
					51 62 50 
					63 62 51 
					52 63 51 
					64 63 52 
					53 64 52 
					65 64 53 
					58 65 53 
					54 66 57 
					55 66 54 
					59 67 55 
					55 67 66 
					68 58 56 
					57 68 56 
					66 68 57 
					69 65 58 
					68 69 58 
					59 70 67 
					71 70 59 
					60 71 59 
					72 71 60 
					61 72 60 
					73 72 61 
					62 73 61 
					74 73 62 
					63 74 62 
					75 74 63 
					64 75 63 
					76 75 64 
					65 76 64 
					77 76 65 
					69 77 65 
					66 78 68 
					67 78 66 
					70 79 67 
					67 79 78 
					78 69 68 
					80 77 69 
					78 80 69 
					70 81 79 
					82 81 70 
					71 82 70 
					83 82 71 
					72 83 71 
					84 83 72 
					73 84 72 
					85 84 73 
					74 85 73 
					86 85 74 
					75 86 74 
					87 86 75 
					76 87 75 
					88 87 76 
					77 88 76 
					89 88 77 
					80 89 77 
					79 80 78 
					81 90 79 
					79 90 80 
					90 89 80 
					81 91 90 
					92 91 81 
					82 92 81 
					93 92 82 
					83 93 82 
					94 93 83 
					84 94 83 
					95 94 84 
					85 95 84 
					96 95 85 
					86 96 85 
					97 96 86 
					87 97 86 
					98 97 87 
					88 98 87 
					99 98 88 
					89 99 88 
					90 100 89 
					100 99 89 
					91 100 90 
					91 101 100 
					102 101 91 
					92 102 91 
					103 102 92 
					93 103 92 
					104 103 93 
					94 104 93 
					105 104 94 
					95 105 94 
					106 105 95 
					96 106 95 
					107 106 96 
					97 107 96 
					108 107 97 
					98 108 97 
					109 108 98 
					99 109 98 
					100 110 99 
					110 109 99 
					101 110 100 
					101 111 110 
					112 111 101 
					102 112 101 
					113 112 102 
					103 113 102 
					114 113 103 
					104 114 103 
					115 114 104 
					105 115 104 
					116 115 105 
					106 116 105 
					117 116 106 
					107 117 106 
					118 117 107 
					108 118 107 
					119 118 108 
					109 119 108 
					110 120 109 
					120 119 109 
					111 120 110 
					111 121 120 
					112 121 111 
					113 121 112 
					114 121 113 
					115 121 114 
					116 121 115 
					117 121 116 
					118 121 117 
					119 121 118 
					120 121 119 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
