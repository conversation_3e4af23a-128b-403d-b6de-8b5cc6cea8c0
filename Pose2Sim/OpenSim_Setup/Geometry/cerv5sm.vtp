<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="142" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="216">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.004211  0.999991  0.000000
			 0.990772 -0.135539  0.000000
			 0.046770  0.958850  0.280035
			-0.000568  0.999479 -0.032280
			 0.042313  0.960888 -0.273687
			 0.597296 -0.801702 -0.022599
			 0.920678 -0.180844  0.345902
			 0.365782 -0.930701  0.000000
			 0.920678 -0.180845 -0.345901
			 0.546294 -0.834443 -0.072576
			 0.496617  0.866076  0.057312
			-0.624761  0.687957  0.369309
			-0.725617  0.486550 -0.486569
			-0.200673  0.602089 -0.772800
			 0.003130  0.467017 -0.884243
			 0.057884  0.522262 -0.850818
			 0.096847  0.995176 -0.015668
			 0.079516  0.536048  0.840434
			 0.028580  0.500025  0.865539
			-0.132983  0.414545  0.900260
			-0.758935  0.299021  0.578449
			 0.454112  0.815528 -0.358742
			-0.707770  0.634097 -0.311421
			 0.715518 -0.610690  0.339252
			-0.445415 -0.619922 -0.645989
			-0.798804  0.154380 -0.581445
			-0.276730 -0.960948  0.000001
			-0.383950 -0.772239  0.506191
			 0.413525 -0.638768 -0.648824
			-0.880551  0.122836  0.457757
			 0.018142  0.937459  0.347622
			 0.391231  0.892231  0.225526
			-0.492385  0.856530 -0.154637
			-0.225196 -0.593088 -0.773003
			-0.004596 -0.595074 -0.803658
			-0.367824 -0.245027 -0.897032
			-0.378272  0.463581 -0.801251
			-0.963109  0.264627  0.048932
			-0.357356  0.402488  0.842793
			-0.367825 -0.245027  0.897032
			 0.023214 -0.615341  0.787919
			-0.067701 -0.764504  0.641054
			-0.531119  0.825657  0.190270
			 0.229733  0.822681 -0.520018
			 0.004748  0.564037 -0.825736
			-0.399793  0.528014  0.749245
			-0.688971 -0.587606 -0.424308
			 0.469418 -0.868190 -0.160914
			-0.703823 -0.146000 -0.695210
			 0.024328 -0.999704 -0.000000
			-0.736026  0.547094 -0.398690
			-0.805285  0.568301 -0.168967
			-0.270772  0.320232 -0.907818
			-0.689283 -0.587316  0.424203
			 0.479252 -0.872635  0.093946
			-0.759603 -0.172414  0.627117
			-0.735880  0.547103  0.398946
			-0.805505  0.567946  0.169112
			-0.527989  0.782478  0.330085
			-0.352993  0.903641  0.242547
			-0.306539  0.950513  0.050589
			 0.071813  0.939888 -0.333848
			 0.394999  0.823709  0.406792
			-0.494213  0.822094 -0.282692
			 0.578652  0.815456  0.013900
			 0.071155 -0.997465 -0.000001
			-0.676891 -0.736083 -0.000000
			-0.996327 -0.085362 -0.006810
			-0.996327 -0.085362  0.006810
			-0.509590  0.807858  0.296115
			 0.538863  0.841416  0.040568
			 0.241122  0.908990 -0.339998
			-0.404360  0.910058  0.091035
			-0.099232  0.922318  0.373474
			-0.613416  0.760461 -0.213119
			-0.632390  0.687880 -0.356236
			-0.902833 -0.407309  0.137812
			 0.463560 -0.809389 -0.360557
			 0.273555 -0.700922 -0.658693
			 0.109135 -0.450099 -0.886284
			 0.163673 -0.496840 -0.852268
			 0.208958  0.774806 -0.596667
			 0.330062  0.613857 -0.717104
			 0.728669  0.612349  0.306709
			 0.800111  0.375117 -0.468092
			 0.129289 -0.990353 -0.049847
			-0.333780 -0.590900  0.734458
			 0.352966  0.838106 -0.415926
			 0.538216  0.841601 -0.045071
			-0.565465 -0.711727  0.416766
			 0.302798 -0.817438 -0.490009
			-0.299281  0.453803 -0.839341
			-0.900417 -0.428467 -0.075267
			 0.253790 -0.718221  0.647881
			 0.452748 -0.823963  0.340741
			 0.111663 -0.447581  0.887244
			 0.165626 -0.495166  0.852865
			 0.188394  0.758846  0.623427
			 0.309255  0.777795  0.547172
			 0.780350  0.530455  0.331167
			 0.651368  0.672323 -0.351714
			 0.128626 -0.990570  0.047187
			-0.384425 -0.568914 -0.727017
			 0.323166  0.941522 -0.095399
			 0.225624  0.853116  0.470412
			 0.245240 -0.779484  0.576422
			-0.727893 -0.624994 -0.282053
			-0.297625  0.453567  0.840058
			 0.410444  0.806088  0.426330
			 0.338440  0.907755  0.247867
			 0.440903  0.896871  0.035034
			-0.229570  0.875152  0.425919
			-0.993269 -0.105787  0.047176
			-0.177548  0.677688 -0.713594
			-0.181439  0.597878  0.780783
			 0.115301  0.981276  0.154281
			 0.164859  0.841129 -0.515095
			 0.345305  0.903215 -0.254887
			-0.257094  0.891591 -0.372785
			 0.778650 -0.611819 -0.139217
			 0.540181 -0.839202 -0.062798
			 0.912338  0.377152 -0.159360
			 0.738843  0.469181 -0.483716
			 0.780166  0.451195 -0.433316
			 0.689019  0.309576 -0.655298
			 0.844248  0.441507 -0.303838
			 0.732479  0.680320 -0.025300
			 0.480387  0.557938 -0.676708
			 0.628276  0.758636 -0.172456
			 0.791235 -0.608651  0.059089
			 0.915203  0.357289  0.186406
			 0.742525  0.450847  0.495372
			 0.811353  0.434863  0.390642
			 0.609372  0.349362  0.711766
			 0.703259  0.680454  0.205936
			 0.662307  0.735176 -0.144449
			 0.387991  0.598943  0.700521
			 0.371015  0.928511 -0.014668
			 0.699186  0.626244 -0.344902
			 0.099467  0.994059  0.044202
			 0.868028  0.495937  0.023957
			 0.727604  0.618698  0.296319
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.015695  0.058569  0.000150
			-0.004604  0.059201  0.000150
			-0.003959  0.059264 -0.004180
			-0.016656  0.058617  0.000150
			-0.003959  0.059264  0.004480
			-0.010469  0.044483 -0.002122
			-0.004879  0.058563 -0.001485
			-0.010470  0.044482  0.000150
			-0.004879  0.058563  0.001786
			-0.010469  0.044483  0.002422
			 0.001549  0.060672 -0.012190
			-0.004918  0.059362 -0.004960
			-0.007843  0.054154 -0.011813
			-0.020043  0.054433 -0.005270
			-0.027442  0.056134 -0.003752
			-0.032958  0.057294 -0.003527
			-0.032668  0.059315  0.000150
			-0.032958  0.057294  0.003828
			-0.027442  0.056134  0.004053
			-0.020043  0.054433  0.005571
			-0.007843  0.054154  0.012113
			 0.001549  0.060672  0.012491
			-0.004918  0.059362  0.005260
			-0.004048  0.048563 -0.009838
			-0.020835  0.049402 -0.006679
			-0.010746  0.047058 -0.012267
			-0.020840  0.049397  0.000150
			-0.020835  0.049402  0.006979
			-0.004048  0.048563  0.010139
			-0.010746  0.047058  0.012567
			 0.005128  0.055436 -0.012269
			 0.004110  0.056281 -0.013732
			-0.000892  0.057663 -0.022495
			-0.026825  0.050112 -0.005751
			-0.033487  0.048990 -0.005593
			-0.037594  0.049090 -0.005446
			-0.040287  0.056876 -0.004316
			-0.038888  0.060971  0.000150
			-0.040287  0.056876  0.004616
			-0.037594  0.049090  0.005746
			-0.033487  0.048990  0.005893
			-0.026825  0.050112  0.006052
			-0.000892  0.057663  0.022791
			 0.004110  0.056281  0.014032
			 0.005128  0.055436  0.012570
			 0.007837  0.049113 -0.010931
			 0.003754  0.044407 -0.009825
			 0.007330  0.055105 -0.021058
			-0.008207  0.047566 -0.022689
			-0.026829  0.050108  0.000150
			-0.004563  0.052988 -0.024179
			-0.009313  0.047499 -0.017400
			 0.007837  0.049113  0.011231
			 0.003754  0.044407  0.010126
			 0.007330  0.055105  0.021354
			-0.008207  0.047566  0.022985
			-0.004563  0.052988  0.024475
			-0.009313  0.047499  0.017700
			 0.010945  0.058779 -0.009892
			 0.011187  0.060171 -0.010987
			 0.008347  0.055830 -0.013145
			 0.009779  0.055959 -0.016647
			 0.006426  0.059423 -0.018550
			 0.001779  0.058246 -0.024668
			 0.009792  0.062279 -0.023900
			-0.033491  0.048986  0.000150
			-0.037598  0.049086  0.000150
			-0.041680  0.051906 -0.004210
			-0.041680  0.051906  0.004511
			 0.001779  0.058246  0.024959
			 0.009792  0.062279  0.024167
			 0.006426  0.059423  0.018845
			 0.008347  0.055830  0.013446
			 0.009779  0.055959  0.016948
			 0.011187  0.060171  0.011288
			 0.010945  0.058779  0.010193
			 0.004856  0.043043 -0.004101
			 0.023101  0.037841 -0.008543
			 0.020344  0.039838 -0.015003
			 0.016807  0.040972 -0.016757
			 0.014435  0.046875 -0.018399
			 0.009538  0.053152 -0.018276
			 0.007576  0.060596 -0.025971
			 0.007384  0.054771 -0.024241
			 0.008965  0.058096 -0.028250
			 0.005921  0.050197 -0.030349
			 0.008847  0.051762 -0.029420
			 0.012578  0.052399 -0.019549
			 0.012833  0.051338 -0.023095
			 0.011746  0.050370 -0.026796
			 0.014072  0.049318 -0.022307
			 0.003641  0.057354 -0.028665
			 0.004856  0.043043  0.004401
			 0.020344  0.039838  0.015303
			 0.023101  0.037841  0.008844
			 0.016807  0.040972  0.017058
			 0.014435  0.046875  0.018699
			 0.009538  0.053152  0.018576
			 0.007576  0.060596  0.026239
			 0.008965  0.058096  0.028518
			 0.007384  0.054771  0.024532
			 0.005921  0.050197  0.030617
			 0.008847  0.051762  0.029711
			 0.012833  0.051338  0.023391
			 0.012578  0.052398  0.019512
			 0.014072  0.049318  0.022603
			 0.011746  0.050370  0.027086
			 0.003641  0.057354  0.028956
			 0.013884  0.062290 -0.011666
			 0.013234  0.057374 -0.005159
			 0.015089  0.061058 -0.014329
			 0.012213  0.057302 -0.002390
			 0.004705  0.042363  0.000150
			 0.014021  0.060440 -0.015713
			 0.014021  0.060440  0.016013
			 0.015089  0.061058  0.014630
			 0.013884  0.062290  0.011966
			 0.013234  0.057374  0.005460
			 0.012213  0.057302  0.002691
			 0.024376  0.037597 -0.002163
			 0.024384  0.037747  0.000150
			 0.025873  0.051545 -0.007528
			 0.022871  0.053406 -0.011725
			 0.020837  0.055287 -0.013341
			 0.016640  0.053370 -0.017268
			 0.014296  0.050630 -0.022891
			 0.010198  0.053516 -0.024971
			 0.008392  0.050879 -0.029750
			 0.011745  0.051675 -0.025903
			 0.024376  0.037597  0.002464
			 0.025873  0.051545  0.007829
			 0.022871  0.053406  0.012025
			 0.020837  0.055287  0.013641
			 0.016640  0.053370  0.017569
			 0.014296  0.050630  0.023186
			 0.010198  0.053516  0.025261
			 0.008392  0.050879  0.030018
			 0.011745  0.051675  0.026194
			 0.017001  0.059533 -0.013436
			 0.014684  0.055710  0.000150
			 0.026737  0.051243  0.000150
			 0.017001  0.059533  0.013736
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					4 1 0 
					0 3 4 
					5 6 1 
					7 5 1 
					1 6 2 
					1 8 9 
					1 9 7 
					4 8 1 
					10 2 6 
					10 11 2 
					2 12 13 3 
					12 2 11 
					3 14 15 16 
					13 14 3 
					16 17 18 3 
					3 18 19 
					3 19 20 4 
					8 4 21 
					4 22 21 
					22 4 20 
					23 6 5 
					23 5 24 25 
					24 5 7 26 
					10 6 23 
					26 7 9 27 
					28 8 21 
					9 8 28 
					29 27 9 28 
					30 31 10 23 
					10 32 12 11 
					10 31 32 
					13 12 24 33 
					12 25 24 
					12 32 25 
					14 13 33 34 
					14 34 35 
					15 14 35 
					16 15 36 37 
					35 36 15 
					37 38 17 16 
					39 18 17 
					17 38 39 
					39 40 18 
					40 41 19 18 
					41 27 20 19 
					22 20 42 21 
					27 29 20 
					29 42 20 
					28 21 43 44 
					42 43 21 
					30 23 45 
					45 23 46 
					47 46 23 
					47 23 25 48 
					49 33 24 26 
					25 32 50 
					25 50 51 
					25 51 48 
					26 27 41 49 
					52 28 44 
					53 28 52 
					28 53 54 
					55 29 28 54 
					56 42 29 
					57 56 29 
					55 57 29 
					58 59 30 45 
					59 60 30 
					60 31 30 
					61 62 31 60 
					62 32 31 
					32 63 50 
					32 62 64 63 
					65 34 33 49 
					66 35 34 65 
					66 37 35 
					67 35 37 
					67 36 35 
					36 67 37 
					39 37 66 
					37 68 38 
					37 39 68 
					39 38 68 
					65 40 39 66 
					49 41 40 65 
					56 69 42 
					69 70 71 42 
					43 42 71 
					72 43 71 73 
					44 43 72 
					52 44 74 75 
					44 72 74 
					58 45 46 
					58 46 76 
					77 76 46 78 
					78 46 79 
					79 46 80 
					46 47 80 
					81 47 62 61 
					47 64 62 
					47 82 64 
					47 83 84 82 
					47 48 85 
					47 85 86 
					83 47 87 88 
					47 81 87 
					89 90 47 
					90 80 47 
					47 86 89 
					51 50 48 
					50 91 48 
					91 85 48 
					63 91 50 
					53 52 75 
					92 53 75 
					93 53 92 94 
					95 53 93 
					96 53 95 
					96 54 53 
					73 71 54 97 
					71 70 54 
					70 98 54 
					98 99 100 54 
					101 55 54 
					102 101 54 
					103 104 54 100 
					104 97 54 
					54 105 106 
					54 96 105 
					106 102 54 
					55 56 57 
					55 107 56 
					55 101 107 
					56 107 69 
					108 58 109 
					110 59 58 108 
					109 58 111 
					111 58 76 112 
					110 113 60 59 
					113 61 60 
					81 61 113 
					82 63 64 
					91 63 82 
					70 69 98 
					98 69 107 
					74 72 114 115 
					72 73 114 
					114 73 97 
					116 75 74 115 
					117 75 116 
					118 75 117 
					112 92 75 118 
					76 77 119 120 112 
					77 121 119 
					78 122 121 77 
					79 122 78 
					80 123 122 79 
					80 124 123 
					80 90 125 124 
					87 81 113 
					82 84 91 
					126 84 83 
					126 83 88 
					84 127 91 
					126 127 84 
					91 127 85 
					86 85 127 
					89 86 127 128 
					125 88 87 124 
					124 87 113 
					128 126 88 125 
					90 89 128 125 
					112 120 129 94 92 
					94 130 131 93 
					93 131 95 
					129 130 94 
					95 131 132 96 
					132 133 96 
					133 134 105 96 
					114 97 104 
					107 99 98 
					100 99 135 
					107 136 99 
					99 136 135 
					103 100 135 
					101 136 107 
					136 101 102 
					137 136 102 106 
					133 104 103 134 
					134 103 135 137 
					114 104 133 
					134 137 106 105 
					123 138 108 109 
					138 110 108 
					123 109 122 
					122 109 121 
					109 139 140 121 
					109 111 139 
					124 113 110 138 
					139 111 112 
					112 118 139 
					141 115 114 133 
					116 115 141 
					117 116 141 132 
					131 117 132 
					130 117 131 
					130 140 139 117 
					139 118 117 
					121 140 120 119 
					129 120 140 130 
					123 124 138 
					127 126 128 
					141 133 132 
					137 135 136 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 40 43 47 50 54 57 61 64 67 70 73 77 81 84 88 91 94 98 102 106 109 113 116 119 123 126 129 133 136 140 143 146 149 153 157 161 164 167 171 174 177 180 183 187 191 194 197 200 204 207 210 213 217 220 223 226 230 233 236 240 243 246 250 254 258 261 264 267 270 273 276 279 282 286 290 293 297 300 304 307 311 314 317 320 324 327 330 333 337 340 343 347 350 353 357 360 363 366 369 372 375 378 381 384 387 391 394 397 400 404 407 410 414 417 420 424 427 430 433 436 439 442 445 448 451 455 458 462 466 469 472 475 478 481 484 488 491 494 498 501 504 508 513 516 520 523 527 530 534 537 540 543 546 549 552 555 558 562 566 569 573 577 582 586 589 592 596 599 603 606 609 612 615 618 621 624 627 631 635 639 642 646 650 653 656 659 663 666 670 673 676 680 683 687 690 693 697 700 704 708 711 714 717 720 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
