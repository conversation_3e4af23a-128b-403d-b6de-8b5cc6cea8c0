<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.927432 -0.373991  0.000364
			 0.808456 -0.552860  0.201854
			 0.438307 -0.898825  0.000017
			 0.890661  0.327813  0.315059
			 0.976085  0.217390  0.000238
			 0.808549 -0.552818 -0.201595
			 0.890733  0.327916 -0.314747
			 0.742699 -0.438897  0.505734
			 0.471358 -0.466135  0.748692
			-0.175688 -0.984271 -0.018565
			-0.207802 -0.978171  0.000062
			-0.150093 -0.982397  0.111213
			 0.114732 -0.686511  0.718010
			 0.742698 -0.438904 -0.505730
			 0.471356 -0.466136 -0.748693
			-0.175684 -0.984272  0.018544
			-0.150087 -0.982398 -0.111211
			 0.114737 -0.686511 -0.718010
			 0.803930  0.594724  0.000195
			 0.721500  0.579563  0.378873
			 0.501903  0.387129  0.773449
			 0.721597  0.579656 -0.378545
			 0.501900  0.387130 -0.773451
			 0.129989  0.210871  0.968832
			-0.696239 -0.707808  0.119412
			-0.767924 -0.640541  0.000136
			-0.696254 -0.707818 -0.119265
			-0.253214 -0.122630  0.959607
			-0.647929 -0.676147  0.350733
			-0.670134 -0.660435 -0.338741
			 0.129988  0.210870 -0.968833
			-0.647929 -0.676147 -0.350733
			-0.253209 -0.122632 -0.959607
			-0.670134 -0.660435  0.338741
			 0.957724  0.287689  0.000384
			 0.904667  0.173041  0.389402
			 0.457146  0.389666  0.799486
			 0.904780  0.173080 -0.389122
			 0.457145  0.389662 -0.799488
			 0.145853  0.170145  0.974565
			-0.890733 -0.411772 -0.192453
			-0.954712 -0.297532 -0.000081
			-0.890731 -0.411769  0.192472
			-0.157899  0.003977  0.987447
			 0.081890 -0.486865  0.869630
			-0.460833 -0.712161 -0.529585
			 0.145856  0.170140 -0.974566
			 0.081892 -0.486864 -0.869631
			-0.157903  0.003981 -0.987447
			-0.460833 -0.712161  0.529585
			 0.977402  0.211390  0.000216
			 0.932735  0.149876  0.327937
			 0.605988 -0.058097  0.793349
			 0.932786  0.149904 -0.327779
			 0.605986 -0.058092 -0.793351
			 0.298955 -0.194572  0.934220
			-0.914482 -0.300309 -0.271177
			-0.381997 -0.057890 -0.922349
			-0.946284 -0.323337 -0.000160
			-0.914502 -0.300340  0.271075
			-0.381997 -0.057890  0.922349
			 0.108783 -0.251937  0.961610
			 0.136833  0.035674  0.989952
			 0.052694 -0.975084  0.215486
			 0.039612 -0.629558  0.775943
			 0.055462 -0.036301  0.997801
			 0.612741  0.283698 -0.737607
			 0.298953 -0.194567 -0.934222
			 0.136840  0.035672 -0.989951
			 0.108785 -0.251939 -0.961610
			 0.039612 -0.629558 -0.775943
			 0.052694 -0.975084 -0.215486
			 0.055462 -0.036301 -0.997801
			 0.612955  0.283810  0.737386
			 0.666965  0.745089  0.000192
			 0.497718  0.792468  0.352521
			 0.632348 -0.017689  0.774483
			 0.497754  0.792509 -0.352377
			 0.632349 -0.017687 -0.774482
			 0.273819 -0.227000  0.934609
			-0.676429  0.622572 -0.393506
			-0.189895 -0.085153 -0.978105
			-0.889439  0.457055 -0.000237
			-0.676497  0.622597  0.393351
			-0.189895 -0.085153  0.978105
			-0.018630 -0.203534  0.978891
			-0.241312  0.143016  0.959851
			-0.059625  0.661904  0.747213
			-0.560682 -0.392269  0.729220
			-0.451687 -0.557977  0.696161
			 0.334498 -0.110059  0.935948
			 0.794184 -0.238245  0.559027
			 0.097261  0.288713  0.952463
			-0.514760 -0.563377  0.646241
			 0.936653  0.350258 -0.000359
			 0.938419 -0.112396 -0.326705
			-0.712694  0.701475  0.000015
			-0.492443  0.393269 -0.776427
			 0.273821 -0.227001 -0.934608
			-0.241312  0.143016 -0.959851
			-0.018625 -0.203541 -0.978889
			-0.059625  0.661904 -0.747213
			-0.451687 -0.557977 -0.696161
			-0.560685 -0.392268 -0.729218
			 0.334498 -0.110059 -0.935948
			 0.794183 -0.238246 -0.559028
			-0.514755 -0.563372 -0.646251
			 0.097255  0.288711 -0.952464
			 0.938515 -0.112416  0.326425
			-0.492370  0.393324  0.776446
			-0.321303  0.946977  0.000001
			 0.203466  0.816840  0.539790
			 0.052701  0.548337  0.834595
			 0.203462  0.816843 -0.539789
			 0.052704  0.548341 -0.834592
			-0.276624  0.387224  0.879509
			-0.251035  0.939881 -0.231527
			-0.022350  0.925727  0.377532
			-0.725846  0.367963 -0.581163
			-0.457315  0.787513  0.413141
			-0.022350  0.925727 -0.377532
			-0.251038  0.939881  0.231527
			-0.725845  0.367964  0.581164
			-0.457303  0.787526 -0.413130
			 0.230584  0.620327  0.749683
			 0.210895  0.722691  0.658211
			-0.269191  0.509256  0.817432
			 0.066716 -0.422136  0.904074
			 0.045819  0.461228  0.886098
			-0.847005  0.006755  0.531542
			-0.135633  0.033230  0.990202
			-0.856044 -0.285493 -0.430909
			-0.891177 -0.025192 -0.452957
			 0.940665  0.118619  0.317928
			-0.342344  0.189013  0.920366
			 0.088817  0.004752  0.996037
			-0.703143 -0.578082  0.414018
			-0.989737 -0.135200  0.046277
			-0.043923 -0.999035 -0.000247
			 0.223975 -0.840001 -0.494199
			-0.968874  0.247553  0.000190
			-0.798781  0.496186  0.340219
			-0.905332  0.377191  0.195192
			-0.933958  0.261008 -0.244124
			-0.798712  0.496343 -0.340152
			-0.905298  0.377344 -0.195056
			-0.933941  0.260995  0.244203
			-0.755721  0.142274 -0.639253
			-0.276621  0.387235 -0.879505
			 0.230584  0.620325 -0.749685
			 0.210893  0.722691 -0.658211
			-0.135633  0.033230 -0.990202
			-0.847005  0.006755 -0.531542
			 0.066717 -0.422134 -0.904075
			-0.856044 -0.285506  0.430901
			-0.891177 -0.025200  0.452956
			-0.269198  0.509258 -0.817428
			 0.045811  0.461228 -0.886098
			 0.940663  0.118606 -0.317941
			-0.342344  0.189013 -0.920366
			-0.703135 -0.578080 -0.414035
			 0.088821  0.004752 -0.996036
			-0.989736 -0.135203 -0.046290
			 0.223893 -0.840120  0.494035
			-0.755722  0.142270  0.639252
			-0.898437  0.411635 -0.152864
			-0.367528  0.168991 -0.914530
			-0.898436  0.411636  0.152867
			-0.367534  0.168996  0.914527
			-0.314942 -0.392558  0.864124
			-0.062736 -0.330385  0.941759
			-0.905380  0.053453  0.421225
			-0.689615 -0.553843  0.466572
			-0.585135 -0.705991  0.398992
			-0.939771  0.172875  0.294863
			 0.357339 -0.866683  0.348094
			 0.040666 -0.917206 -0.396332
			 0.611764 -0.791040 -0.000223
			 0.040504 -0.917310  0.396109
			-0.828685  0.134493  0.543316
			-0.907609  0.419816 -0.000818
			-0.803164 -0.015493  0.595556
			-0.828746  0.134466 -0.543230
			-0.802944 -0.015025 -0.595865
			-0.939770  0.172878 -0.294865
			-0.689615 -0.553843 -0.466572
			-0.905377  0.053450 -0.421230
			-0.585135 -0.705991 -0.398992
			-0.314941 -0.392553 -0.864126
			-0.062736 -0.330385 -0.941759
			 0.357337 -0.866685 -0.348092
			-0.647471  0.053999  0.760175
			 0.052925 -0.696785  0.715324
			-0.126377 -0.985591 -0.112422
			 0.733863 -0.679298 -0.000027
			-0.126377 -0.985591  0.112422
			 0.052908 -0.696765 -0.715345
			-0.829756  0.558125 -0.001416
			-0.646394  0.053666 -0.761114
			-0.173674  0.215744  0.960881
			 0.056880 -0.070360  0.995899
			 0.458466 -0.472066  0.752969
			 0.337441 -0.941347 -0.000522
			 0.458440 -0.472020 -0.753014
			 0.056868 -0.070359 -0.995899
			-0.803840 -0.189251  0.563937
			-0.890553 -0.454878 -0.001023
			-0.172846  0.214955 -0.961207
			-0.802719 -0.189371 -0.565492
			-0.091774 -0.706188  0.702052
			-0.364043 -0.931381 -0.000992
			-0.091120 -0.705654 -0.702673
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.011550  0.177404  0.000025
			-0.012451  0.177358  0.008244
			-0.026103  0.170451  0.000003
			-0.012808  0.182239  0.009890
			-0.011333  0.179459  0.000027
			-0.012451  0.177358 -0.008244
			-0.012808  0.182239 -0.009890
			-0.017084  0.175854  0.016798
			-0.024231  0.172740  0.020958
			-0.038195  0.164642  0.004957
			-0.038457  0.164875  0.000003
			-0.038692  0.164722  0.014592
			-0.035239  0.167062  0.021071
			-0.017084  0.175854 -0.016798
			-0.024231  0.172740 -0.020958
			-0.038195  0.164642 -0.004957
			-0.038692  0.164722 -0.014592
			-0.035239  0.167062 -0.021071
			-0.014030  0.183975  0.000027
			-0.015636  0.183821  0.010588
			-0.018688  0.180550  0.017846
			-0.015636  0.183821 -0.010588
			-0.018688  0.180550 -0.017846
			-0.027505  0.177389  0.021676
			-0.041640  0.168144  0.005102
			-0.043637  0.166211  0.000003
			-0.041640  0.168144 -0.005102
			-0.038899  0.172046  0.021829
			-0.042069  0.163906  0.016388
			-0.043118  0.169312  0.009696
			-0.027505  0.177389 -0.021676
			-0.042069  0.163906 -0.016388
			-0.038899  0.172046 -0.021829
			-0.043118  0.169312 -0.009696
			-0.017552  0.194077  0.000030
			-0.018538  0.192398  0.010219
			-0.021914  0.182645  0.017400
			-0.018538  0.192398 -0.010219
			-0.021914  0.182645 -0.017400
			-0.029435  0.181759  0.019719
			-0.046224  0.175869  0.006542
			-0.045742  0.176084  0.000005
			-0.046224  0.175869 -0.006542
			-0.037870  0.178601  0.020660
			-0.044554  0.174869  0.019536
			-0.048281  0.175784  0.010005
			-0.029435  0.181759 -0.019719
			-0.044554  0.174869 -0.019536
			-0.037870  0.178601 -0.020660
			-0.048281  0.175784 -0.010005
			-0.018461  0.198386  0.000031
			-0.018930  0.197744  0.011304
			-0.024560  0.190830  0.018069
			-0.018930  0.197744 -0.011304
			-0.024560  0.190830 -0.018069
			-0.029501  0.189187  0.019809
			-0.049642  0.184903  0.006760
			-0.051771  0.184642  0.010461
			-0.048956  0.185277  0.000008
			-0.049642  0.184903 -0.006760
			-0.051771  0.184642 -0.010461
			-0.039696  0.185407  0.020809
			-0.044802  0.184721  0.021214
			-0.050470  0.172214  0.017599
			-0.048678  0.176186  0.021577
			-0.051237  0.182195  0.025800
			-0.056721  0.178438  0.003840
			-0.029501  0.189187 -0.019809
			-0.044802  0.184721 -0.021214
			-0.039696  0.185407 -0.020809
			-0.048678  0.176186 -0.021577
			-0.050470  0.172214 -0.017599
			-0.051237  0.182195 -0.025800
			-0.056721  0.178438 -0.003840
			-0.020211  0.202937  0.000032
			-0.020956  0.202386  0.011511
			-0.023983  0.195452  0.019126
			-0.020956  0.202386 -0.011511
			-0.023983  0.195452 -0.019126
			-0.029912  0.192694  0.021903
			-0.052785  0.192235  0.008618
			-0.052919  0.190174  0.011233
			-0.050799  0.191876  0.000009
			-0.052785  0.192235 -0.008618
			-0.052919  0.190174 -0.011233
			-0.039044  0.190655  0.022959
			-0.047380  0.188901  0.020500
			-0.054108  0.187926  0.020548
			-0.061169  0.181152  0.023816
			-0.057926  0.177393  0.017700
			-0.055021  0.171930  0.025475
			-0.050878  0.168739  0.010848
			-0.055481  0.181853  0.024502
			-0.056808  0.173820  0.022274
			-0.057511  0.172407  0.000004
			-0.051304  0.155144  0.005268
			-0.066115  0.178412  0.000005
			-0.061006  0.183888  0.007949
			-0.029912  0.192694 -0.021903
			-0.047380  0.188901 -0.020500
			-0.039044  0.190655 -0.022959
			-0.054108  0.187926 -0.020548
			-0.057926  0.177393 -0.017700
			-0.061169  0.181152 -0.023816
			-0.055021  0.171930 -0.025475
			-0.050878  0.168739 -0.010848
			-0.056808  0.173820 -0.022274
			-0.055481  0.181853 -0.024502
			-0.051304  0.155144 -0.005268
			-0.061006  0.183888 -0.007949
			-0.035518  0.197669  0.000010
			-0.025893  0.200380  0.019390
			-0.032910  0.198087  0.022961
			-0.025893  0.200380 -0.019390
			-0.032910  0.198087 -0.022961
			-0.041718  0.193898  0.023101
			-0.063010  0.193793  0.016478
			-0.054471  0.189913  0.014526
			-0.061848  0.191085  0.010364
			-0.050718  0.192407  0.017631
			-0.054471  0.189913 -0.014526
			-0.063010  0.193793 -0.016478
			-0.061848  0.191085 -0.010364
			-0.050718  0.192407 -0.017631
			-0.059646  0.186696  0.021870
			-0.059747  0.190266  0.018944
			-0.068973  0.191494  0.019845
			-0.066888  0.182389  0.014623
			-0.063074  0.187268  0.021910
			-0.064921  0.166413  0.013020
			-0.060755  0.169618  0.015939
			-0.072936  0.179534  0.017561
			-0.064729  0.180438  0.013058
			-0.052467  0.170003  0.012357
			-0.063250  0.162317  0.017556
			-0.059406  0.180814  0.027100
			-0.060247  0.177973  0.024714
			-0.063706  0.184898  0.025062
			-0.056178  0.161077  0.000003
			-0.050628  0.154194  0.006903
			-0.071827  0.163662  0.000004
			-0.067571  0.172531  0.005106
			-0.063369  0.172832  0.008549
			-0.063202  0.179136  0.010714
			-0.067571  0.172531 -0.005106
			-0.063369  0.172832 -0.008549
			-0.063202  0.179136 -0.010714
			-0.062258  0.184033  0.011691
			-0.041718  0.193898 -0.023101
			-0.059646  0.186696 -0.021870
			-0.059747  0.190266 -0.018944
			-0.060755  0.169618 -0.015939
			-0.064921  0.166413 -0.013020
			-0.066888  0.182389 -0.014623
			-0.072936  0.179534 -0.017561
			-0.064729  0.180438 -0.013058
			-0.068973  0.191494 -0.019844
			-0.063074  0.187268 -0.021910
			-0.052467  0.170003 -0.012357
			-0.063250  0.162317 -0.017556
			-0.060247  0.177973 -0.024714
			-0.059406  0.180814 -0.027100
			-0.063706  0.184898 -0.025062
			-0.050628  0.154194 -0.006903
			-0.062258  0.184033 -0.011691
			-0.073422  0.186266  0.016633
			-0.067211  0.186231  0.014339
			-0.073422  0.186266 -0.016633
			-0.067211  0.186231 -0.014339
			-0.071119  0.185404  0.021988
			-0.072110  0.182418  0.017086
			-0.066493  0.162655  0.010569
			-0.066242  0.156868  0.010001
			-0.059849  0.154591  0.017306
			-0.065523  0.168584  0.009115
			-0.051069  0.151467  0.013718
			-0.054370  0.156527  0.005489
			-0.058695  0.160637  0.000001
			-0.054370  0.156527 -0.005489
			-0.071053  0.166779  0.004312
			-0.075691  0.155210 -0.000016
			-0.069076  0.162319  0.004195
			-0.071053  0.166779 -0.004312
			-0.069076  0.162319 -0.004195
			-0.065523  0.168584 -0.009115
			-0.066242  0.156868 -0.010001
			-0.066493  0.162655 -0.010569
			-0.059849  0.154591 -0.017306
			-0.071119  0.185404 -0.021988
			-0.072110  0.182418 -0.017086
			-0.051069  0.151467 -0.013718
			-0.068913  0.156762  0.004376
			-0.067097  0.154951  0.005130
			-0.058974  0.155551  0.007867
			-0.063017  0.150092 -0.000000
			-0.058974  0.155551 -0.007867
			-0.067097  0.154951 -0.005130
			-0.081235  0.149107 -0.000021
			-0.068913  0.156762 -0.004376
			-0.072703  0.151656  0.004761
			-0.068406  0.150476  0.004779
			-0.070188  0.142438  0.003711
			-0.071901  0.141143 -0.000003
			-0.070188  0.142438 -0.003711
			-0.068406  0.150476 -0.004779
			-0.081475  0.145053  0.004814
			-0.081895  0.143293 -0.000023
			-0.072703  0.151656 -0.004761
			-0.081475  0.145053 -0.004814
			-0.078279  0.142092  0.005606
			-0.076527  0.140564 -0.000021
			-0.078279  0.142092 -0.005606
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
