<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.939705 -0.341985  0.000363
			 0.827331 -0.524290  0.201601
			 0.470626 -0.882333  0.000017
			 0.878876  0.357995  0.315304
			 0.968158  0.250340  0.000237
			 0.827423 -0.524244 -0.201342
			 0.878945  0.358100 -0.314992
			 0.757722 -0.412893  0.505348
			 0.487856 -0.449279  0.748429
			-0.140464 -0.989912 -0.018572
			-0.172448 -0.985019  0.000062
			-0.114804 -0.987148  0.111169
			 0.139427 -0.681740  0.718185
			 0.757720 -0.412901 -0.505344
			 0.487854 -0.449279 -0.748430
			-0.140460 -0.989912  0.018550
			-0.114799 -0.987149 -0.111167
			 0.139432 -0.681740 -0.718185
			 0.782860  0.622197  0.000196
			 0.700764  0.604160  0.379368
			 0.487724  0.404019  0.773883
			 0.700858  0.604257 -0.379040
			 0.487721  0.404019 -0.773884
			 0.122193  0.215105  0.968916
			-0.671017 -0.731741  0.119546
			-0.745208 -0.666832  0.000136
			-0.671032 -0.731752 -0.119399
			-0.248725 -0.131081  0.959663
			-0.623893 -0.698371  0.350765
			-0.646566 -0.683319 -0.339159
			 0.122192  0.215104 -0.968917
			-0.623893 -0.698371 -0.350765
			-0.248721 -0.131082 -0.959664
			-0.646566 -0.683319  0.339159
			 0.947494  0.319774  0.000384
			 0.898289  0.203298  0.389547
			 0.442867  0.404965  0.799920
			 0.898401  0.203342 -0.389266
			 0.442867  0.404961 -0.799922
			 0.139540  0.174919  0.974645
			-0.876186 -0.441793 -0.192655
			-0.944145 -0.329530 -0.000081
			-0.876184 -0.441790  0.192674
			-0.157848 -0.001332  0.987463
			 0.099509 -0.483567  0.869633
			-0.435404 -0.727581 -0.530140
			 0.139543  0.174914 -0.974645
			 0.099511 -0.483565 -0.869633
			-0.157852 -0.001327 -0.987462
			-0.435404 -0.727581  0.530140
			 0.969750  0.244101  0.000216
			 0.927154  0.181182  0.327960
			 0.607739 -0.037733  0.793240
			 0.927205  0.181211 -0.327801
			 0.607737 -0.037728 -0.793242
			 0.305893 -0.184341  0.934049
			-0.903785 -0.330962 -0.271363
			-0.379735 -0.070654 -0.922393
			-0.934840 -0.355070 -0.000160
			-0.903804 -0.330993  0.271261
			-0.379735 -0.070654  0.922393
			 0.117988 -0.248053  0.961535
			 0.135436  0.040316  0.989966
			 0.087990 -0.972540  0.215461
			 0.062692 -0.627732  0.775901
			 0.056708 -0.034424  0.997797
			 0.602414  0.304190 -0.737947
			 0.305890 -0.184336 -0.934051
			 0.135443  0.040313 -0.989965
			 0.117991 -0.248054 -0.961534
			 0.062692 -0.627732 -0.775901
			 0.087990 -0.972540 -0.215461
			 0.056708 -0.034424 -0.997797
			 0.602624  0.304310  0.737726
			 0.640917  0.767610  0.000193
			 0.469605  0.809169  0.353153
			 0.632679  0.003615  0.774405
			 0.469640  0.809211 -0.353009
			 0.632680  0.003617 -0.774405
			 0.281969 -0.217572  0.934428
			-0.698434  0.598215 -0.392848
			-0.187224 -0.091252 -0.978070
			-0.904914  0.425596 -0.000237
			-0.698502  0.598237  0.392692
			-0.187224 -0.091252  0.978070
			-0.011147 -0.203983  0.978911
			-0.246427  0.134790  0.959742
			-0.083752  0.659354  0.747153
			-0.546645 -0.411504  0.729276
			-0.431456 -0.573456  0.696415
			 0.338238 -0.099307  0.935806
			 0.802134 -0.211459  0.558450
			 0.086575  0.291856  0.952536
			-0.494511 -0.581091  0.646368
			 0.924273  0.381733 -0.000359
			 0.941572 -0.081092 -0.326905
			-0.736703  0.676217  0.000015
			-0.506161  0.376389 -0.775972
			 0.281972 -0.217572 -0.934427
			-0.246427  0.134790 -0.959742
			-0.011143 -0.203989 -0.978910
			-0.083752  0.659354 -0.747153
			-0.431456 -0.573456 -0.696415
			-0.546648 -0.411503 -0.729274
			 0.338238 -0.099307 -0.935806
			 0.802133 -0.211461 -0.558451
			-0.494506 -0.581085 -0.646377
			 0.086569  0.291854 -0.952537
			 0.941671 -0.081095  0.326619
			-0.506089  0.376446  0.775991
			-0.355703  0.934599  0.000001
			 0.173877  0.823214  0.540449
			 0.032765  0.549330  0.834963
			 0.173873  0.823216 -0.540447
			 0.032765  0.549335 -0.834960
			-0.290540  0.377222  0.879369
			-0.284926  0.930349 -0.230800
			-0.055623  0.924268  0.377671
			-0.738422  0.343002 -0.580589
			-0.485458  0.770660  0.412811
			-0.055623  0.924268 -0.377671
			-0.284929  0.930348  0.230800
			-0.738420  0.343003  0.580591
			-0.485447  0.770673 -0.412800
			 0.207784  0.627904  0.750042
			 0.184330  0.729637  0.658523
			-0.287569  0.499320  0.817303
			 0.081655 -0.419758  0.903956
			 0.028824  0.462476  0.886163
			-0.846824 -0.021713  0.531431
			-0.136671  0.028624  0.990203
			-0.845677 -0.314387 -0.431267
			-0.889850 -0.055181 -0.452903
			 0.936178  0.150077  0.317882
			-0.349075  0.177039  0.920219
			 0.088400  0.007846  0.996054
			-0.682564 -0.601947  0.414447
			-0.984639 -0.168312  0.046442
			-0.007701 -0.999970 -0.000266
			 0.253944 -0.831617 -0.493887
			-0.976749  0.214386  0.000188
			-0.815532  0.468397  0.339870
			-0.917735  0.346081  0.194911
			-0.942319  0.229256 -0.243878
			-0.815469  0.468555 -0.339803
			-0.917706  0.346234 -0.194775
			-0.942302  0.229244  0.243956
			-0.760286  0.116798 -0.639002
			-0.290538  0.377233 -0.879365
			 0.207783  0.627902 -0.750044
			 0.184327  0.729637 -0.658523
			-0.136671  0.028624 -0.990203
			-0.846824 -0.021713 -0.531431
			 0.081655 -0.419755 -0.903957
			-0.845677 -0.314400  0.431259
			-0.889850 -0.055188  0.452902
			-0.287576  0.499321 -0.817299
			 0.028816  0.462476 -0.886163
			 0.936176  0.150072 -0.317887
			-0.349075  0.177039 -0.920219
			-0.682555 -0.601945 -0.414464
			 0.088403  0.007847 -0.996054
			-0.984638 -0.168314 -0.046455
			 0.253856 -0.831745  0.493719
			-0.760287  0.116794  0.639001
			-0.912147  0.380409 -0.152569
			-0.373517  0.156468 -0.914332
			-0.912146  0.380410  0.152572
			-0.373523  0.156472  0.914329
			-0.300605 -0.403147  0.864355
			-0.050808 -0.332151  0.941857
			-0.906692  0.023067  0.421162
			-0.669732 -0.577302  0.467100
			-0.559708 -0.726189  0.399220
			-0.945094  0.141094  0.294770
			 0.388258 -0.853574  0.347371
			 0.074210 -0.915116 -0.396302
			 0.639515 -0.768779 -0.000223
			 0.074055 -0.915225  0.396081
			-0.832827  0.106214  0.543248
			-0.921443  0.388513 -0.000819
			-0.802186 -0.042511  0.595559
			-0.832885  0.106179 -0.543165
			-0.801982 -0.042038 -0.595867
			-0.945094  0.141094 -0.294770
			-0.669732 -0.577302 -0.467100
			-0.906692  0.023067 -0.421162
			-0.559708 -0.726189 -0.399220
			-0.300604 -0.403143 -0.864357
			-0.050808 -0.332151 -0.941857
			 0.388258 -0.853574 -0.347371
			-0.649126  0.032062  0.760005
			 0.078218 -0.694498  0.715230
			-0.090140 -0.989557 -0.112478
			 0.757222 -0.653158 -0.000027
			-0.090140 -0.989557  0.112478
			 0.078200 -0.694479 -0.715251
			-0.848614  0.529011 -0.001416
			-0.648041  0.031770 -0.760943
			-0.181384  0.209598  0.960817
			 0.059348 -0.068375  0.995893
			 0.475100 -0.455902  0.752617
			 0.371402 -0.928472 -0.000521
			 0.475073 -0.455857 -0.752662
			 0.059335 -0.068375 -0.995894
			-0.796798 -0.216957  0.563953
			-0.874251 -0.485474 -0.001024
			-0.180529  0.208840 -0.961143
			-0.795674 -0.217036 -0.565508
			-0.066056 -0.709027  0.702081
			-0.330275 -0.943884 -0.000992
			-0.065422 -0.708471 -0.702702
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.000728  0.147993  0.000025
			-0.001628  0.147914  0.008244
			-0.015038  0.140507  0.000003
			-0.002147  0.152779  0.009890
			-0.000580  0.150055  0.000027
			-0.001628  0.147914 -0.008244
			-0.002147  0.152779 -0.009890
			-0.006206  0.146240  0.016798
			-0.013244  0.142864  0.020958
			-0.026926  0.134254  0.004957
			-0.027196  0.134478  0.000003
			-0.027425  0.134317  0.014592
			-0.024054  0.136782  0.021071
			-0.006206  0.146240 -0.016798
			-0.013244  0.142864 -0.020958
			-0.026926  0.134254 -0.004957
			-0.027425  0.134317 -0.014592
			-0.024054  0.136782 -0.021071
			-0.003427  0.154470  0.000027
			-0.005026  0.154256  0.010588
			-0.007966  0.150874  0.017846
			-0.005026  0.154256 -0.010588
			-0.007966  0.150874 -0.017846
			-0.016671  0.147390  0.021676
			-0.030485  0.137628  0.005102
			-0.032416  0.135623  0.000003
			-0.030485  0.137628 -0.005102
			-0.027877  0.141629  0.021829
			-0.030773  0.133377  0.016388
			-0.032001  0.138742  0.009696
			-0.016671  0.147390 -0.021676
			-0.030773  0.133377 -0.016388
			-0.027877  0.141629 -0.021829
			-0.032001  0.138742 -0.009696
			-0.007283  0.164438  0.000030
			-0.008212  0.162723  0.010219
			-0.011260  0.152850  0.017400
			-0.008212  0.162723 -0.010219
			-0.011260  0.152850 -0.017400
			-0.018746  0.151687  0.019719
			-0.035324  0.145181  0.006542
			-0.034850  0.145413  0.000005
			-0.035324  0.145181 -0.006542
			-0.027068  0.148219  0.020660
			-0.033622  0.144242  0.019536
			-0.037377  0.145020  0.010005
			-0.018746  0.151687 -0.019719
			-0.033622  0.144242 -0.019536
			-0.027068  0.148219 -0.020660
			-0.037377  0.145020 -0.010005
			-0.008336  0.168711  0.000031
			-0.008783  0.168052  0.011304
			-0.014177  0.160933  0.018069
			-0.008783  0.168052 -0.011304
			-0.014177  0.160933 -0.018069
			-0.019060  0.159109  0.019809
			-0.039042  0.154085  0.006760
			-0.041160  0.153745  0.010461
			-0.038368  0.154484  0.000008
			-0.039042  0.154085 -0.006760
			-0.041160  0.153745 -0.010461
			-0.029121  0.154955  0.020809
			-0.034199  0.154081  0.021214
			-0.039444  0.141371  0.017599
			-0.037787  0.145408  0.021577
			-0.040545  0.151319  0.025800
			-0.045899  0.147361  0.003840
			-0.019060  0.159109 -0.019809
			-0.034199  0.154081 -0.021214
			-0.029121  0.154955 -0.020809
			-0.037787  0.145408 -0.021577
			-0.039444  0.141371 -0.017599
			-0.040545  0.151319 -0.025800
			-0.045899  0.147361 -0.003840
			-0.010237  0.173195  0.000032
			-0.010963  0.172617  0.011511
			-0.013755  0.165575  0.019126
			-0.010963  0.172617 -0.011511
			-0.013755  0.165575 -0.019126
			-0.019587  0.162599  0.021903
			-0.042427  0.161298  0.008618
			-0.042492  0.159233  0.011233
			-0.040431  0.161012  0.000009
			-0.042427  0.161298 -0.008618
			-0.042492  0.159233 -0.011233
			-0.028644  0.160225  0.022959
			-0.036915  0.158164  0.020500
			-0.043605  0.156942  0.020548
			-0.050434  0.149910  0.023816
			-0.047068  0.146273  0.017700
			-0.043982  0.140920  0.025475
			-0.039736  0.137883  0.010848
			-0.044774  0.150821  0.024502
			-0.045832  0.142743  0.022274
			-0.046486  0.141304  0.000004
			-0.039707  0.124278  0.005268
			-0.055285  0.146990  0.000005
			-0.050362  0.152651  0.007949
			-0.019587  0.162599 -0.021903
			-0.036915  0.158164 -0.020500
			-0.028644  0.160225 -0.022959
			-0.043605  0.156942 -0.020548
			-0.047068  0.146273 -0.017700
			-0.050434  0.149910 -0.023816
			-0.043982  0.140920 -0.025475
			-0.039736  0.137883 -0.010848
			-0.045832  0.142743 -0.022274
			-0.044774  0.150821 -0.024502
			-0.039707  0.124278 -0.005268
			-0.050362  0.152651 -0.007949
			-0.025355  0.167365  0.000010
			-0.015829  0.170430  0.019390
			-0.022764  0.167880  0.022961
			-0.015829  0.170430 -0.019390
			-0.022764  0.167880 -0.022961
			-0.031424  0.163367  0.023101
			-0.052696  0.162478  0.016478
			-0.044035  0.158915  0.014526
			-0.051445  0.159814  0.010364
			-0.040368  0.161546  0.017631
			-0.044035  0.158915 -0.014526
			-0.052696  0.162478 -0.016478
			-0.051445  0.159814 -0.010364
			-0.040368  0.161546 -0.017631
			-0.049098  0.155508  0.021870
			-0.049318  0.159073  0.018944
			-0.058578  0.159961  0.019845
			-0.056190  0.150936  0.014623
			-0.052542  0.155954  0.021910
			-0.053690  0.135040  0.013020
			-0.049635  0.138397  0.015939
			-0.062138  0.147860  0.017561
			-0.053968  0.149065  0.013058
			-0.041366  0.139088  0.012357
			-0.051883  0.131008  0.017556
			-0.048661  0.149638  0.027100
			-0.049407  0.146767  0.024714
			-0.053094  0.153562  0.025062
			-0.044776  0.130029  0.000003
			-0.039000  0.123353  0.006903
			-0.060499  0.132036  0.000004
			-0.056543  0.141058  0.005106
			-0.052354  0.141513  0.008549
			-0.052398  0.147820  0.010714
			-0.056543  0.141058 -0.005106
			-0.052354  0.141513 -0.008549
			-0.052398  0.147820 -0.010714
			-0.051618  0.152750  0.011691
			-0.031424  0.163367 -0.023101
			-0.049098  0.155508 -0.021870
			-0.049318  0.159073 -0.018944
			-0.049635  0.138397 -0.015939
			-0.053690  0.135040 -0.013020
			-0.056190  0.150936 -0.014623
			-0.062138  0.147860 -0.017561
			-0.053968  0.149065 -0.013058
			-0.058578  0.159961 -0.019844
			-0.052542  0.155954 -0.021910
			-0.041366  0.139088 -0.012357
			-0.051883  0.131008 -0.017556
			-0.049407  0.146767 -0.024714
			-0.048661  0.149638 -0.027100
			-0.053094  0.153562 -0.025062
			-0.039000  0.123353 -0.006903
			-0.051618  0.152750 -0.011691
			-0.062849  0.154571  0.016633
			-0.056641  0.154765  0.014339
			-0.062849  0.154571 -0.016633
			-0.056641  0.154765 -0.014339
			-0.060518  0.153794  0.021988
			-0.061408  0.150773  0.017086
			-0.055135  0.131226  0.010569
			-0.054691  0.125451  0.010001
			-0.048227  0.123411  0.017306
			-0.054365  0.137188  0.009115
			-0.039350  0.120612  0.013718
			-0.042817  0.125547  0.005489
			-0.047276  0.129496  0.000001
			-0.042817  0.125547 -0.005489
			-0.059830  0.135180  0.004312
			-0.064077  0.123445 -0.000016
			-0.057705  0.130795  0.004195
			-0.059830  0.135180 -0.004312
			-0.057705  0.130795 -0.004195
			-0.054365  0.137188 -0.009115
			-0.054691  0.125451 -0.010001
			-0.055135  0.131226 -0.010569
			-0.048227  0.123411 -0.017306
			-0.060518  0.153794 -0.021988
			-0.061408  0.150773 -0.017086
			-0.039350  0.120612 -0.013718
			-0.057356  0.125246  0.004376
			-0.055482  0.123504  0.005130
			-0.047385  0.124402  0.007867
			-0.051242  0.118797 -0.000000
			-0.047385  0.124402 -0.007867
			-0.055482  0.123504 -0.005130
			-0.069413  0.117140 -0.000021
			-0.057356  0.125246 -0.004376
			-0.060973  0.120003  0.004761
			-0.056639  0.118982  0.004779
			-0.058151  0.110882  0.003711
			-0.059820  0.109524 -0.000003
			-0.058151  0.110882 -0.003711
			-0.056639  0.118982 -0.004779
			-0.069517  0.113080  0.004814
			-0.069878  0.111305 -0.000023
			-0.060973  0.120003 -0.004761
			-0.069517  0.113080 -0.004814
			-0.066224  0.110238  0.005606
			-0.064423  0.108775 -0.000021
			-0.066224  0.110238 -0.005606
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
