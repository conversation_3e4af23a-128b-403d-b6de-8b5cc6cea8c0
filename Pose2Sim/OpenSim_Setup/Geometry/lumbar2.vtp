<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.338322 -0.941030  0.000000
			 0.685755 -0.697749 -0.207090
			 0.883323 -0.468765  0.000000
			 0.753388 -0.384589 -0.533384
			 0.418669 -0.236539 -0.876793
			-0.937672 -0.347522  0.000000
			-0.650568 -0.759294  0.015326
			-0.895403 -0.369406 -0.248583
			-0.158849 -0.361598 -0.918702
			 0.685756 -0.697748  0.207090
			 0.753388 -0.384583  0.533387
			 0.418664 -0.236535  0.876796
			-0.650568 -0.759294 -0.015326
			-0.895403 -0.369406  0.248583
			-0.158849 -0.361598  0.918702
			 0.937979  0.234184 -0.255645
			 0.983852  0.178983  0.000000
			 0.937978  0.234187  0.255644
			 0.476217  0.480497 -0.736437
			 0.091564  0.374933 -0.922519
			-0.980399 -0.185491  0.066412
			-0.929691 -0.368341  0.000000
			-0.980399 -0.185491 -0.066412
			-0.755735 -0.412787 -0.508401
			-0.506580  0.208848 -0.836516
			-0.866609 -0.487453  0.106670
			 0.476212  0.480503  0.736437
			 0.091564  0.374933  0.922519
			-0.506580  0.208848  0.836516
			-0.755736 -0.412785  0.508400
			-0.866610 -0.487451 -0.106667
			 0.796086  0.460944 -0.392144
			 0.872518  0.488583  0.000000
			 0.796086  0.460944  0.392144
			 0.515851  0.333314 -0.789176
			 0.146224  0.184389 -0.971915
			-0.762754 -0.603022  0.233603
			-0.817127 -0.576458  0.000000
			-0.762755 -0.603024 -0.233598
			 0.133301 -0.643472 -0.753773
			-0.102893 -0.105615 -0.989070
			-0.278996 -0.865241  0.416557
			 0.515851  0.333314  0.789176
			 0.146224  0.184389  0.971915
			-0.102893 -0.105615  0.989070
			 0.133301 -0.643472  0.753774
			-0.279003 -0.865242 -0.416549
			 0.901315  0.203417 -0.382430
			 0.953834  0.300333  0.000000
			 0.901315  0.203417  0.382430
			 0.610047 -0.012690 -0.792264
			 0.304650 -0.167365 -0.937645
			-0.861546 -0.429031  0.271423
			-0.368344 -0.152538  0.917090
			-0.909538 -0.415622  0.000000
			-0.861546 -0.429031 -0.271423
			-0.368348 -0.152542 -0.917088
			 0.137952  0.031081 -0.989951
			 0.117580 -0.252337 -0.960469
			 0.167643 -0.746892 -0.643466
			 0.348219 -0.900797 -0.259440
			 0.663947 -0.210651 -0.717496
			 0.596022  0.421014  0.683743
			 0.610047 -0.012690  0.792264
			 0.304650 -0.167365  0.937645
			 0.117580 -0.252337  0.960469
			 0.137952  0.031081  0.989951
			 0.348218 -0.900797  0.259441
			 0.167643 -0.746892  0.643466
			 0.663947 -0.210651  0.717496
			 0.596021  0.421015 -0.683743
			 0.928964  0.174108 -0.326670
			 0.972446  0.233127  0.000000
			 0.928964  0.174108  0.326670
			 0.635956  0.004296 -0.771714
			 0.283404 -0.209022 -0.935944
			-0.707521  0.588289  0.391575
			-0.180019 -0.085220  0.979965
			-0.906169  0.422915  0.000000
			-0.707521  0.588289 -0.391575
			-0.180019 -0.085220 -0.979965
			-0.251861  0.130387 -0.958939
			-0.007560 -0.194181 -0.980936
			 0.144160  0.773909 -0.616671
			-0.219201 -0.751207 -0.622607
			-0.498748 -0.533213 -0.683326
			-0.106690 -0.886188 -0.450875
			 0.906690 -0.010209 -0.421675
			 0.219344 -0.970271 -0.102288
			 0.729457  0.307214 -0.611156
			 0.899945  0.163224  0.404297
			 0.799954  0.600061  0.000000
			-0.746741  0.665115  0.000002
			-0.536492  0.315742  0.782613
			 0.635956  0.004296  0.771714
			 0.283404 -0.209022  0.935944
			-0.007560 -0.194181  0.980936
			-0.251861  0.130387  0.958939
			 0.144160  0.773909  0.616671
			-0.498750 -0.533215  0.683323
			-0.219201 -0.751207  0.622607
			-0.106690 -0.886188  0.450875
			 0.906690 -0.010209  0.421675
			 0.729457  0.307214  0.611156
			 0.219345 -0.970270  0.102293
			 0.899945  0.163224 -0.404297
			-0.536494  0.315743 -0.782611
			 0.458988  0.809497 -0.366122
			 0.642400  0.766370  0.000000
			 0.458988  0.809497  0.366122
			 0.143872  0.820109 -0.553825
			 0.019031  0.545153 -0.838120
			-0.299235  0.377613 -0.876280
			-0.039679  0.918555 -0.393296
			-0.345945  0.917083  0.198194
			-0.738792  0.353899  0.573535
			-0.386598  0.922249  0.000000
			-0.514948  0.752481 -0.410611
			-0.345943  0.917083 -0.198196
			-0.039679  0.918555  0.393296
			-0.738793  0.353898 -0.573533
			-0.514948  0.752481  0.410611
			-0.487676  0.620919 -0.613703
			 0.114717  0.838182 -0.533190
			-0.042496 -0.000675 -0.999096
			-0.856282 -0.074255 -0.511142
			-0.205828 -0.679481 -0.704231
			-0.470559 -0.636649  0.610943
			-0.917259 -0.185198  0.352614
			-0.298364  0.520559 -0.799998
			-0.478308  0.478687 -0.736261
			 0.959291  0.150658 -0.238878
			-0.006697  0.118364 -0.992948
			-0.531530 -0.825818 -0.188418
			 0.331272 -0.053312 -0.942028
			-0.970890 -0.117724  0.208597
			 0.887728 -0.040845  0.458554
			 0.999449 -0.033199  0.000000
			-0.681775  0.379777 -0.625261
			-0.882265  0.470753  0.000002
			-0.955434  0.198921 -0.218118
			-0.975878  0.044555  0.213719
			-0.681777  0.379779  0.625259
			-0.955435  0.198922  0.218115
			-0.975879  0.044558 -0.213718
			-0.788395  0.092128  0.608232
			 0.143872  0.820109  0.553825
			 0.019031  0.545153  0.838120
			-0.299235  0.377613  0.876280
			-0.487674  0.620919  0.613704
			 0.114717  0.838183  0.533187
			-0.298368  0.520558  0.799997
			-0.205828 -0.679481  0.704231
			-0.478313  0.478685  0.736259
			-0.856282 -0.074255  0.511142
			-0.042496 -0.000675  0.999096
			-0.470556 -0.636648 -0.610947
			-0.917260 -0.185194 -0.352616
			 0.959291  0.150658  0.238878
			-0.006697  0.118364  0.992948
			 0.331276 -0.053305  0.942027
			-0.531526 -0.825817  0.188432
			-0.970893 -0.117728 -0.208585
			 0.887728 -0.040845 -0.458554
			-0.788393  0.092131 -0.608234
			-0.813048  0.215575  0.540814
			-0.405943  0.156776  0.900351
			-0.813048  0.215575 -0.540814
			-0.405936  0.156775 -0.900354
			-0.558612 -0.657381 -0.505770
			-0.764693 -0.045422 -0.642792
			-0.641811 -0.684556 -0.345632
			-0.933821  0.198074 -0.297901
			-0.632931 -0.116258 -0.765430
			-0.634935 -0.767993 -0.083933
			 0.772399 -0.616974 -0.150808
			 0.649172 -0.606540  0.459005
			 0.959241 -0.282587  0.000000
			 0.649172 -0.606540 -0.459005
			-0.694880  0.226206 -0.682622
			-0.627347  0.172309 -0.759438
			-0.921180  0.389138  0.000000
			-0.694881  0.226211  0.682619
			-0.627347  0.172309  0.759438
			-0.933821  0.198074  0.297901
			-0.632931 -0.116258  0.765430
			-0.634935 -0.767993  0.083933
			-0.764693 -0.045422  0.642792
			-0.558612 -0.657381  0.505770
			-0.641811 -0.684556  0.345632
			 0.772399 -0.616974  0.150808
			-0.391775 -0.127919 -0.911125
			-0.094607 -0.984624  0.146852
			 0.129052 -0.713602 -0.688562
			 0.686537 -0.727095  0.000000
			-0.094607 -0.984624 -0.146852
			 0.129052 -0.713602  0.688562
			-0.968078  0.250649  0.000000
			-0.391775 -0.127919  0.911125
			-0.097941  0.161529 -0.981996
			-0.002171 -0.087877 -0.996129
			 0.349634 -0.523961 -0.776673
			 0.304186 -0.952613  0.000000
			 0.349634 -0.523961  0.776673
			-0.002171 -0.087877  0.996129
			-0.684607 -0.325998 -0.651950
			-0.730377 -0.683045  0.000000
			-0.684607 -0.325998  0.651950
			-0.097941  0.161529  0.981996
			-0.077140 -0.685265 -0.724197
			-0.391815 -0.920044  0.000000
			-0.077140 -0.685265  0.724197
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.001762  0.006343  0.000000
			 0.014785  0.013410 -0.008983
			 0.013830  0.009016  0.000000
			 0.010159  0.011784 -0.018384
			 0.003032  0.008366 -0.022961
			-0.015067  0.001624  0.000000
			-0.014846  0.001312 -0.005395
			-0.015316  0.001512 -0.015981
			-0.010712  0.004746 -0.023095
			 0.014785  0.013410  0.008983
			 0.010159  0.011784  0.018384
			 0.003032  0.008366  0.022961
			-0.014846  0.001312  0.005395
			-0.015316  0.001512  0.015981
			-0.010712  0.004746  0.023095
			 0.015039  0.014755 -0.010789
			 0.014692  0.015381  0.000000
			 0.015039  0.014755  0.010789
			 0.008364  0.014313 -0.017755
			-0.000149  0.013462 -0.021586
			-0.014005  0.007541 -0.005046
			-0.013921  0.007415  0.000000
			-0.014005  0.007541  0.005046
			-0.014655  0.009293 -0.016318
			-0.010367  0.008154 -0.021747
			-0.015504  0.008654 -0.009635
			 0.008364  0.014313  0.017755
			-0.000149  0.013462  0.021586
			-0.010367  0.008154  0.021747
			-0.014655  0.009293  0.016318
			-0.015504  0.008654  0.009635
			 0.010695  0.019963 -0.010502
			 0.012155  0.020090  0.000000
			 0.010695  0.019963  0.010502
			 0.004981  0.018849 -0.017309
			-0.001876  0.018050 -0.019629
			-0.018904  0.013521 -0.006481
			-0.018437  0.013777  0.000000
			-0.018904  0.013521  0.006481
			-0.017213  0.012613 -0.019459
			-0.011816  0.015408 -0.020577
			-0.020938  0.013278 -0.009942
			 0.004981  0.018849  0.017309
			-0.001876  0.018050  0.019629
			-0.011816  0.015408  0.020577
			-0.017213  0.012613  0.019459
			-0.020938  0.013278  0.009942
			 0.007493  0.028940 -0.010130
			 0.008412  0.030762  0.000000
			 0.007493  0.028940  0.010130
			 0.001581  0.026848 -0.017974
			-0.003261  0.024756 -0.019716
			-0.023126  0.019279 -0.006696
			-0.025225  0.018846 -0.010395
			-0.022461  0.019716  0.000000
			-0.023126  0.019279  0.006696
			-0.025225  0.018846  0.010395
			-0.018319  0.019469 -0.021130
			-0.013283  0.020575 -0.020722
			-0.021338  0.013678 -0.021499
			-0.022990  0.009386 -0.017530
			-0.024612  0.016342 -0.025716
			-0.029872  0.012040 -0.003789
			 0.001581  0.026848  0.017974
			-0.003261  0.024756  0.019716
			-0.013283  0.020575  0.020722
			-0.018319  0.019469  0.021130
			-0.022990  0.009386  0.017530
			-0.021338  0.013678  0.021499
			-0.024612  0.016342  0.025716
			-0.029872  0.012040  0.003789
			 0.006938  0.034499 -0.011210
			 0.007376  0.035197  0.000000
			 0.006938  0.034499  0.011210
			 0.002008  0.031725 -0.019027
			-0.003777  0.028392 -0.021805
			-0.026469  0.026705 -0.008549
			-0.026536  0.024542 -0.011162
			-0.024494  0.026474  0.000000
			-0.026469  0.026705  0.008549
			-0.026536  0.024542  0.011162
			-0.021004  0.023641 -0.020416
			-0.012801  0.026112 -0.022866
			-0.027639  0.022110 -0.020468
			-0.031072  0.010941 -0.017633
			-0.034270  0.014810 -0.023741
			-0.027132  0.009978 -0.025399
			-0.023876  0.005642 -0.010789
			-0.028897  0.011919 -0.029720
			-0.028601  0.015641 -0.032692
			-0.025567  0.000252 -0.005221
			-0.029620  0.010412  0.000000
			-0.038795  0.010041  0.000000
			-0.034096  0.017653 -0.007892
			 0.002008  0.031725  0.019027
			-0.003777  0.028392  0.021805
			-0.012801  0.026112  0.022866
			-0.021004  0.023641  0.020416
			-0.027639  0.022110  0.020468
			-0.034270  0.014810  0.023741
			-0.031072  0.010941  0.017633
			-0.027132  0.009978  0.025399
			-0.023876  0.005642  0.010789
			-0.028601  0.015641  0.032692
			-0.028897  0.011919  0.029720
			-0.025567  0.000252  0.005221
			-0.034096  0.017653  0.007892
			 0.004784  0.039196 -0.011416
			 0.005500  0.039820  0.000000
			 0.004784  0.039196  0.011416
			-0.000039  0.036730 -0.019289
			-0.006916  0.033802 -0.022860
			-0.015551  0.029297 -0.023008
			-0.028063  0.024154 -0.014452
			-0.036016  0.027955 -0.016406
			-0.034882  0.025146 -0.010300
			-0.009497  0.033147  0.000000
			-0.024422  0.027049 -0.017549
			-0.036016  0.027955  0.016406
			-0.028063  0.024154  0.014452
			-0.034882  0.025146  0.010300
			-0.024422  0.027049  0.017549
			-0.032714  0.020618 -0.029174
			-0.032792  0.024335 -0.018869
			-0.031654  0.002940 -0.015879
			-0.035459 -0.000450 -0.012968
			-0.039983  0.014369 -0.021653
			-0.040029  0.012831 -0.014540
			-0.037395  0.012187 -0.013000
			-0.042683  0.023561 -0.019979
			-0.036123  0.021155 -0.021835
			-0.022606 -0.001300 -0.012299
			-0.033947 -0.004697 -0.017501
			-0.033372  0.011514 -0.032981
			-0.032513  0.014494 -0.036167
			-0.036765  0.018678 -0.033444
			-0.023987 -0.005268 -0.006855
			-0.028957  0.000134  0.000000
			-0.038663  0.005837 -0.005062
			-0.045492 -0.001030  0.000000
			-0.034477  0.006225 -0.008498
			-0.035885  0.010854 -0.010659
			-0.038663  0.005837  0.005062
			-0.034477  0.006225  0.008498
			-0.035885  0.010854  0.010659
			-0.035339  0.017787 -0.011630
			-0.000039  0.036730  0.019289
			-0.006916  0.033802  0.022860
			-0.015551  0.029297  0.023008
			-0.032714  0.020618  0.029174
			-0.032792  0.024335  0.018869
			-0.042683  0.023561  0.019979
			-0.039983  0.014369  0.021653
			-0.036123  0.021155  0.021835
			-0.035459 -0.000450  0.012968
			-0.031654  0.002940  0.015879
			-0.040029  0.012831  0.014540
			-0.037395  0.012187  0.013000
			-0.022606 -0.001300  0.012299
			-0.033947 -0.004697  0.017501
			-0.032513  0.014494  0.036167
			-0.033372  0.011514  0.032981
			-0.036765  0.018678  0.033444
			-0.023987 -0.005268  0.006855
			-0.035339  0.017787  0.011630
			-0.047150  0.018030 -0.016744
			-0.039822  0.018187 -0.014277
			-0.047150  0.018030  0.016744
			-0.039822  0.018187  0.014277
			-0.035599 -0.009248 -0.009958
			-0.036903 -0.004387 -0.010524
			-0.031712 -0.011676 -0.017254
			-0.036004  0.001804 -0.009067
			-0.044860  0.017176 -0.022146
			-0.045871  0.014040 -0.017202
			-0.022843 -0.013948 -0.013666
			-0.025839 -0.008717 -0.005445
			-0.029813 -0.006412  0.000000
			-0.025839 -0.008717  0.005445
			-0.041047 -0.000144 -0.004274
			-0.042845 -0.005343 -0.004160
			-0.053517 -0.007652  0.000000
			-0.041047 -0.000144  0.004274
			-0.042845 -0.005343  0.004160
			-0.036004  0.001804  0.009067
			-0.044860  0.017176  0.022146
			-0.045871  0.014040  0.017202
			-0.036903 -0.004387  0.010524
			-0.035599 -0.009248  0.009958
			-0.031712 -0.011676  0.017254
			-0.022843 -0.013948  0.013666
			-0.041619 -0.009964 -0.004343
			-0.030916 -0.010674 -0.007826
			-0.036387 -0.011260 -0.005095
			-0.034619 -0.016416  0.000000
			-0.030916 -0.010674  0.007826
			-0.036387 -0.011260  0.005095
			-0.053970 -0.013334  0.000000
			-0.041619 -0.009964  0.004343
			-0.044128 -0.014541 -0.004733
			-0.040218 -0.015723 -0.004749
			-0.040258 -0.020205 -0.003687
			-0.041824 -0.021577  0.000000
			-0.040258 -0.020205  0.003687
			-0.040218 -0.015723  0.004749
			-0.050530 -0.017604 -0.004792
			-0.050920 -0.019447  0.000000
			-0.050530 -0.017604  0.004792
			-0.044128 -0.014541  0.004733
			-0.047627 -0.020655 -0.005584
			-0.046039 -0.022233  0.000000
			-0.047627 -0.020655  0.005584
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 3 1 
					0 4 3 
					0 5 6 
					6 7 0 
					0 7 8 
					8 4 0 
					2 9 0 
					9 10 0 
					10 11 0 
					12 5 0 
					0 13 12 
					14 13 0 
					0 11 14 
					2 1 15 
					15 1 3 
					16 2 15 
					17 9 2 
					17 2 16 
					18 15 3 
					18 3 4 
					19 18 4 
					19 4 8 
					20 6 5 
					21 20 5 
					5 12 22 
					5 22 21 
					6 20 7 
					7 23 24 
					8 7 24 
					25 23 7 
					20 25 7 
					24 19 8 
					10 9 17 
					10 17 26 
					11 10 26 
					11 26 27 
					14 11 27 
					13 22 12 
					28 29 13 
					28 13 14 
					13 29 30 
					13 30 22 
					14 27 28 
					15 31 32 
					16 15 32 
					31 15 18 
					32 17 16 
					32 33 17 
					26 17 33 
					34 31 18 
					34 18 19 
					35 34 19 
					35 19 24 
					20 21 36 
					25 20 36 
					37 36 21 
					21 38 37 
					38 21 22 
					38 22 30 
					23 39 40 
					24 23 40 
					41 23 25 
					41 39 23 
					40 35 24 
					41 25 36 
					26 33 42 
					27 26 42 
					27 42 43 
					28 27 43 
					28 43 44 
					44 29 28 
					44 45 29 
					30 29 46 
					29 45 46 
					38 30 46 
					31 47 48 
					32 31 48 
					47 31 34 
					48 33 32 
					48 49 33 
					42 33 49 
					50 47 34 
					50 34 35 
					51 50 35 
					51 35 40 
					52 36 37 
					52 53 36 
					53 41 36 
					54 52 37 
					37 38 55 
					37 55 54 
					38 56 55 
					38 46 56 
					39 57 58 
					40 39 58 
					59 39 60 
					39 59 61 
					57 39 61 
					41 60 39 
					58 51 40 
					41 62 60 
					62 41 53 
					42 49 63 
					43 42 63 
					43 63 64 
					44 43 64 
					44 64 65 
					65 45 44 
					65 66 45 
					67 45 68 
					69 68 45 
					69 45 66 
					45 67 46 
					67 70 46 
					56 46 70 
					47 71 72 
					48 47 72 
					71 47 50 
					72 49 48 
					72 73 49 
					63 49 73 
					74 71 50 
					74 50 51 
					75 74 51 
					75 51 58 
					76 52 54 
					77 53 52 
					76 77 52 
					53 77 62 
					78 76 54 
					54 55 79 
					54 79 78 
					55 56 80 
					55 80 79 
					70 80 56 
					57 81 82 
					58 57 82 
					57 61 83 
					81 57 83 
					82 75 58 
					61 59 60 
					60 84 85 
					86 60 85 
					86 61 60 
					87 84 60 
					62 87 60 
					88 89 61 
					86 88 61 
					61 89 83 
					90 62 91 
					92 91 62 
					93 92 62 
					62 90 87 
					77 93 62 
					63 73 94 
					64 63 94 
					64 94 95 
					65 64 95 
					65 95 96 
					96 66 65 
					96 97 66 
					98 69 66 
					98 66 97 
					99 100 67 
					99 67 101 
					67 68 69 
					67 69 101 
					67 100 102 
					67 102 70 
					69 103 104 
					69 104 101 
					98 103 69 
					91 70 105 
					70 91 92 
					70 92 106 
					102 105 70 
					70 106 80 
					71 107 108 
					72 71 108 
					107 71 74 
					108 73 72 
					108 109 73 
					94 73 109 
					110 107 74 
					74 75 111 
					110 74 111 
					75 82 112 
					111 75 112 
					76 113 114 
					114 115 76 
					115 93 76 
					77 76 93 
					116 76 78 
					116 117 76 
					76 117 113 
					78 79 116 
					118 119 79 
					79 120 118 
					79 106 120 
					106 79 80 
					79 121 116 
					119 121 79 
					82 81 112 
					81 83 117 
					81 117 112 
					83 113 117 
					89 122 83 
					123 83 122 
					113 83 123 
					84 124 125 
					85 84 126 
					84 127 126 
					87 124 84 
					125 128 84 
					128 127 84 
					85 126 129 
					122 86 85 
					122 85 130 
					130 85 129 
					122 88 86 
					131 87 90 
					87 131 132 
					124 87 132 
					88 133 134 
					89 88 134 
					88 135 133 
					122 135 88 
					89 134 122 
					136 90 137 
					137 90 91 
					136 131 90 
					91 105 137 
					92 138 139 
					140 138 92 
					141 140 92 
					92 93 141 
					139 142 92 
					92 142 143 
					92 143 144 
					144 106 92 
					93 145 141 
					115 145 93 
					94 109 146 
					147 95 94 
					147 94 146 
					148 96 95 
					148 95 147 
					148 97 96 
					121 98 97 
					148 121 97 
					121 119 98 
					98 149 103 
					149 98 150 
					150 98 119 
					151 152 99 
					152 100 99 
					99 101 149 
					153 99 149 
					151 99 153 
					154 155 100 
					152 156 100 
					100 155 102 
					100 157 154 
					100 156 157 
					101 104 149 
					105 102 158 
					159 158 102 
					159 102 155 
					149 160 103 
					160 104 103 
					160 161 104 
					161 162 104 
					104 162 149 
					137 105 163 
					105 158 163 
					144 164 106 
					106 164 120 
					116 107 110 
					116 108 107 
					109 108 116 
					146 109 116 
					116 110 111 
					116 111 112 
					116 112 117 
					123 114 113 
					114 123 129 
					114 129 165 
					166 114 165 
					166 145 114 
					115 114 145 
					147 146 116 
					148 147 116 
					121 148 116 
					119 118 150 
					151 150 118 
					167 151 118 
					167 118 168 
					118 164 168 
					164 118 120 
					134 135 122 
					130 123 122 
					123 130 129 
					125 124 132 
					169 170 125 
					171 169 125 
					132 171 125 
					172 125 170 
					125 172 128 
					126 173 129 
					174 173 126 
					174 126 127 
					165 174 127 
					166 165 127 
					127 128 145 
					166 127 145 
					145 128 141 
					140 128 172 
					141 128 140 
					165 129 173 
					131 175 132 
					136 175 131 
					175 171 132 
					133 135 134 
					176 136 137 
					176 175 136 
					177 176 137 
					137 163 178 
					137 178 177 
					139 138 179 
					138 140 179 
					180 181 139 
					179 180 139 
					182 142 139 
					139 181 183 
					139 183 182 
					140 172 179 
					182 143 142 
					182 184 143 
					184 157 143 
					143 157 144 
					144 157 164 
					149 162 160 
					149 150 153 
					151 153 150 
					151 185 152 
					185 151 167 
					152 185 186 
					156 152 186 
					159 155 154 
					154 187 188 
					154 188 189 
					154 189 159 
					187 154 184 
					157 184 154 
					156 186 167 
					156 167 168 
					164 157 156 
					164 156 168 
					159 190 158 
					158 190 163 
					159 189 190 
					160 162 161 
					163 190 178 
					174 165 173 
					185 167 186 
					170 169 191 
					169 192 193 
					191 169 193 
					169 171 192 
					172 170 180 
					180 170 191 
					171 175 192 
					179 172 180 
					192 175 176 
					193 192 176 
					194 193 176 
					194 176 177 
					177 178 194 
					178 190 195 
					178 195 196 
					178 196 194 
					180 191 181 
					191 197 181 
					181 197 198 
					181 198 183 
					183 184 182 
					183 187 184 
					198 187 183 
					198 188 187 
					196 195 188 
					196 188 198 
					195 189 188 
					195 190 189 
					191 199 197 
					191 193 200 
					199 191 200 
					193 194 201 
					200 193 201 
					202 201 194 
					194 203 202 
					203 194 196 
					204 196 198 
					203 196 204 
					199 205 197 
					197 205 206 
					197 207 208 
					197 208 198 
					206 207 197 
					204 198 208 
					199 200 201 
					209 199 201 
					199 209 205 
					209 201 202 
					210 209 202 
					202 203 211 
					202 211 210 
					203 204 208 
					203 208 211 
					205 209 210 
					206 205 210 
					210 207 206 
					210 211 207 
					207 211 208 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
