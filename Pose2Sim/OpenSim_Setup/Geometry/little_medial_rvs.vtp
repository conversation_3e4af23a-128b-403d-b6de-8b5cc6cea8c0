<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="120" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="236">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.511516 -0.714234  0.477726
			-0.678767 -0.732475 -0.052485
			-0.089696 -0.991704 -0.092072
			-0.566037 -0.531127  0.630481
			-0.383266 -0.466702  0.797055
			-0.688751 -0.667530  0.282887
			 0.023879 -0.387600  0.921518
			-0.290551 -0.775650  0.560310
			-0.578109 -0.740472 -0.342771
			-0.698484 -0.705807 -0.118142
			 0.113149 -0.867220  0.484899
			 0.138219 -0.975902 -0.168854
			 0.815647 -0.573777 -0.074162
			 0.869331 -0.491729 -0.049667
			-0.562953 -0.654210  0.505068
			-0.357926 -0.230154  0.904941
			-0.290450  0.036761  0.956184
			-0.655413 -0.734351 -0.176528
			 0.479208 -0.617168  0.624071
			 0.674297 -0.228445  0.702236
			 0.320857  0.126639  0.938623
			-0.035610  0.068456  0.997018
			 0.767155  0.404018  0.498240
			 0.098508 -0.788695 -0.606842
			-0.683307 -0.585510 -0.436200
			-0.656081 -0.332792 -0.677352
			-0.341480 -0.347931 -0.873118
			 0.912620  0.408647 -0.011483
			 0.996887  0.078655 -0.005492
			 0.713334 -0.476794 -0.513636
			 0.970883  0.011778  0.239264
			 0.967272  0.202583 -0.152791
			-0.473073 -0.803430  0.361529
			-0.291516 -0.635154  0.715260
			-0.199872 -0.442334  0.874295
			 0.029496  0.632561  0.773948
			 0.588317  0.495714  0.638867
			-0.030437  0.812429  0.582265
			-0.367670 -0.253127 -0.894844
			-0.603917 -0.795305 -0.052667
			-0.581022 -0.692301 -0.427940
			-0.315433 -0.393083 -0.863706
			 0.419522  0.723239  0.548568
			 0.819641  0.572155 -0.028767
			 0.576230  0.817243 -0.008573
			 0.211715 -0.018397 -0.977158
			-0.480899 -0.243105 -0.842399
			-0.530583  0.088236 -0.843028
			-0.408884  0.025215 -0.912238
			-0.304993 -0.141117 -0.941842
			 0.776479  0.628003  0.051886
			 0.569984  0.819308  0.062066
			 0.850760  0.032052 -0.524576
			 0.796367  0.398546 -0.454929
			-0.020566 -0.561920  0.826936
			-0.378859 -0.688102  0.618855
			 0.096619 -0.547079  0.831486
			-0.635574 -0.727013  0.259802
			 0.217316 -0.194897  0.956446
			 0.261271  0.929148  0.261576
			 0.536161  0.518585 -0.666033
			 0.825040  0.560584 -0.071091
			 0.411213  0.911058 -0.029608
			 0.735502  0.392446  0.552289
			-0.098500  0.334305 -0.937304
			 0.233610  0.411757 -0.880842
			-0.670658 -0.718930 -0.182641
			-0.628881 -0.486693 -0.606333
			-0.262896 -0.232843 -0.936306
			 0.128183  0.027408 -0.991372
			 0.224877  0.323178 -0.919231
			-0.224376  0.621158 -0.750878
			 0.120111  0.801850 -0.585329
			-0.156508  0.325263 -0.932582
			 0.053099  0.471827 -0.880091
			 0.581992  0.678756 -0.447856
			 0.294485  0.905268 -0.306218
			 0.606225 -0.104411  0.788409
			 0.476707 -0.045370  0.877891
			-0.869332  0.317033  0.379146
			-0.139703 -0.396576  0.907310
			 0.391799 -0.108317  0.913653
			-0.910693 -0.370034  0.183614
			-0.682538  0.663880  0.305622
			 0.633444  0.427172 -0.645192
			 0.677028  0.654919 -0.335731
			 0.713344  0.670086  0.205245
			 0.877328  0.356408  0.321354
			-0.666647  0.743332  0.055126
			-0.924858  0.102297 -0.366297
			-0.735846  0.408824 -0.539809
			-0.823924  0.477764 -0.304780
			 0.020563  0.226311 -0.973838
			 0.313886  0.456446 -0.832546
			 0.713178  0.252754  0.653829
			-0.860045  0.509892  0.018243
			-0.600221  0.584124  0.546383
			-0.956235  0.274165  0.102216
			-0.882773  0.414913  0.220359
			-0.370503  0.135154  0.918945
			-0.470774  0.881305  0.040910
			-0.711895  0.693592  0.110161
			 0.006542  0.172549  0.984979
			 0.203244  0.360204  0.910464
			-0.460729  0.876808  0.137614
			-0.206093  0.875000 -0.438065
			-0.795656  0.605214 -0.025437
			-0.633992  0.732689  0.247429
			 0.463322  0.614436  0.638593
			-0.583051  0.759262  0.289090
			-0.072469  0.903271  0.422907
			-0.398436  0.899811  0.177733
			-0.440562  0.706264 -0.554163
			-0.795438  0.555348  0.242624
			-0.339595  0.692203  0.636812
			-0.944532  0.012440  0.328184
			-0.809071  0.559171  0.180917
			-0.974744  0.017859  0.222611
			-0.913790  0.379899  0.143755
			-0.814231  0.565792  0.130028
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.031547 -0.132250 -0.045774
			 0.030906 -0.131724 -0.046719
			 0.033384 -0.133430 -0.046929
			 0.030091 -0.130211 -0.045145
			 0.030553 -0.130215 -0.044688
			 0.029985 -0.130611 -0.045603
			 0.034343 -0.130799 -0.044425
			 0.033132 -0.133203 -0.045935
			 0.032556 -0.132318 -0.051070
			 0.029923 -0.130544 -0.047012
			 0.033701 -0.133297 -0.045882
			 0.033290 -0.133002 -0.048849
			 0.034730 -0.132038 -0.048507
			 0.035028 -0.132689 -0.046251
			 0.028352 -0.128582 -0.045321
			 0.029851 -0.129287 -0.044698
			 0.030905 -0.129261 -0.044227
			 0.026151 -0.127343 -0.048065
			 0.034661 -0.132731 -0.045510
			 0.034822 -0.131879 -0.044982
			 0.033341 -0.129915 -0.044109
			 0.032012 -0.129361 -0.044005
			 0.034683 -0.130225 -0.044910
			 0.033591 -0.132680 -0.051344
			 0.029788 -0.129707 -0.049626
			 0.030514 -0.130069 -0.050694
			 0.033123 -0.131996 -0.051645
			 0.034600 -0.130472 -0.048188
			 0.035137 -0.130914 -0.049886
			 0.034478 -0.132063 -0.051550
			 0.035219 -0.131331 -0.045492
			 0.034999 -0.130567 -0.046674
			 0.023801 -0.124851 -0.044249
			 0.026191 -0.126102 -0.043915
			 0.028618 -0.128265 -0.044723
			 0.030401 -0.128480 -0.044928
			 0.029354 -0.127576 -0.044609
			 0.031404 -0.128384 -0.044599
			 0.026589 -0.126486 -0.049550
			 0.022455 -0.124133 -0.047608
			 0.021730 -0.123323 -0.048203
			 0.020694 -0.121411 -0.049291
			 0.033310 -0.129100 -0.044366
			 0.034782 -0.130065 -0.046387
			 0.033488 -0.128983 -0.046086
			 0.034338 -0.130663 -0.051907
			 0.029500 -0.128792 -0.050212
			 0.030364 -0.128927 -0.050812
			 0.031157 -0.129392 -0.051284
			 0.031311 -0.130396 -0.051128
			 0.034498 -0.129552 -0.049628
			 0.033194 -0.128941 -0.047808
			 0.034831 -0.130902 -0.051545
			 0.034630 -0.130218 -0.051473
			 0.024440 -0.124325 -0.042895
			 0.020002 -0.121340 -0.040567
			 0.020997 -0.120911 -0.040011
			 0.017999 -0.120751 -0.042263
			 0.027768 -0.126547 -0.044091
			 0.030528 -0.128046 -0.046048
			 0.023217 -0.121677 -0.049451
			 0.023749 -0.121178 -0.044723
			 0.029924 -0.127448 -0.048745
			 0.025445 -0.123350 -0.043461
			 0.029290 -0.127964 -0.050020
			 0.028822 -0.127000 -0.049700
			 0.017661 -0.121020 -0.046200
			 0.018003 -0.119767 -0.048821
			 0.019078 -0.119467 -0.049694
			 0.022052 -0.120962 -0.049501
			 0.033776 -0.129639 -0.051820
			 0.031092 -0.128065 -0.050751
			 0.029792 -0.127710 -0.049828
			 0.031725 -0.128808 -0.051394
			 0.032696 -0.128939 -0.051582
			 0.034277 -0.129547 -0.051377
			 0.032701 -0.128462 -0.051039
			 0.022709 -0.121271 -0.041053
			 0.025142 -0.123962 -0.042868
			 0.018440 -0.119941 -0.040670
			 0.020213 -0.119994 -0.039516
			 0.021345 -0.119317 -0.039583
			 0.016517 -0.119801 -0.044385
			 0.017325 -0.119998 -0.042567
			 0.022064 -0.120211 -0.049293
			 0.021276 -0.117629 -0.048342
			 0.021669 -0.117220 -0.041519
			 0.023495 -0.120827 -0.042094
			 0.016458 -0.119393 -0.046474
			 0.016657 -0.119386 -0.047116
			 0.018130 -0.118333 -0.049431
			 0.017253 -0.119041 -0.048201
			 0.019740 -0.118771 -0.049771
			 0.020539 -0.117974 -0.049287
			 0.022097 -0.118948 -0.040147
			 0.020261 -0.117256 -0.041229
			 0.019863 -0.118184 -0.039927
			 0.020279 -0.117730 -0.043343
			 0.019951 -0.118038 -0.044262
			 0.019631 -0.118983 -0.039364
			 0.018733 -0.119179 -0.044518
			 0.019406 -0.118800 -0.044248
			 0.020476 -0.119261 -0.039364
			 0.020730 -0.118559 -0.039505
			 0.018583 -0.119328 -0.045342
			 0.019499 -0.116860 -0.048670
			 0.019665 -0.116296 -0.047604
			 0.020246 -0.116158 -0.043152
			 0.021377 -0.117952 -0.040138
			 0.020539 -0.116478 -0.041844
			 0.020526 -0.116995 -0.040868
			 0.018057 -0.118925 -0.047232
			 0.018517 -0.117422 -0.049057
			 0.019428 -0.118141 -0.046511
			 0.020226 -0.117217 -0.040365
			 0.020110 -0.117601 -0.043979
			 0.019497 -0.118362 -0.044610
			 0.019880 -0.117122 -0.044489
			 0.019861 -0.118019 -0.044910
			 0.019309 -0.118838 -0.044923
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 0 4 
					0 5 1 
					6 0 7 
					4 0 6 
					2 7 0 
					5 0 3 
					2 1 8 
					8 1 9 
					5 9 1 
					2 10 7 
					11 2 8 
					11 12 2 
					12 13 2 
					13 10 2 
					14 3 15 
					4 15 3 
					5 3 14 
					15 4 16 
					16 4 6 
					17 9 5 
					5 14 17 
					7 10 6 
					10 18 6 
					18 19 6 
					20 21 6 
					21 16 6 
					6 22 20 
					22 6 19 
					8 23 11 
					9 24 8 
					25 8 24 
					26 8 25 
					26 23 8 
					17 24 9 
					13 18 10 
					12 11 23 
					13 12 27 
					12 28 27 
					28 12 29 
					12 23 29 
					30 18 13 
					30 13 31 
					31 13 27 
					32 14 33 
					32 17 14 
					15 34 14 
					34 33 14 
					35 36 15 
					34 15 36 
					16 35 15 
					37 16 21 
					35 16 37 
					24 17 38 
					17 32 39 
					40 17 39 
					38 17 41 
					17 40 41 
					30 19 18 
					30 22 19 
					21 20 42 
					20 22 42 
					21 42 37 
					43 44 22 
					22 30 43 
					42 22 44 
					23 45 29 
					26 45 23 
					38 46 24 
					47 25 24 
					24 46 47 
					48 49 25 
					48 25 47 
					49 26 25 
					49 45 26 
					28 50 27 
					27 50 51 
					43 27 44 
					27 51 44 
					43 31 27 
					52 53 28 
					28 53 50 
					52 28 29 
					52 29 45 
					31 43 30 
					54 32 33 
					55 32 56 
					56 32 54 
					32 55 57 
					39 32 57 
					33 34 58 
					33 58 54 
					36 58 34 
					35 59 36 
					35 37 59 
					36 60 61 
					62 60 36 
					36 59 62 
					36 61 63 
					63 58 36 
					62 37 42 
					62 59 37 
					38 64 46 
					41 65 38 
					38 65 64 
					66 40 39 
					57 66 39 
					67 40 66 
					41 40 67 
					41 67 68 
					41 68 69 
					41 69 60 
					60 65 41 
					42 44 62 
					44 51 62 
					45 49 70 
					70 53 45 
					52 45 53 
					64 47 46 
					71 48 47 
					72 71 47 
					72 47 64 
					48 71 73 
					49 48 73 
					74 49 73 
					70 49 74 
					50 53 75 
					75 76 50 
					76 51 50 
					62 51 76 
					70 75 53 
					56 54 77 
					78 77 54 
					58 78 54 
					79 57 55 
					79 55 80 
					55 56 80 
					80 56 81 
					56 77 81 
					66 57 82 
					57 83 82 
					83 57 79 
					63 78 58 
					84 60 69 
					65 60 62 
					60 84 61 
					61 84 85 
					86 61 85 
					61 87 63 
					86 87 61 
					62 76 72 
					72 65 62 
					77 78 63 
					63 87 77 
					65 72 64 
					66 82 88 
					89 67 66 
					89 66 88 
					90 67 91 
					89 91 67 
					90 68 67 
					68 90 92 
					69 68 92 
					69 93 84 
					69 92 93 
					70 74 75 
					71 76 73 
					71 72 76 
					76 74 73 
					76 75 74 
					94 77 87 
					81 77 94 
					95 79 96 
					79 97 98 
					96 79 99 
					79 80 99 
					100 83 79 
					97 79 95 
					100 79 101 
					98 101 79 
					102 99 80 
					80 81 102 
					102 81 103 
					103 81 94 
					82 83 88 
					83 104 88 
					100 104 83 
					85 84 93 
					105 85 93 
					85 105 106 
					106 107 85 
					85 107 86 
					86 108 94 
					109 86 107 
					94 87 86 
					108 86 110 
					109 110 86 
					88 111 89 
					104 111 88 
					111 91 89 
					92 90 112 
					111 90 91 
					111 113 90 
					113 112 90 
					112 93 92 
					105 93 112 
					103 94 108 
					109 97 95 
					109 95 110 
					114 110 95 
					95 96 114 
					99 102 96 
					102 114 96 
					97 115 98 
					109 107 97 
					115 97 107 
					101 98 116 
					115 117 98 
					118 98 117 
					98 118 116 
					104 100 101 
					101 119 104 
					119 101 116 
					102 103 114 
					103 108 114 
					113 111 104 
					113 104 119 
					106 105 113 
					105 112 113 
					107 106 117 
					106 113 118 
					117 106 118 
					115 107 117 
					110 114 108 
					113 119 116 
					118 113 116 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
