<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="142" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="216">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.469202  0.883091  0.000000
			 0.975185 -0.221390 -0.000010
			-0.364800  0.774137  0.517333
			-0.286954  0.957086 -0.040537
			-0.380316  0.775535 -0.503890
			 0.739541 -0.671361  0.048508
			 0.921031 -0.128934  0.367531
			 0.628199 -0.778053 -0.000015
			 0.921031 -0.128931 -0.367529
			 0.734933 -0.675776 -0.056566
			 0.382507  0.918697  0.098406
			-0.686923  0.612199  0.391599
			-0.877989  0.380914 -0.289895
			-0.505481  0.565132 -0.652009
			-0.108614  0.455939 -0.883359
			-0.048634  0.485582 -0.872837
			-0.102416  0.993772 -0.043923
			-0.016039  0.536521  0.843735
			-0.063438  0.508673  0.858620
			-0.382345  0.422069  0.821992
			-0.869679  0.272202  0.411781
			 0.307899  0.878659 -0.364908
			-0.740512  0.571636 -0.353375
			 0.749913 -0.514820  0.415442
			-0.371725 -0.770167 -0.518328
			-0.843211  0.136741 -0.519901
			 0.003871 -0.999992 -0.000002
			-0.223266 -0.815714  0.533632
			 0.537198 -0.578530 -0.613776
			-0.883032  0.119843  0.453754
			 0.027866  0.852074  0.522679
			 0.399130  0.869939  0.289657
			-0.484054  0.830795 -0.274720
			-0.342034 -0.624354 -0.702278
			 0.094505 -0.563670 -0.820576
			-0.330649 -0.274261 -0.903024
			-0.498332  0.397288 -0.770602
			-0.995969  0.079890  0.040781
			-0.464884  0.350216  0.813162
			-0.330637 -0.274270  0.903026
			 0.160884 -0.581994  0.797119
			-0.077163 -0.712165  0.697759
			-0.551537  0.802467  0.227716
			 0.258161  0.801346 -0.539627
			 0.004434  0.468214 -0.883604
			-0.419862  0.489700  0.764140
			-0.642968 -0.625101 -0.442540
			 0.480919 -0.876277 -0.029239
			-0.658613 -0.157755 -0.735760
			 0.095748 -0.995406 -0.000003
			-0.681247  0.510327 -0.524852
			-0.820660  0.545463 -0.170256
			-0.277248  0.291090 -0.915642
			-0.643279 -0.624911  0.442356
			 0.461643 -0.871721 -0.164282
			-0.721206 -0.190863  0.665907
			-0.735897  0.448202  0.507513
			-0.820662  0.545461  0.170255
			-0.565175  0.745719  0.352819
			-0.404919  0.876339  0.260904
			-0.336653  0.936745  0.095782
			 0.052109  0.926828 -0.371852
			 0.380151  0.863017  0.332696
			-0.472417  0.776532 -0.416917
			 0.472494  0.877530  0.081792
			 0.301276 -0.953537  0.000003
			-0.479099 -0.877761  0.000008
			-0.966281 -0.253593  0.044623
			-0.966280 -0.253598 -0.044617
			-0.409992  0.700325  0.584338
			 0.598981  0.768713  0.224283
			 0.273627  0.921750 -0.274781
			-0.413798  0.908435  0.059306
			-0.070634  0.910892  0.406555
			-0.654122  0.725601 -0.213605
			-0.662227  0.649845 -0.373037
			-0.877201 -0.457072  0.146977
			 0.497059 -0.780823 -0.378481
			 0.291537 -0.680469 -0.672286
			 0.120704 -0.433662 -0.892954
			 0.188352 -0.475881 -0.859105
			 0.176698  0.771110 -0.611692
			 0.160636  0.619286 -0.768558
			 0.740120  0.670972 -0.044940
			 0.745895  0.445838 -0.494843
			-0.026068 -0.592669 -0.805024
			-0.044333 -0.913893 -0.403527
			 0.312392  0.779067 -0.543568
			 0.407033  0.712881 -0.571073
			 0.220249 -0.892401 -0.393841
			 0.380456 -0.802647 -0.459359
			-0.302436  0.387553 -0.870824
			-0.875036 -0.477361 -0.080242
			 0.273361 -0.698263  0.661591
			 0.487985 -0.796319  0.357416
			 0.123034 -0.431126  0.893864
			 0.188411 -0.473993  0.860135
			 0.139881  0.761353  0.633068
			 0.352149  0.544782  0.761054
			 0.771271  0.508164  0.383289
			 0.824445  0.565918 -0.005160
			-0.024745 -0.590495  0.806662
			 0.014486 -0.928562  0.370894
			 0.250287  0.637799  0.728401
			 0.178446  0.862607  0.473356
			 0.057742 -0.803242  0.592847
			-0.231896 -0.836621  0.496276
			-0.236226  0.292461  0.926641
			 0.362790  0.817972  0.446437
			 0.287621  0.920624  0.264056
			 0.384180  0.922558  0.035950
			-0.269166  0.854410  0.444448
			-0.984211 -0.169531  0.050869
			-0.215209  0.648298 -0.730339
			-0.217568  0.576661  0.787481
			 0.062343  0.983607  0.169204
			 0.121827  0.834463 -0.537429
			 0.293847  0.916681 -0.270830
			-0.299034  0.871604 -0.388439
			 0.808102 -0.570007 -0.148538
			 0.584385 -0.808697 -0.067110
			 0.888914  0.426264 -0.167723
			 0.701008  0.509558 -0.498938
			 0.746051  0.492615 -0.448039
			 0.658366  0.339967 -0.671548
			 0.680929  0.433096 -0.590562
			 0.707809  0.573745 -0.412096
			 0.476631  0.172095 -0.862094
			 0.598598  0.333195 -0.728465
			 0.821668 -0.566485  0.062898
			 0.891949  0.407422  0.196045
			 0.705156  0.491782  0.510788
			 0.779607  0.478051  0.404573
			 0.582855  0.372303  0.722268
			 0.370240  0.496828  0.784910
			 0.725381  0.423797  0.542419
			 0.500951  0.250776  0.828347
			 0.454666  0.448534  0.769478
			 0.659710  0.660030 -0.359364
			 0.053471  0.997431  0.047679
			 0.840264  0.541564  0.025769
			 0.689518  0.654825  0.309465
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.009926  0.070241  0.000140
			-0.002164  0.077119  0.000140
			-0.001523  0.077218 -0.003896
			-0.010889  0.070235  0.000140
			-0.001523  0.077218  0.004177
			-0.007261  0.062087 -0.001978
			-0.002405  0.076466 -0.001385
			-0.007262  0.062085  0.000140
			-0.002405  0.076466  0.001665
			-0.007260  0.062087  0.002258
			 0.003862  0.079788 -0.011364
			-0.002485  0.077262 -0.004624
			-0.005137  0.071895 -0.011012
			-0.012932  0.065918 -0.004913
			-0.018704  0.065640 -0.003498
			-0.024271  0.066486 -0.003289
			-0.024084  0.068521  0.000140
			-0.024271  0.066486  0.003569
			-0.018704  0.065640  0.003778
			-0.012932  0.065918  0.005193
			-0.005137  0.071895  0.011293
			 0.003862  0.079788  0.011645
			-0.002485  0.077262  0.004904
			-0.001061  0.066525 -0.009172
			-0.013489  0.061373 -0.006226
			-0.007670  0.064643 -0.011435
			-0.013494  0.061367  0.000140
			-0.013489  0.061373  0.006506
			-0.001061  0.066525  0.009452
			-0.007670  0.064643  0.011716
			 0.007746  0.073911 -0.011438
			 0.006644  0.075548 -0.012801
			 0.001621  0.075795 -0.021334
			-0.016062  0.061648 -0.005362
			-0.022629  0.059626 -0.005214
			-0.026677  0.058314 -0.005077
			-0.030488  0.064064 -0.004023
			-0.029302  0.068234  0.000140
			-0.030488  0.064064  0.004304
			-0.026677  0.058314  0.005357
			-0.022629  0.059626  0.005494
			-0.016062  0.061648  0.005642
			 0.001621  0.075795  0.021614
			 0.006644  0.075548  0.013081
			 0.007746  0.073911  0.011718
			 0.010776  0.067748 -0.010190
			 0.006941  0.062817 -0.009160
			 0.009961  0.073705 -0.019971
			-0.005162  0.065294 -0.021518
			-0.016066  0.061643  0.000140
			-0.001803  0.070917 -0.022931
			-0.006262  0.065164 -0.016221
			 0.010776  0.067748  0.010470
			 0.006941  0.062817  0.009440
			 0.009961  0.073705  0.020251
			-0.005162  0.065294  0.021798
			-0.001803  0.070917  0.023211
			-0.006262  0.065164  0.016501
			 0.013381  0.077579 -0.009222
			 0.013550  0.078983 -0.010242
			 0.010939  0.074487 -0.012255
			 0.012362  0.074696 -0.015519
			 0.008837  0.077967 -0.017592
			 0.004378  0.074078 -0.023228
			 0.011895  0.079819 -0.022017
			-0.022633  0.059621  0.000140
			-0.026681  0.058310  0.000140
			-0.030901  0.060894 -0.003925
			-0.030901  0.060894  0.004205
			 0.004258  0.076528  0.024080
			 0.011895  0.079819  0.022264
			 0.008837  0.077967  0.017872
			 0.010939  0.074487  0.012535
			 0.012362  0.074696  0.015799
			 0.013550  0.078983  0.010523
			 0.013381  0.077579  0.009502
			 0.008112  0.061516 -0.003823
			 0.026593  0.057355 -0.007964
			 0.023738  0.059194 -0.013986
			 0.020149  0.060126 -0.015622
			 0.017478  0.065887 -0.017152
			 0.012266  0.071879 -0.017037
			 0.009770  0.078020 -0.023925
			 0.010151  0.070953 -0.022826
			 0.011286  0.075608 -0.026025
			 0.008658  0.067573 -0.027959
			 0.011823  0.066864 -0.026899
			 0.015339  0.071299 -0.018225
			 0.015649  0.070255 -0.021903
			 0.014787  0.065655 -0.024499
			 0.016989  0.068307 -0.021155
			 0.006300  0.072929 -0.026208
			 0.008112  0.061516  0.004103
			 0.023738  0.059194  0.014267
			 0.026593  0.057355  0.008245
			 0.020149  0.060126  0.015902
			 0.017478  0.065887  0.017432
			 0.012266  0.071879  0.017317
			 0.009770  0.078020  0.024172
			 0.011286  0.075608  0.026271
			 0.010033  0.073375  0.023668
			 0.008658  0.067573  0.028205
			 0.011823  0.066864  0.027164
			 0.015649  0.070255  0.022183
			 0.015339  0.071299  0.018505
			 0.016989  0.068307  0.021436
			 0.014787  0.065655  0.024764
			 0.006300  0.072929  0.026474
			 0.016134  0.081252 -0.010875
			 0.015738  0.076305 -0.004810
			 0.017400  0.080090 -0.013358
			 0.014722  0.076175 -0.002229
			 0.007997  0.060829  0.000140
			 0.016366  0.079412 -0.014648
			 0.016366  0.079412  0.014928
			 0.017400  0.080090  0.013639
			 0.016134  0.081252  0.011156
			 0.015738  0.076305  0.005090
			 0.014722  0.076175  0.002509
			 0.027878  0.057185 -0.002017
			 0.027879  0.057334  0.000140
			 0.028656  0.071200 -0.007018
			 0.025563  0.072889 -0.010930
			 0.023436  0.074652 -0.012437
			 0.019344  0.072499 -0.016098
			 0.017145  0.069631 -0.021709
			 0.013024  0.069869 -0.023513
			 0.011090  0.068389 -0.027407
			 0.014720  0.066942 -0.023683
			 0.027878  0.057185  0.002297
			 0.028656  0.071200  0.007298
			 0.025563  0.072889  0.011210
			 0.023436  0.074652  0.012717
			 0.019344  0.072499  0.016378
			 0.017145  0.069631  0.021989
			 0.013082  0.068672  0.023096
			 0.011090  0.068389  0.027653
			 0.014720  0.066942  0.023949
			 0.019388  0.078675 -0.012525
			 0.017272  0.074726  0.000140
			 0.029534  0.070948  0.000140
			 0.019388  0.078675  0.012805
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					4 1 0 
					0 3 4 
					5 6 1 
					7 5 1 
					1 6 2 
					1 8 9 
					1 9 7 
					4 8 1 
					10 2 6 
					10 11 2 
					2 12 13 3 
					12 2 11 
					3 14 15 16 
					13 14 3 
					16 17 18 3 
					3 18 19 
					3 19 20 4 
					8 4 21 
					4 22 21 
					22 4 20 
					23 6 5 
					23 5 24 25 
					24 5 7 26 
					10 6 23 
					26 7 9 27 
					28 8 21 
					9 8 28 
					29 27 9 28 
					30 31 10 23 
					10 32 12 11 
					10 31 32 
					13 12 24 33 
					12 25 24 
					12 32 25 
					14 13 33 34 
					14 34 35 
					15 14 35 
					16 15 36 37 
					35 36 15 
					37 38 17 16 
					39 18 17 
					17 38 39 
					39 40 18 
					40 41 19 18 
					41 27 20 19 
					22 20 42 21 
					27 29 20 
					29 42 20 
					28 21 43 44 
					42 43 21 
					30 23 45 
					45 23 46 
					47 46 23 
					47 23 25 48 
					49 33 24 26 
					25 32 50 
					25 50 51 
					25 51 48 
					26 27 41 49 
					52 28 44 
					53 28 52 
					28 53 54 
					55 29 28 54 
					56 42 29 
					57 56 29 
					55 57 29 
					58 59 30 45 
					59 60 30 
					60 31 30 
					61 62 31 60 
					62 32 31 
					32 63 50 
					32 62 64 63 
					65 34 33 49 
					66 35 34 65 
					66 37 35 
					67 35 37 
					67 36 35 
					36 67 37 
					39 37 66 
					37 68 38 
					37 39 68 
					39 38 68 
					65 40 39 66 
					49 41 40 65 
					56 69 42 
					69 70 71 42 
					43 42 71 
					72 43 71 73 
					44 43 72 
					52 44 74 75 
					44 72 74 
					58 45 46 
					58 46 76 
					77 76 46 78 
					78 46 79 
					79 46 80 
					46 47 80 
					81 47 62 61 
					47 64 62 
					47 82 64 
					47 83 84 82 
					47 48 85 
					47 85 86 
					83 47 87 88 
					47 81 87 
					89 90 47 
					90 80 47 
					47 86 89 
					51 50 48 
					50 91 48 
					91 85 48 
					63 91 50 
					53 52 75 
					92 53 75 
					93 53 92 94 
					95 53 93 
					96 53 95 
					96 54 53 
					73 71 54 97 
					71 70 54 
					70 98 54 
					98 99 100 54 
					101 55 54 
					102 101 54 
					103 104 54 100 
					104 97 54 
					54 105 106 
					54 96 105 
					106 102 54 
					55 56 57 
					55 107 56 
					55 101 107 
					56 107 69 
					108 58 109 
					110 59 58 108 
					109 58 111 
					111 58 76 112 
					110 113 60 59 
					113 61 60 
					81 61 113 
					82 63 64 
					91 63 82 
					70 69 98 
					98 69 107 
					74 72 114 115 
					72 73 114 
					114 73 97 
					116 75 74 115 
					117 75 116 
					118 75 117 
					112 92 75 118 
					76 77 119 120 112 
					77 121 119 
					78 122 121 77 
					79 122 78 
					80 123 122 79 
					80 124 123 
					80 90 125 124 
					87 81 113 
					82 84 91 
					126 84 83 
					126 83 88 
					84 127 91 
					126 127 84 
					91 127 85 
					86 85 127 
					89 86 127 128 
					125 88 87 124 
					124 87 113 
					128 126 88 125 
					90 89 128 125 
					112 120 129 94 92 
					94 130 131 93 
					93 131 95 
					129 130 94 
					95 131 132 96 
					132 133 96 
					133 134 105 96 
					114 97 104 
					107 99 98 
					100 99 135 
					107 136 99 
					99 136 135 
					103 100 135 
					101 136 107 
					136 101 102 
					137 136 102 106 
					133 104 103 134 
					134 103 135 137 
					114 104 133 
					134 137 106 105 
					123 138 108 109 
					138 110 108 
					123 109 122 
					122 109 121 
					109 139 140 121 
					109 111 139 
					124 113 110 138 
					139 111 112 
					112 118 139 
					141 115 114 133 
					116 115 141 
					117 116 141 132 
					131 117 132 
					130 117 131 
					130 140 139 117 
					139 118 117 
					121 140 120 119 
					129 120 140 130 
					123 124 138 
					127 126 128 
					141 133 132 
					137 135 136 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 40 43 47 50 54 57 61 64 67 70 73 77 81 84 88 91 94 98 102 106 109 113 116 119 123 126 129 133 136 140 143 146 149 153 157 161 164 167 171 174 177 180 183 187 191 194 197 200 204 207 210 213 217 220 223 226 230 233 236 240 243 246 250 254 258 261 264 267 270 273 276 279 282 286 290 293 297 300 304 307 311 314 317 320 324 327 330 333 337 340 343 347 350 353 357 360 363 366 369 372 375 378 381 384 387 391 394 397 400 404 407 410 414 417 420 424 427 430 433 436 439 442 445 448 451 455 458 462 466 469 472 475 478 481 484 488 491 494 498 501 504 508 513 516 520 523 527 530 534 537 540 543 546 549 552 555 558 562 566 569 573 577 582 586 589 592 596 599 603 606 609 612 615 618 621 624 627 631 635 639 642 646 650 653 656 659 663 666 670 673 676 680 683 687 690 693 697 700 704 708 711 714 717 720 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
