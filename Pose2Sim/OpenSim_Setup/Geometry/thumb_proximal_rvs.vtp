<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="143" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="282">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.493507 -0.244079  0.834792
			 0.018597 -0.191201  0.981375
			-0.167888  0.014788  0.985695
			-0.731243 -0.117321  0.671952
			-0.810642 -0.044677  0.583835
			-0.367836 -0.650044  0.664936
			-0.595268 -0.500322  0.628756
			 0.591894 -0.137562  0.794190
			 0.141112 -0.661215  0.736806
			 0.063900  0.363382  0.929446
			 0.312553  0.342153  0.886139
			-0.311736  0.040377  0.949311
			-0.717782 -0.171279  0.674872
			-0.832280 -0.259693  0.489765
			-0.887197 -0.238651  0.394877
			-0.963374 -0.172890  0.204986
			-0.911813 -0.153415  0.380869
			-0.272155 -0.816105  0.509809
			-0.134109 -0.942631  0.305713
			 0.861834 -0.392354  0.321404
			 0.880881  0.031731  0.472273
			 0.804543  0.268093  0.529940
			 0.569930  0.630230  0.527248
			 0.393813  0.576929  0.715587
			 0.312326 -0.933145  0.178027
			 0.607034 -0.787925 -0.103368
			 0.363472  0.489487  0.792648
			 0.432302  0.114740  0.894399
			-0.403442 -0.081218  0.911393
			-0.831500 -0.262973  0.489339
			-0.820622 -0.343135  0.456989
			-0.943020 -0.170491  0.285737
			-0.951389 -0.154126 -0.266654
			-0.895326 -0.047865 -0.442833
			-0.844446 -0.508047  0.169700
			-0.925639 -0.241265  0.291519
			-0.832215 -0.511582 -0.213781
			-0.399659 -0.704292  0.586724
			 0.016681 -0.920525  0.390327
			 0.132579 -0.977088 -0.166498
			 0.296138 -0.953975  0.047260
			 0.351131 -0.915658  0.195645
			 0.949178 -0.126542 -0.288180
			 0.778776 -0.588125 -0.218216
			 0.911575  0.330109 -0.245068
			 0.757763  0.635838  0.146651
			 0.554337  0.801365 -0.224776
			 0.302516  0.947540 -0.103209
			 0.378580  0.909042  0.174128
			 0.527256  0.766883  0.365912
			 0.499123 -0.724355 -0.475590
			 0.931692  0.321579 -0.168929
			 0.377525  0.031073  0.925478
			 0.846046  0.049415  0.530815
			 0.930710  0.101641  0.351351
			-0.366728 -0.290770  0.883721
			 0.232260 -0.196245  0.952651
			-0.759310 -0.212822  0.614943
			-0.908078 -0.386469  0.161357
			-0.869339 -0.378644 -0.317613
			-0.682667 -0.706616  0.186169
			-0.676070  0.271185 -0.685119
			-0.141585  0.256075 -0.956232
			-0.551235 -0.501374 -0.666906
			-0.548270 -0.031408 -0.835711
			-0.202026  0.707897 -0.676806
			-0.560519 -0.733298 -0.384829
			-0.093172 -0.941205 -0.324735
			 0.544242 -0.751680 -0.372530
			 0.524091 -0.795785 -0.303405
			 0.668317 -0.236029 -0.705438
			 0.540536  0.399618 -0.740356
			 0.293064  0.842751 -0.451535
			 0.419975  0.722533 -0.549152
			 0.566788  0.815586 -0.116490
			 0.663651 -0.354669 -0.658618
			 0.455857  0.394456 -0.797871
			 0.372857  0.377026 -0.847838
			 0.497749  0.072203 -0.864311
			 0.870762  0.023363 -0.491150
			 0.896239 -0.145892  0.418892
			 0.985997 -0.040644 -0.161733
			 0.992388 -0.115823  0.041853
			 0.062486 -0.072090  0.995439
			-0.714805 -0.353363  0.603480
			-0.663073  0.065024  0.745725
			-0.304264  0.148939  0.940872
			 0.570341 -0.107058  0.814401
			-0.973266  0.116285  0.198067
			-0.919322 -0.201689 -0.337888
			-0.229076 -0.496945 -0.837000
			-0.877599 -0.072771  0.473841
			-0.141436 -0.228833 -0.963136
			-0.103885  0.655318 -0.748175
			 0.263224  0.380802 -0.886399
			 0.369740  0.586079 -0.720974
			 0.087027 -0.721405 -0.687024
			 0.192243 -0.286472 -0.938604
			 0.059262  0.342268 -0.937732
			 0.510123  0.571494 -0.642782
			 0.439164  0.612412 -0.657334
			 0.672560 -0.290032 -0.680841
			 0.588365  0.315645 -0.744442
			 0.247512  0.699486 -0.670415
			 0.391286  0.737542 -0.550388
			 0.541404 -0.208904 -0.814396
			 0.949564 -0.029190  0.312211
			 0.893240 -0.284192 -0.348363
			 0.830637 -0.230957 -0.506657
			-0.072851  0.586145  0.806924
			 0.432915  0.575707  0.693647
			-0.600195  0.745669  0.289384
			-0.512155  0.762838  0.394683
			-0.369782  0.712072  0.596838
			-0.971219  0.238139 -0.004815
			-0.672721  0.722172  0.160979
			-0.740703  0.633685 -0.223166
			-0.624367  0.306028 -0.718689
			-0.393624 -0.207935 -0.895446
			 0.546531 -0.366079 -0.753187
			 0.309409 -0.297957 -0.903044
			 0.796571  0.542739  0.266288
			 0.999743 -0.005896 -0.021892
			 0.748149  0.297013 -0.593343
			 0.062574  0.751380  0.656896
			 0.290590  0.772691  0.564363
			-0.221421  0.826171  0.518087
			 0.348985  0.936669  0.029332
			-0.346531  0.929173 -0.128660
			-0.050716  0.963155 -0.264122
			-0.388216  0.917891  0.082245
			-0.368775  0.834731 -0.408936
			-0.206717  0.774970 -0.597235
			-0.346740  0.348757 -0.870712
			 0.051526  0.391625 -0.918681
			 0.533584  0.239589 -0.811101
			 0.813337  0.581596 -0.015156
			 0.138955  0.934388 -0.328041
			-0.038665  0.968883  0.244482
			 0.398445  0.754690  0.521233
			-0.216970  0.941358 -0.258397
			-0.209331  0.957478 -0.198536
			 0.121590  0.745531 -0.655286
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.023482 -0.088596  0.057583
			 0.024971 -0.088199  0.058362
			 0.024320 -0.086802  0.058527
			 0.020920 -0.087187  0.056084
			 0.020930 -0.086621  0.055907
			 0.023941 -0.089220  0.057456
			 0.022635 -0.088682  0.056836
			 0.028529 -0.089046  0.056372
			 0.026051 -0.089274  0.058004
			 0.024275 -0.083407  0.057690
			 0.025271 -0.083367  0.057624
			 0.021708 -0.081255  0.057173
			 0.019351 -0.080269  0.055407
			 0.020702 -0.088128  0.055181
			 0.019285 -0.081936  0.054522
			 0.019345 -0.085321  0.052841
			 0.020057 -0.087117  0.054304
			 0.022055 -0.089277  0.055598
			 0.024786 -0.089745  0.056983
			 0.029047 -0.088479  0.056044
			 0.029752 -0.085843  0.055516
			 0.029643 -0.085166  0.055543
			 0.028573 -0.083967  0.055996
			 0.026105 -0.082795  0.056859
			 0.027485 -0.089984  0.055773
			 0.028441 -0.089346  0.055220
			 0.025141 -0.082847  0.057285
			 0.023440 -0.077564  0.057188
			 0.018301 -0.075162  0.055818
			 0.015977 -0.073823  0.054080
			 0.020610 -0.089488  0.054421
			 0.020522 -0.088926  0.054300
			 0.018475 -0.082029  0.051836
			 0.019317 -0.084771  0.050245
			 0.020307 -0.090594  0.052609
			 0.020529 -0.090146  0.053394
			 0.020358 -0.090748  0.051906
			 0.021021 -0.090332  0.054009
			 0.021539 -0.090979  0.053261
			 0.026149 -0.090134  0.054532
			 0.024627 -0.089918  0.053299
			 0.023413 -0.090909  0.052161
			 0.029917 -0.086817  0.054094
			 0.029217 -0.088512  0.054708
			 0.029714 -0.084894  0.054091
			 0.029235 -0.084062  0.054870
			 0.028910 -0.083778  0.054143
			 0.026985 -0.082947  0.054945
			 0.026746 -0.082776  0.056353
			 0.025585 -0.082468  0.056387
			 0.027690 -0.088926  0.053068
			 0.025090 -0.079183  0.055065
			 0.023063 -0.073736  0.056868
			 0.023829 -0.073951  0.056252
			 0.024355 -0.075921  0.055854
			 0.017755 -0.067962  0.056981
			 0.023464 -0.069849  0.057123
			 0.015136 -0.070790  0.053837
			 0.013855 -0.070093  0.051462
			 0.017657 -0.079359  0.051599
			 0.020529 -0.091132  0.052423
			 0.019809 -0.083553  0.050149
			 0.019881 -0.081905  0.050521
			 0.021727 -0.088547  0.048265
			 0.020848 -0.086017  0.047746
			 0.021054 -0.084518  0.048310
			 0.020858 -0.090814  0.051321
			 0.021882 -0.091235  0.050974
			 0.025877 -0.089167  0.051989
			 0.024625 -0.089977  0.050297
			 0.028461 -0.087542  0.052445
			 0.027647 -0.084613  0.052422
			 0.027499 -0.083347  0.053605
			 0.025417 -0.083157  0.053586
			 0.026111 -0.082713  0.055364
			 0.026291 -0.087942  0.051135
			 0.022081 -0.082633  0.050652
			 0.021720 -0.081717  0.050962
			 0.019192 -0.078821  0.050948
			 0.024419 -0.075969  0.054553
			 0.024418 -0.071786  0.055422
			 0.024422 -0.073977  0.054808
			 0.024567 -0.071807  0.054479
			 0.022345 -0.063811  0.058624
			 0.014344 -0.068689  0.053791
			 0.014741 -0.065093  0.056007
			 0.019276 -0.064824  0.058591
			 0.024745 -0.063741  0.058222
			 0.012063 -0.064817  0.051271
			 0.011828 -0.066475  0.047756
			 0.015567 -0.071244  0.048266
			 0.012732 -0.065914  0.053779
			 0.018355 -0.079144  0.050291
			 0.020755 -0.083824  0.049113
			 0.021104 -0.082595  0.050301
			 0.021419 -0.083539  0.050077
			 0.022505 -0.090237  0.049464
			 0.022833 -0.088699  0.048173
			 0.021716 -0.085094  0.047481
			 0.023603 -0.084895  0.048968
			 0.022313 -0.083720  0.050495
			 0.025016 -0.088403  0.049124
			 0.025969 -0.086153  0.050881
			 0.026967 -0.083779  0.052780
			 0.024746 -0.084390  0.051438
			 0.019194 -0.072829  0.049581
			 0.026277 -0.063227  0.056076
			 0.025259 -0.065781  0.052506
			 0.024352 -0.071836  0.053660
			 0.021915 -0.062311  0.058270
			 0.024702 -0.062011  0.058028
			 0.012803 -0.064138  0.052302
			 0.015916 -0.063285  0.055900
			 0.019904 -0.061662  0.056987
			 0.011969 -0.064160  0.048782
			 0.012216 -0.063586  0.048993
			 0.012292 -0.063384  0.047818
			 0.013250 -0.064136  0.046050
			 0.015097 -0.065915  0.044824
			 0.019134 -0.069438  0.048493
			 0.017213 -0.065544  0.044348
			 0.025972 -0.061927  0.055565
			 0.026649 -0.063007  0.053886
			 0.025722 -0.061847  0.050411
			 0.022478 -0.060673  0.056546
			 0.023869 -0.061251  0.056601
			 0.021244 -0.061102  0.056890
			 0.024561 -0.060478  0.053966
			 0.020468 -0.062084  0.051420
			 0.020812 -0.061883  0.047870
			 0.021661 -0.060465  0.055611
			 0.013598 -0.063146  0.046594
			 0.015524 -0.063075  0.045588
			 0.015203 -0.064086  0.044767
			 0.017109 -0.064161  0.044415
			 0.019415 -0.063393  0.046107
			 0.026286 -0.061565  0.053068
			 0.024138 -0.061054  0.050168
			 0.023029 -0.060030  0.055576
			 0.023421 -0.060430  0.056087
			 0.022277 -0.060285  0.054909
			 0.022906 -0.060895  0.053180
			 0.017090 -0.063046  0.045492
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 0 4 
					5 1 0 
					3 6 0 
					5 0 6 
					4 0 2 
					7 2 1 
					8 7 1 
					8 1 5 
					9 2 10 
					10 2 7 
					11 2 9 
					12 4 2 
					12 2 11 
					6 3 13 
					3 4 13 
					12 14 4 
					14 15 4 
					4 15 16 
					16 13 4 
					5 6 17 
					5 18 8 
					17 18 5 
					13 17 6 
					19 20 7 
					21 7 20 
					21 22 7 
					22 23 7 
					23 10 7 
					24 7 8 
					19 7 25 
					7 24 25 
					24 8 18 
					11 9 26 
					10 26 9 
					26 10 23 
					26 27 11 
					28 11 27 
					29 12 11 
					11 28 29 
					12 29 14 
					30 17 13 
					31 13 16 
					13 31 30 
					15 14 32 
					32 14 29 
					15 32 33 
					15 34 35 
					15 35 16 
					33 36 15 
					36 34 15 
					31 16 35 
					17 37 38 
					39 18 17 
					17 30 37 
					40 17 41 
					17 38 41 
					17 40 39 
					39 24 18 
					42 20 19 
					43 42 19 
					43 19 25 
					20 44 21 
					20 42 44 
					45 21 44 
					22 21 45 
					22 46 47 
					22 48 23 
					47 48 22 
					45 46 22 
					23 49 26 
					48 49 23 
					25 24 39 
					43 25 50 
					50 25 39 
					27 26 51 
					49 51 26 
					27 52 28 
					52 27 53 
					51 54 27 
					54 53 27 
					55 28 56 
					52 56 28 
					28 55 57 
					57 29 28 
					58 29 57 
					29 58 59 
					59 32 29 
					60 37 30 
					60 30 35 
					30 31 35 
					61 33 32 
					62 61 32 
					32 59 62 
					63 36 33 
					63 33 64 
					33 65 64 
					65 33 61 
					60 35 34 
					34 36 66 
					60 34 66 
					36 63 66 
					37 60 38 
					60 67 38 
					41 38 67 
					50 39 68 
					39 40 68 
					40 69 68 
					40 41 69 
					69 41 67 
					70 71 42 
					42 43 70 
					44 42 71 
					50 70 43 
					71 46 44 
					45 44 46 
					72 47 46 
					72 46 71 
					72 73 47 
					74 47 73 
					74 48 47 
					48 74 49 
					74 51 49 
					68 75 50 
					70 50 75 
					76 77 51 
					76 51 73 
					51 77 78 
					51 78 79 
					74 73 51 
					51 79 54 
					53 80 52 
					56 52 80 
					81 82 53 
					81 53 54 
					80 53 82 
					81 54 79 
					56 83 55 
					57 55 84 
					84 55 85 
					83 86 55 
					86 85 55 
					56 87 83 
					87 56 80 
					58 57 84 
					58 88 89 
					59 58 90 
					58 84 91 
					91 88 58 
					58 89 90 
					92 62 59 
					90 92 59 
					66 67 60 
					62 93 61 
					93 65 61 
					78 62 92 
					77 76 62 
					76 94 62 
					93 62 95 
					62 94 95 
					77 62 78 
					66 63 67 
					96 63 97 
					97 63 64 
					96 67 63 
					98 97 64 
					65 98 64 
					95 65 93 
					98 65 99 
					65 100 99 
					100 65 95 
					96 69 67 
					69 101 68 
					75 68 101 
					101 69 96 
					102 70 75 
					102 71 70 
					103 71 102 
					103 72 71 
					103 73 72 
					100 76 73 
					104 100 73 
					104 73 103 
					75 101 102 
					76 100 94 
					78 90 105 
					105 79 78 
					92 90 78 
					105 81 79 
					106 87 80 
					80 82 107 
					106 80 107 
					108 81 105 
					108 82 81 
					108 107 82 
					109 86 83 
					110 109 83 
					87 110 83 
					91 84 85 
					91 85 111 
					85 112 111 
					112 85 86 
					113 86 109 
					112 86 113 
					110 87 106 
					114 88 115 
					88 91 111 
					111 115 88 
					88 114 89 
					114 116 89 
					117 89 116 
					118 90 89 
					117 118 89 
					119 105 90 
					118 120 90 
					90 120 119 
					95 94 100 
					101 96 97 
					98 99 97 
					99 101 97 
					99 100 104 
					99 104 102 
					102 101 99 
					104 103 102 
					107 108 105 
					107 105 119 
					106 121 110 
					122 106 107 
					121 106 122 
					119 123 107 
					107 123 122 
					124 109 125 
					109 110 125 
					109 126 113 
					126 109 124 
					127 125 110 
					127 110 121 
					128 111 112 
					115 111 129 
					111 128 129 
					130 112 113 
					112 130 128 
					130 113 126 
					115 116 114 
					116 115 129 
					131 117 116 
					131 116 129 
					132 117 131 
					133 118 117 
					133 117 132 
					133 134 118 
					120 118 134 
					123 119 135 
					120 135 119 
					134 135 120 
					121 136 127 
					121 122 136 
					136 122 123 
					137 123 129 
					123 135 129 
					136 123 127 
					137 127 123 
					138 126 124 
					125 139 124 
					139 138 124 
					127 139 125 
					138 130 126 
					127 138 139 
					140 138 127 
					141 127 137 
					140 127 141 
					130 140 128 
					140 141 128 
					128 141 129 
					129 135 142 
					131 129 132 
					142 132 129 
					129 141 137 
					130 138 140 
					134 133 132 
					134 132 142 
					134 142 135 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
