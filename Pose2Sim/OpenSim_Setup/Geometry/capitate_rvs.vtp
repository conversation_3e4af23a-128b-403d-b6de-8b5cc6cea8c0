<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="90" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="176">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.925442  0.270628 -0.265173
			-0.795084  0.254980 -0.550296
			-0.850679 -0.525458  0.015466
			-0.957385 -0.195340  0.212735
			-0.821271  0.539541 -0.185499
			-0.939578  0.334432  0.073133
			-0.254865 -0.469416 -0.845395
			-0.786748 -0.539937 -0.299158
			-0.615790  0.441827 -0.652374
			 0.135474  0.086726 -0.986978
			-0.204106 -0.927770  0.312385
			-0.103721 -0.993953 -0.036035
			-0.374321 -0.766884  0.521319
			-0.872961 -0.112735  0.474583
			-0.708473  0.685778  0.166655
			-0.658253  0.483144 -0.577300
			-0.951291  0.262146  0.162253
			-0.755394  0.650652 -0.077664
			-0.774129  0.401012  0.489810
			 0.367093 -0.573977 -0.731979
			-0.131130  0.272086 -0.953296
			 0.488331 -0.306142 -0.817196
			 0.247957 -0.193108 -0.949329
			-0.019325 -0.955343  0.294867
			 0.308760 -0.893900 -0.324976
			-0.495147 -0.758038  0.424509
			-0.676226 -0.286344  0.678768
			-0.364806 -0.517208  0.774217
			-0.843011  0.483333  0.236055
			-0.730245  0.426863  0.533413
			-0.789158  0.482735  0.379731
			-0.976620  0.172534 -0.128238
			-0.440370  0.552290 -0.707849
			-0.932443 -0.246849  0.263848
			-0.626070  0.215179  0.749489
			-0.385800  0.502020 -0.774037
			 0.025627 -0.069445 -0.997256
			-0.039770 -0.921917 -0.385339
			 0.491809 -0.395722 -0.775583
			 0.397779 -0.406705 -0.822413
			 0.620143 -0.368548 -0.692527
			-0.222336 -0.734528  0.641120
			-0.339310 -0.920901  0.191862
			-0.420070 -0.086330  0.903376
			-0.310900 -0.254044  0.915862
			-0.017282 -0.190023  0.981628
			-0.905221 -0.116897  0.408546
			-0.866787 -0.130919  0.481187
			-0.687957  0.261092  0.677160
			-0.987206  0.077368  0.139421
			-0.840747  0.485030 -0.240604
			-0.931736  0.178444  0.316268
			-0.285891  0.898515 -0.333071
			-0.048727  0.692554 -0.719719
			-0.112598  0.179653 -0.977265
			 0.402918  0.296681 -0.865816
			 0.638689 -0.275261 -0.718546
			 0.650682 -0.559415 -0.513485
			 0.143615 -0.952240 -0.269468
			 0.817194 -0.254137 -0.517310
			 0.763002 -0.474080 -0.439404
			 0.964964 -0.247507 -0.087090
			 0.628274 -0.776538  0.047544
			 0.650046 -0.371545  0.662868
			-0.098080 -0.250140  0.963229
			-0.048434 -0.095860  0.994216
			 0.770810  0.004089  0.637052
			 0.444365 -0.154277  0.882462
			-0.839916 -0.035947  0.541524
			-0.755592  0.055711  0.652670
			-0.549232  0.341252  0.762818
			-0.361718  0.303742  0.881420
			-0.822303  0.218524  0.525419
			 0.178592 -0.025083  0.983603
			-0.374929  0.922588  0.090881
			-0.368155  0.613703  0.698448
			 0.430273  0.813913 -0.390398
			 0.146747  0.984281 -0.098271
			 0.918267  0.271851 -0.287894
			 0.928388 -0.290593 -0.231628
			 0.988855 -0.109437 -0.100941
			 0.946904 -0.311703  0.078833
			 0.969575  0.003742  0.244765
			 0.532393 -0.218947  0.817692
			 0.816323 -0.208968  0.538469
			-0.494310  0.288851  0.819892
			 0.127301  0.392256  0.911005
			 0.576341  0.248793  0.778417
			 0.092853  0.844924  0.526765
			 0.667814  0.656730  0.350328
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.013661 -0.025863 -0.004057
			-0.011822 -0.023896 -0.008425
			-0.013803 -0.026908 -0.003993
			-0.013905 -0.026050 -0.002741
			-0.012024 -0.022477 -0.003478
			-0.013424 -0.022140 -0.000880
			-0.011910 -0.027010 -0.009027
			-0.012682 -0.027714 -0.006819
			-0.010435 -0.020082 -0.007488
			-0.009268 -0.022702 -0.008323
			-0.011745 -0.027669 -0.002485
			-0.010398 -0.028567 -0.006015
			-0.011697 -0.026265  0.000328
			-0.012616 -0.024168  0.000911
			-0.011637 -0.019667  0.000295
			-0.007323 -0.014970 -0.006505
			-0.006878 -0.014404 -0.004275
			-0.008640 -0.017907 -0.001675
			-0.011125 -0.021380  0.002219
			-0.009081 -0.027998 -0.007584
			-0.004688 -0.014503 -0.006745
			-0.001620 -0.024554 -0.003862
			-0.002948 -0.016417 -0.006924
			-0.005247 -0.027214 -0.002928
			-0.004244 -0.027459 -0.004371
			-0.003405 -0.024908  0.003243
			-0.011494 -0.024014  0.003175
			-0.003557 -0.022498  0.005508
			-0.006393 -0.015370  0.000483
			-0.006390 -0.015810  0.001282
			-0.003838 -0.016383  0.004267
			-0.007103 -0.013523 -0.004834
			-0.004837 -0.012741 -0.005838
			-0.006147 -0.012992  0.000610
			-0.010003 -0.021571  0.004733
			-0.002895 -0.009789 -0.005437
			 0.000477 -0.013368 -0.006868
			-0.002214 -0.027800 -0.002398
			 0.001478 -0.025646 -0.001873
			 0.003186 -0.014688 -0.005971
			 0.003067 -0.016721 -0.004963
			 0.000716 -0.029113  0.006303
			 0.000702 -0.029953  0.004556
			-0.005615 -0.019156  0.006846
			-0.002754 -0.020649  0.006465
			 0.000138 -0.027014  0.007157
			-0.004951 -0.014096  0.002447
			-0.003800 -0.015528  0.004281
			-0.004373 -0.017152  0.007763
			-0.003356 -0.014774  0.005632
			-0.007375 -0.012289 -0.003101
			-0.006894 -0.011255  0.001543
			-0.000168 -0.007025 -0.002638
			 0.002452 -0.008688 -0.006459
			 0.002393 -0.010394 -0.007203
			 0.004049 -0.009966 -0.007045
			 0.005027 -0.012240 -0.006566
			 0.001377 -0.028923  0.000399
			 0.000486 -0.029865  0.001249
			 0.004406 -0.019733 -0.002237
			 0.004847 -0.014447 -0.004353
			 0.005129 -0.016008 -0.001241
			 0.002655 -0.029199  0.003518
			 0.002639 -0.028099  0.005935
			-0.001701 -0.018797  0.007322
			-0.000225 -0.021341  0.007428
			 0.002701 -0.020423  0.006324
			 0.001681 -0.021416  0.006890
			-0.005424 -0.012075  0.004458
			-0.003336 -0.013121  0.006533
			-0.002683 -0.014074  0.007181
			-0.001663 -0.013427  0.007558
			-0.003250 -0.013934  0.006653
			 0.001265 -0.015036  0.008488
			 0.000162 -0.006194  0.000696
			-0.001251 -0.009225  0.005493
			 0.004049 -0.007243 -0.003664
			 0.003330 -0.005896 -0.000086
			 0.006673 -0.010579 -0.003715
			 0.006642 -0.011715 -0.003326
			 0.005578 -0.019518  0.000184
			 0.004435 -0.026331  0.003310
			 0.006810 -0.011927  0.003525
			 0.002095 -0.019074  0.007308
			 0.005528 -0.014984  0.005418
			-0.002479 -0.013306  0.007087
			 0.000932 -0.013540  0.008327
			 0.004651 -0.011557  0.005686
			 0.004036 -0.006438  0.002998
			 0.005030 -0.006711  0.002757
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 0 2 
					0 4 1 
					4 0 5 
					3 5 0 
					6 7 1 
					2 1 7 
					8 1 4 
					6 1 9 
					9 1 8 
					10 2 11 
					7 11 2 
					10 12 2 
					12 3 2 
					13 5 3 
					3 12 13 
					14 4 5 
					8 4 15 
					15 4 16 
					17 16 4 
					17 4 14 
					18 14 5 
					18 5 13 
					7 6 11 
					19 11 6 
					19 6 9 
					9 8 15 
					9 15 20 
					21 19 9 
					9 20 22 
					9 22 21 
					10 11 23 
					10 23 12 
					24 23 11 
					11 19 24 
					12 23 25 
					12 26 13 
					12 27 26 
					25 27 12 
					18 13 26 
					28 17 14 
					29 28 14 
					14 18 30 
					29 14 30 
					16 31 15 
					15 31 32 
					15 32 20 
					17 28 16 
					33 16 28 
					31 16 33 
					34 18 26 
					30 18 34 
					19 21 24 
					35 20 32 
					20 35 36 
					36 22 20 
					21 37 24 
					22 38 21 
					38 37 21 
					39 22 36 
					39 40 22 
					22 40 38 
					23 24 37 
					37 25 23 
					41 27 25 
					42 41 25 
					37 42 25 
					27 34 26 
					43 27 44 
					27 43 34 
					44 27 45 
					41 45 27 
					33 28 46 
					46 28 29 
					47 29 30 
					46 29 47 
					34 43 30 
					48 30 43 
					49 30 48 
					30 49 47 
					31 33 50 
					50 32 31 
					50 35 32 
					33 51 50 
					33 46 51 
					35 52 53 
					50 52 35 
					53 54 35 
					36 35 54 
					54 55 36 
					55 56 36 
					36 56 39 
					37 38 57 
					37 57 58 
					58 42 37 
					59 57 38 
					40 59 38 
					39 60 40 
					60 39 56 
					61 59 40 
					61 40 60 
					42 62 41 
					62 63 41 
					45 41 63 
					58 62 42 
					43 64 48 
					44 64 43 
					64 44 65 
					45 65 44 
					66 45 63 
					66 67 45 
					67 65 45 
					68 51 46 
					47 68 46 
					49 69 47 
					69 68 47 
					70 48 71 
					49 48 72 
					48 70 72 
					48 73 71 
					48 64 73 
					49 72 69 
					74 52 50 
					51 74 50 
					75 74 51 
					75 51 68 
					52 76 53 
					52 74 77 
					52 77 76 
					55 53 76 
					54 53 55 
					56 55 78 
					76 78 55 
					60 56 79 
					79 56 78 
					80 57 59 
					81 57 80 
					81 62 57 
					57 62 58 
					59 61 80 
					79 61 60 
					79 82 61 
					80 61 66 
					83 66 61 
					83 61 84 
					82 84 61 
					62 81 63 
					63 81 66 
					73 64 83 
					83 64 65 
					67 83 65 
					81 80 66 
					66 83 67 
					69 75 68 
					72 85 69 
					85 75 69 
					70 71 85 
					70 85 72 
					86 71 73 
					71 86 75 
					85 71 75 
					73 87 86 
					73 84 87 
					83 84 73 
					77 74 88 
					74 75 88 
					88 75 86 
					76 77 89 
					89 78 76 
					88 89 77 
					89 82 78 
					79 78 82 
					87 82 89 
					84 82 87 
					86 87 89 
					89 88 86 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
