<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="137" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="270">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.010725 -0.512819  0.858429
			 0.145013 -0.243089  0.959103
			-0.200079 -0.374467  0.905397
			-0.469645 -0.416352  0.778515
			-0.525903 -0.626268  0.575512
			-0.181907 -0.939706  0.289590
			 0.541930 -0.544645  0.640058
			 0.481676 -0.874666 -0.054297
			 0.020614 -0.005991  0.999770
			 0.648075  0.457977  0.608487
			 0.897174 -0.015783  0.441394
			-0.235307 -0.204963  0.950064
			-0.827824 -0.560931 -0.008001
			-0.786406 -0.476822  0.392691
			-0.664370 -0.746178  0.042789
			-0.259746 -0.959024 -0.113154
			 0.877742 -0.448693  0.168057
			 0.865042 -0.431563 -0.255844
			 0.535628 -0.839125 -0.094722
			 0.016260 -0.984672 -0.173658
			-0.299317  0.418511  0.857472
			-0.048902  0.652865  0.755894
			 0.316671  0.844111  0.432662
			 0.784476  0.619178  0.034863
			 0.988706  0.073502 -0.130604
			 0.161236  0.692562  0.703108
			-0.279310 -0.331937  0.901001
			 0.184465 -0.141882  0.972544
			-0.890502 -0.451704 -0.054495
			-0.638751 -0.571714  0.514917
			-0.871103 -0.487760  0.057177
			-0.304439 -0.366779  0.879085
			-0.883263 -0.438734 -0.165403
			-0.742702 -0.667698 -0.050722
			-0.470572 -0.866354 -0.167311
			 0.980350 -0.187729 -0.060597
			 0.871958 -0.489573 -0.002747
			 0.581749 -0.794896 -0.172362
			-0.185357 -0.979305 -0.081264
			 0.228689 -0.940958 -0.249599
			 0.503599  0.863926  0.004426
			 0.592825  0.790447  0.154118
			 0.809940  0.585714 -0.030613
			 0.770310  0.260103  0.582210
			 0.211048 -0.159029  0.964453
			-0.134239 -0.463976  0.875618
			-0.648425 -0.561607  0.513948
			-0.798924 -0.549216  0.245117
			-0.813971 -0.560939  0.150993
			-0.880553 -0.448710 -0.152597
			-0.718762 -0.269582 -0.640864
			 0.706866  0.037418  0.706357
			-0.938858 -0.291167 -0.183758
			-0.740167 -0.571461 -0.354380
			-0.838957 -0.268199 -0.473519
			 0.991050  0.133294 -0.007291
			 0.801606 -0.418382 -0.427065
			 0.432910 -0.694637 -0.574515
			-0.245435 -0.800465 -0.546825
			 0.135556 -0.661513 -0.737580
			 0.544095  0.838835  0.017818
			 0.641989  0.766670 -0.008177
			 0.617275  0.423426 -0.663085
			 0.656243  0.729769 -0.191787
			-0.485515 -0.588389  0.646586
			 0.130657 -0.564069  0.815325
			 0.470366 -0.318601  0.822951
			 0.363039 -0.211372  0.907483
			-0.825230 -0.469290  0.314266
			-0.975764 -0.214814  0.041717
			-0.898726 -0.370399 -0.234726
			-0.762057 -0.328440 -0.558029
			-0.441553 -0.015224 -0.897106
			-0.301910 -0.125529 -0.945036
			 0.876176 -0.091949  0.473140
			 0.940338  0.194955 -0.278848
			-0.804008 -0.232787 -0.547157
			-0.910519 -0.176595 -0.373858
			-0.371373 -0.286039 -0.883326
			-0.714922 -0.206154 -0.668122
			-0.704072 -0.125575 -0.698937
			 0.761787  0.199592 -0.616314
			 0.204837 -0.245609 -0.947480
			 0.271640  0.661321 -0.699189
			 0.190717  0.809912 -0.554680
			 0.354833  0.217792 -0.909209
			 0.157179  0.361720 -0.918942
			-0.364708 -0.566470  0.738986
			-0.788806 -0.379673  0.483358
			 0.072600 -0.509010  0.857694
			 0.386742 -0.218700  0.895880
			 0.668323 -0.298358  0.681415
			-0.928623 -0.233540 -0.288302
			-0.833691  0.529107  0.158132
			-0.985747  0.117975 -0.119934
			-0.977564 -0.072447 -0.197787
			-0.892141  0.364578  0.266772
			-0.920264 -0.234939 -0.312918
			-0.909118 -0.159733 -0.384695
			-0.455788 -0.248163 -0.854793
			-0.344293  0.283288 -0.895103
			-0.037318 -0.096764 -0.994607
			 0.975524  0.090969  0.200194
			 0.416409  0.088545 -0.904855
			 0.837800  0.282274 -0.467346
			-0.416865 -0.076093 -0.905778
			-0.291704  0.150400 -0.944610
			 0.174110  0.145493 -0.973919
			-0.700584 -0.150543  0.697509
			-0.310431 -0.297597  0.902812
			-0.111443 -0.029653  0.993328
			 0.005782  0.307202  0.951627
			 0.445879  0.419411  0.790751
			 0.746449  0.002703  0.665437
			-0.733716  0.668196 -0.123186
			-0.927646  0.312259 -0.204862
			-0.194244  0.969122  0.151895
			-0.714223  0.260335  0.649701
			-0.530873  0.769201  0.355673
			-0.708360 -0.004637 -0.705836
			-0.111048 -0.051963 -0.992456
			 0.787804  0.566807  0.241029
			 0.234214  0.509775 -0.827812
			 0.619571  0.687671 -0.378470
			 0.742795  0.666361 -0.064948
			-0.473259  0.083406  0.876966
			-0.386403  0.388286  0.836616
			-0.173413  0.717276  0.674865
			 0.150152  0.877455  0.455551
			-0.194491  0.973297  0.121928
			-0.468979  0.240997 -0.849693
			-0.364471  0.778065 -0.511640
			-0.314240  0.931314  0.184139
			 0.422042  0.906073  0.030198
			-0.132503  0.365550 -0.921312
			 0.012641  0.940852 -0.338581
			 0.385873  0.905718 -0.175435
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.017104 -0.118801 -0.040698
			 0.017964 -0.115112 -0.038993
			 0.015775 -0.116212 -0.040419
			 0.012575 -0.116619 -0.040680
			 0.013766 -0.118370 -0.041032
			 0.015253 -0.119120 -0.041025
			 0.018001 -0.117695 -0.040090
			 0.017521 -0.118845 -0.041714
			 0.016220 -0.113480 -0.038647
			 0.018411 -0.113550 -0.039467
			 0.019205 -0.115383 -0.040075
			 0.012883 -0.113091 -0.039166
			 0.013068 -0.117739 -0.041662
			 0.008726 -0.109109 -0.040509
			 0.013584 -0.118333 -0.041684
			 0.014899 -0.119126 -0.042502
			 0.018898 -0.117153 -0.040767
			 0.018888 -0.116730 -0.042743
			 0.017059 -0.117859 -0.044672
			 0.014928 -0.118401 -0.044815
			 0.013759 -0.112848 -0.038864
			 0.014445 -0.112588 -0.038843
			 0.016335 -0.112182 -0.039608
			 0.018591 -0.113437 -0.041783
			 0.019333 -0.115213 -0.042131
			 0.014551 -0.112054 -0.039768
			 0.009335 -0.106533 -0.038169
			 0.010289 -0.106414 -0.038156
			 0.013067 -0.117364 -0.042349
			 0.002863 -0.097950 -0.037875
			 0.004112 -0.102624 -0.041538
			 0.007264 -0.101823 -0.037071
			 0.012696 -0.116797 -0.044479
			 0.013379 -0.118239 -0.042493
			 0.013915 -0.118081 -0.044722
			 0.018569 -0.115656 -0.044412
			 0.018764 -0.116366 -0.046591
			 0.017117 -0.117938 -0.047375
			 0.014877 -0.118458 -0.045983
			 0.016106 -0.118234 -0.047634
			 0.016820 -0.112096 -0.041441
			 0.015110 -0.111486 -0.041543
			 0.018068 -0.113276 -0.044020
			 0.012172 -0.107777 -0.038878
			 0.010169 -0.103958 -0.037439
			 0.005466 -0.097050 -0.035345
			 0.000493 -0.094776 -0.036094
			 0.002244 -0.098009 -0.038969
			 0.000042 -0.094687 -0.037366
			 0.002044 -0.097902 -0.041162
			 0.006262 -0.104716 -0.044079
			 0.009257 -0.101610 -0.037059
			 0.012585 -0.114317 -0.046657
			 0.013968 -0.117317 -0.047729
			 0.012198 -0.112799 -0.046861
			 0.019136 -0.114417 -0.046403
			 0.018247 -0.116248 -0.048712
			 0.017633 -0.117004 -0.048531
			 0.015166 -0.118092 -0.048143
			 0.016036 -0.117950 -0.048238
			 0.017203 -0.112625 -0.043904
			 0.014819 -0.110719 -0.045573
			 0.009681 -0.102042 -0.045860
			 0.017463 -0.112269 -0.048262
			 0.000515 -0.093337 -0.035177
			 0.003417 -0.091494 -0.032172
			 0.006105 -0.093147 -0.033949
			 0.007212 -0.096587 -0.035468
			-0.000558 -0.093474 -0.036839
			-0.002322 -0.090140 -0.038480
			-0.001579 -0.091279 -0.040130
			-0.000972 -0.090814 -0.042414
			 0.009651 -0.107251 -0.046526
			 0.002327 -0.092522 -0.044097
			 0.007693 -0.091829 -0.035175
			 0.007640 -0.094211 -0.042567
			 0.012643 -0.114400 -0.047224
			 0.012401 -0.113087 -0.047330
			 0.015041 -0.117052 -0.048676
			 0.012135 -0.111749 -0.047295
			 0.012501 -0.111893 -0.047704
			 0.018690 -0.114321 -0.048665
			 0.017713 -0.115804 -0.049269
			 0.013683 -0.110335 -0.047090
			 0.015307 -0.110743 -0.047795
			 0.006777 -0.095011 -0.044629
			 0.016800 -0.112654 -0.048922
			 0.001125 -0.090478 -0.031652
			-0.000546 -0.091485 -0.034050
			 0.003112 -0.090636 -0.031389
			 0.004974 -0.088174 -0.030971
			 0.006875 -0.090026 -0.033016
			-0.002245 -0.090037 -0.039999
			-0.002147 -0.087768 -0.039251
			-0.002037 -0.087042 -0.041164
			-0.002129 -0.088490 -0.040638
			-0.001705 -0.087510 -0.035879
			-0.001739 -0.089451 -0.041148
			-0.001706 -0.089035 -0.042147
			 0.001034 -0.090155 -0.044354
			 0.012451 -0.110961 -0.047712
			 0.004729 -0.091802 -0.044466
			 0.007731 -0.086612 -0.034098
			 0.005655 -0.091526 -0.044355
			 0.006116 -0.085388 -0.043655
			 0.013267 -0.113701 -0.047975
			 0.013594 -0.111910 -0.048380
			 0.017896 -0.114252 -0.049258
			 0.000695 -0.089577 -0.031570
			 0.001712 -0.089581 -0.031011
			 0.002581 -0.088828 -0.030605
			 0.004394 -0.086368 -0.030926
			 0.005942 -0.085599 -0.031620
			 0.006500 -0.087790 -0.031795
			-0.000306 -0.084109 -0.043322
			-0.001534 -0.085783 -0.042384
			 0.005782 -0.083612 -0.034675
			 0.000076 -0.087259 -0.032333
			 0.001109 -0.085663 -0.032572
			-0.001111 -0.087389 -0.043515
			 0.002087 -0.088629 -0.045054
			 0.007129 -0.084844 -0.033918
			 0.003513 -0.083542 -0.044499
			 0.005149 -0.083614 -0.043458
			 0.006630 -0.084295 -0.036356
			 0.001426 -0.088693 -0.031029
			 0.001089 -0.086915 -0.031775
			 0.003836 -0.084749 -0.031965
			 0.005062 -0.084123 -0.032882
			 0.002827 -0.082567 -0.042154
			 0.000230 -0.085293 -0.044427
			 0.001076 -0.083175 -0.043646
			 0.003969 -0.084338 -0.033043
			 0.005016 -0.083089 -0.038220
			 0.002564 -0.083816 -0.044649
			 0.002746 -0.082627 -0.043491
			 0.003949 -0.082713 -0.042545
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					3 4 0 
					4 5 0 
					6 1 0 
					7 0 5 
					6 0 7 
					8 2 1 
					8 1 9 
					1 10 9 
					10 1 6 
					8 11 2 
					11 3 2 
					12 4 3 
					12 3 13 
					13 3 11 
					14 4 12 
					5 4 14 
					15 5 14 
					7 5 15 
					16 10 6 
					16 6 7 
					16 7 17 
					7 18 17 
					18 7 19 
					19 7 15 
					20 8 21 
					22 21 8 
					11 8 20 
					9 22 8 
					9 10 23 
					22 9 23 
					24 23 10 
					10 16 24 
					20 25 11 
					11 26 13 
					26 11 27 
					11 25 27 
					14 12 28 
					12 13 28 
					29 30 13 
					26 31 13 
					29 13 31 
					28 13 32 
					30 32 13 
					28 33 14 
					33 15 14 
					15 33 34 
					34 19 15 
					17 24 16 
					17 18 35 
					24 17 35 
					18 36 35 
					36 18 37 
					18 38 39 
					18 39 37 
					19 38 18 
					19 34 38 
					21 25 20 
					25 21 22 
					22 23 40 
					25 22 41 
					40 41 22 
					23 42 40 
					42 23 24 
					42 24 35 
					43 25 41 
					25 43 27 
					27 44 26 
					31 26 44 
					43 44 27 
					32 33 28 
					45 29 31 
					46 29 45 
					47 30 29 
					48 47 29 
					48 29 46 
					30 49 50 
					30 47 49 
					32 30 50 
					51 45 31 
					31 44 51 
					32 52 53 
					32 53 33 
					32 50 54 
					54 52 32 
					34 33 53 
					34 53 38 
					36 55 35 
					35 55 42 
					36 56 55 
					56 36 37 
					37 39 57 
					56 37 57 
					58 38 53 
					58 39 38 
					59 57 39 
					59 39 58 
					41 40 60 
					42 60 40 
					61 62 41 
					43 41 62 
					61 41 60 
					42 63 60 
					63 42 55 
					51 43 62 
					51 44 43 
					64 45 65 
					64 46 45 
					66 65 45 
					67 66 45 
					67 45 51 
					46 64 68 
					68 48 46 
					48 49 47 
					69 48 68 
					69 49 48 
					69 70 49 
					71 50 49 
					71 49 70 
					50 62 72 
					50 71 73 
					73 62 50 
					54 50 72 
					74 51 75 
					62 75 51 
					67 51 74 
					76 52 77 
					53 52 76 
					54 77 52 
					78 53 76 
					78 58 53 
					54 72 79 
					80 77 54 
					80 54 79 
					55 56 81 
					81 63 55 
					56 82 81 
					56 57 82 
					57 59 82 
					78 59 58 
					78 82 59 
					61 60 63 
					62 61 83 
					61 63 84 
					84 83 61 
					62 73 85 
					62 83 72 
					75 62 85 
					81 86 63 
					84 63 86 
					87 64 65 
					88 68 64 
					64 87 88 
					65 89 87 
					90 89 65 
					90 65 91 
					65 66 91 
					74 66 67 
					91 66 74 
					88 69 68 
					69 92 70 
					69 93 94 
					92 69 95 
					69 94 95 
					93 69 96 
					69 88 96 
					70 97 98 
					70 92 97 
					98 71 70 
					99 71 98 
					73 71 99 
					72 100 79 
					72 83 100 
					73 101 85 
					99 101 73 
					102 74 75 
					91 74 102 
					103 75 85 
					75 103 104 
					75 104 102 
					76 77 80 
					105 78 76 
					105 76 80 
					82 78 105 
					100 80 79 
					80 100 106 
					105 80 106 
					107 86 81 
					107 81 82 
					107 82 105 
					83 84 100 
					84 106 100 
					86 106 84 
					103 85 101 
					107 105 86 
					105 106 86 
					108 88 87 
					109 108 87 
					87 89 109 
					96 88 108 
					109 89 110 
					110 89 90 
					111 90 112 
					90 91 113 
					110 90 111 
					113 112 90 
					91 102 113 
					95 97 92 
					93 114 115 
					115 94 93 
					93 96 116 
					93 116 114 
					94 98 95 
					94 115 98 
					98 97 95 
					96 108 117 
					96 118 116 
					118 96 117 
					98 119 99 
					115 119 98 
					99 120 101 
					99 119 120 
					101 120 103 
					102 104 121 
					102 121 113 
					122 104 103 
					122 103 120 
					122 123 104 
					123 124 104 
					121 104 124 
					125 117 108 
					125 108 109 
					110 125 109 
					125 110 126 
					111 126 110 
					126 111 127 
					111 112 127 
					128 127 112 
					121 112 113 
					112 121 128 
					114 119 115 
					129 114 116 
					130 119 114 
					114 131 130 
					129 131 114 
					118 132 116 
					116 121 133 
					116 128 121 
					116 133 129 
					128 116 132 
					118 117 126 
					126 117 125 
					127 118 126 
					118 127 132 
					119 130 120 
					134 120 130 
					134 122 120 
					124 133 121 
					134 131 122 
					135 122 131 
					135 136 122 
					123 122 136 
					123 136 124 
					133 124 136 
					127 128 132 
					131 129 135 
					129 136 135 
					133 136 129 
					131 134 130 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
