<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.807313 -0.590123  0.000352
			 0.650161 -0.734217  0.195488
			 0.233230 -0.972422  0.000015
			 0.937686  0.146079  0.315287
			 0.999923  0.012366  0.000238
			 0.650262 -0.734194 -0.195240
			 0.937777  0.146172 -0.314974
			 0.617047 -0.606450  0.501469
			 0.345362 -0.569528  0.745897
			-0.331718 -0.943241 -0.016107
			-0.357438 -0.933937  0.000058
			-0.294668 -0.945772  0.136697
			-0.003900 -0.691582  0.722288
			 0.617044 -0.606458 -0.501464
			 0.345362 -0.569533 -0.745893
			-0.331716 -0.943242  0.016088
			-0.294662 -0.945774 -0.136693
			-0.003893 -0.691586 -0.722284
			 0.884436  0.466662  0.000189
			 0.801975  0.471781  0.366415
			 0.563361  0.308026  0.766645
			 0.802080  0.471855 -0.366088
			 0.563361  0.308026 -0.766645
			 0.169726  0.205438  0.963840
			-0.837627 -0.537676  0.096362
			-0.937381 -0.348306  0.000142
			-0.837646 -0.537672 -0.096216
			-0.251723 -0.049201  0.966548
			-0.780079 -0.455266  0.429198
			-0.841107 -0.428036 -0.330643
			 0.169726  0.205438 -0.963840
			-0.780078 -0.455270 -0.429194
			-0.251726 -0.049201 -0.966547
			-0.841105 -0.428036  0.330646
			 0.993853  0.110703  0.000385
			 0.920744 -0.011755  0.389991
			 0.520545  0.328849  0.787966
			 0.920863 -0.011735 -0.389709
			 0.520545  0.328849 -0.787966
			 0.170298  0.167992  0.970967
			-0.965781 -0.151693 -0.210373
			-0.994976 -0.100113 -0.000082
			-0.965778 -0.151683  0.210392
			-0.166903  0.049659  0.984722
			-0.023731 -0.463104  0.885986
			-0.677020 -0.531990 -0.508558
			 0.170298  0.167992 -0.970967
			-0.023730 -0.463107 -0.885985
			-0.166903  0.049657 -0.984722
			-0.677019 -0.531990  0.508558
			 0.999983  0.005885  0.000216
			 0.943425 -0.052395  0.327422
			 0.575892 -0.209157  0.790317
			 0.943481 -0.052377 -0.327260
			 0.575892 -0.209157 -0.790317
			 0.242503 -0.289886  0.925829
			-0.947256 -0.172987 -0.269780
			-0.406221 -0.014523 -0.913659
			-0.984285 -0.176588 -0.000159
			-0.947280 -0.173017  0.269678
			-0.406221 -0.014523  0.913659
			 0.034147 -0.298773  0.953713
			 0.137842  0.026655  0.990096
			-0.085041 -0.974187  0.209110
			-0.070023 -0.640465  0.764789
			 0.050527 -0.032261  0.998201
			 0.649979  0.239645 -0.721178
			 0.242500 -0.289880 -0.925831
			 0.137844  0.026664 -0.990095
			 0.034149 -0.298771 -0.953714
			-0.070023 -0.640465 -0.764789
			-0.085042 -0.974187 -0.209109
			 0.050527 -0.032263 -0.998201
			 0.650156  0.239729  0.720990
			 0.794809  0.606859  0.000184
			 0.646488  0.681711  0.342526
			 0.612227 -0.168951  0.772420
			 0.646527  0.681743 -0.342388
			 0.612224 -0.168952 -0.772423
			 0.207747 -0.317250  0.925307
			-0.542094  0.755210 -0.368499
			-0.246849 -0.037852 -0.968314
			-0.761711  0.647917 -0.000228
			-0.542157  0.755237  0.368352
			-0.246851 -0.037857  0.968314
			-0.065831 -0.222777  0.972644
			-0.206460  0.215460  0.954438
			 0.005934  0.710077  0.704099
			-0.231922 -0.487741  0.841618
			-0.358604 -0.593487  0.720539
			 0.310889 -0.107854  0.944307
			 0.772906 -0.390493  0.500131
			 0.131677  0.283864  0.949780
			-0.514913 -0.583725  0.627798
			 0.968740  0.248077 -0.000288
			 0.902561 -0.327941 -0.278996
			-0.595452  0.803391  0.000039
			-0.402898  0.544134 -0.735928
			 0.207747 -0.317249 -0.925308
			-0.206459  0.215463 -0.954437
			-0.065831 -0.222774 -0.972645
			 0.005937  0.710078 -0.704098
			-0.358604 -0.593488 -0.720538
			-0.231921 -0.487743 -0.841618
			 0.310892 -0.107864 -0.944305
			 0.772906 -0.390493 -0.500131
			-0.514911 -0.583728 -0.627796
			 0.131677  0.283864 -0.949780
			 0.902585 -0.327984  0.278866
			-0.402826  0.544174  0.735939
			-0.083636  0.996496  0.000000
			 0.377190  0.758730  0.531090
			 0.190765  0.522309  0.831145
			 0.377184  0.758737 -0.531085
			 0.190765  0.522305 -0.831148
			-0.171271  0.444705  0.879150
			-0.116151  0.983968 -0.135336
			 0.065257  0.935667  0.346798
			-0.644527  0.537762 -0.543504
			-0.248993  0.890222  0.381454
			 0.065258  0.935667 -0.346797
			-0.116154  0.983968  0.135335
			-0.644527  0.537762  0.543504
			-0.248983  0.890227 -0.381450
			 0.300782  0.626443  0.719097
			 0.410018  0.652654  0.637125
			 0.413234  0.395299  0.820352
			 0.275287 -0.594131  0.755795
			 0.495945  0.301215  0.814437
			-0.852930  0.105212  0.511313
			-0.086909  0.020662  0.996002
			-0.875398 -0.298990  0.379847
			-0.928867  0.112426 -0.352939
			 0.940928 -0.105288  0.321822
			-0.293203  0.288644  0.911437
			 0.103150  0.009262  0.994623
			-0.787018 -0.539574  0.299102
			-0.976359  0.079658 -0.200943
			-0.126439 -0.991974 -0.000141
			 0.116972 -0.913217 -0.390324
			-0.926240  0.376935  0.000239
			-0.718226  0.560280  0.412598
			-0.892237  0.395190  0.218491
			-0.902848  0.366060 -0.225535
			-0.718151  0.560514 -0.412410
			-0.892186  0.395390 -0.218338
			-0.902835  0.366044  0.225613
			-0.766103  0.328197 -0.552606
			-0.171268  0.444705 -0.879150
			 0.300783  0.626443 -0.719096
			 0.410018  0.652654 -0.637125
			-0.086909  0.020662 -0.996002
			-0.852929  0.105209 -0.511315
			 0.275282 -0.594137 -0.755792
			-0.875402 -0.299003 -0.379827
			-0.928868  0.112420  0.352940
			 0.413234  0.395299 -0.820352
			 0.495945  0.301215 -0.814437
			 0.940928 -0.105288 -0.321822
			-0.293203  0.288644 -0.911437
			-0.787018 -0.539574 -0.299102
			 0.103150  0.009262 -0.994623
			-0.976359  0.079658  0.200943
			 0.117111 -0.913218  0.390279
			-0.766103  0.328195  0.552606
			-0.801520  0.596681  0.039211
			-0.614057  0.230783 -0.754767
			-0.801524  0.596676 -0.039213
			-0.614059  0.230793  0.754763
			-0.480394 -0.455367  0.749575
			-0.087280 -0.371997  0.924122
			-0.891971  0.118911  0.436175
			-0.777863 -0.476425  0.409815
			-0.741494 -0.568048  0.357083
			-0.912717  0.327593  0.244195
			 0.234581 -0.908222  0.346560
			-0.122276 -0.950479 -0.285725
			 0.359290 -0.933226 -0.000195
			-0.122383 -0.950518  0.285550
			-0.800393  0.261257  0.539551
			-0.897136  0.441754 -0.000861
			-0.818812  0.089219  0.567087
			-0.800463  0.261243 -0.539454
			-0.818485  0.089847 -0.567458
			-0.912717  0.327593 -0.244195
			-0.777859 -0.476429 -0.409818
			-0.891969  0.118906 -0.436179
			-0.741494 -0.568049 -0.357081
			-0.480394 -0.455367 -0.749575
			-0.087304 -0.372008 -0.924115
			 0.234581 -0.908222 -0.346561
			-0.660093  0.032080  0.750499
			-0.077421 -0.908027  0.411696
			-0.281117 -0.952651 -0.115888
			 0.685905 -0.727691 -0.000001
			-0.281119 -0.952651  0.115884
			-0.077431 -0.908006 -0.411739
			-0.771020  0.636810 -0.001373
			-0.658996  0.031767 -0.751475
			-0.138406  0.211606  0.967506
			-0.015214 -0.046803  0.998788
			 0.617812 -0.654088  0.436436
			 0.237697 -0.971339 -0.000439
			 0.617801 -0.654080 -0.436465
			-0.015214 -0.046803 -0.998788
			-0.835902 -0.004809  0.548858
			-0.964313 -0.264763 -0.000959
			-0.137637  0.210848 -0.967781
			-0.834909 -0.005119 -0.550364
			-0.222706 -0.692668  0.686012
			-0.523027 -0.852316 -0.000881
			-0.222005 -0.692349 -0.686562
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.029504  0.235413  0.000025
			-0.030384  0.235535  0.008122
			-0.044970  0.232280  0.000003
			-0.029735  0.239597  0.009744
			-0.028876  0.237059  0.000026
			-0.030384  0.235535 -0.008122
			-0.029735  0.239597 -0.009744
			-0.035163  0.235119  0.016549
			-0.042697  0.233826  0.020648
			-0.058630  0.228705  0.004884
			-0.058836  0.228943  0.000003
			-0.059093  0.228859  0.014377
			-0.054481  0.231113  0.020759
			-0.035163  0.235119 -0.016549
			-0.042697  0.233826 -0.020648
			-0.058630  0.228705 -0.004884
			-0.059093  0.228859 -0.014377
			-0.054481  0.231113 -0.020759
			-0.030561  0.241236  0.000027
			-0.032142  0.241392  0.010432
			-0.035755  0.239250  0.017582
			-0.032142  0.241392 -0.010432
			-0.035755  0.239250 -0.017582
			-0.044911  0.238213  0.021355
			-0.061243  0.232183  0.005026
			-0.063564  0.230951  0.000003
			-0.061243  0.232183 -0.005026
			-0.056999  0.235843  0.021506
			-0.062520  0.228786  0.016145
			-0.062432  0.233401  0.009553
			-0.044911  0.238213 -0.021355
			-0.062520  0.228786 -0.016145
			-0.056999  0.235843 -0.021506
			-0.062432  0.233401 -0.009553
			-0.031985  0.249701  0.000029
			-0.033279  0.248499  0.010068
			-0.038443  0.241535  0.017143
			-0.033279  0.248499 -0.010068
			-0.038443  0.241535 -0.017143
			-0.045885  0.242135  0.019427
			-0.062087  0.239041  0.006445
			-0.061578  0.239132  0.000005
			-0.062087  0.239041 -0.006445
			-0.055474  0.240088  0.020355
			-0.060679  0.237927  0.019247
			-0.064090  0.239334  0.009857
			-0.045885  0.242135 -0.019427
			-0.060679  0.237927 -0.019247
			-0.055474  0.240088 -0.020355
			-0.064090  0.239334 -0.009857
			-0.031986  0.253393  0.000030
			-0.032569  0.252949  0.011137
			-0.039413  0.248275  0.017802
			-0.032569  0.252949 -0.011137
			-0.039413  0.248275 -0.017802
			-0.044518  0.247800  0.019516
			-0.064753  0.248273  0.006660
			-0.066861  0.248434  0.010307
			-0.064014  0.248458  0.000008
			-0.064753  0.248273 -0.006660
			-0.066861  0.248434 -0.010307
			-0.055048  0.246933  0.020501
			-0.060116  0.247271  0.020900
			-0.066930  0.236794  0.017339
			-0.064391  0.239734  0.021258
			-0.066844  0.246335  0.025419
			-0.073706  0.243277  0.003783
			-0.044518  0.247800 -0.019516
			-0.060116  0.247271 -0.020900
			-0.055048  0.246933 -0.020501
			-0.064391  0.239734 -0.021258
			-0.066930  0.236794 -0.017339
			-0.066844  0.246335 -0.025419
			-0.073706  0.243277 -0.003783
			-0.032748  0.257431  0.000031
			-0.033580  0.257110  0.011341
			-0.037914  0.251962  0.018843
			-0.033580  0.257110 -0.011341
			-0.037914  0.251962 -0.018843
			-0.044200  0.250746  0.021579
			-0.066294  0.254836  0.008490
			-0.066843  0.253170  0.011066
			-0.064450  0.254192  0.000009
			-0.066294  0.254836 -0.008490
			-0.066843  0.253170 -0.011066
			-0.053350  0.251119  0.022619
			-0.061754  0.251150  0.020197
			-0.068449  0.251538  0.020244
			-0.077447  0.246285  0.023464
			-0.075082  0.242633  0.017438
			-0.071382  0.237364  0.025099
			-0.068612  0.234099  0.010687
			-0.071813  0.245857  0.024139
			-0.072723  0.239228  0.021945
			-0.073689  0.238193  0.000004
			-0.071445  0.224377  0.005190
			-0.082781  0.244912  0.000005
			-0.076733  0.248498  0.007831
			-0.044200  0.250746 -0.021579
			-0.061754  0.251150 -0.020197
			-0.053350  0.251119 -0.022619
			-0.068449  0.251538 -0.020244
			-0.075082  0.242633 -0.017438
			-0.077447  0.246285 -0.023464
			-0.071382  0.237364 -0.025098
			-0.068612  0.234099 -0.010687
			-0.072723  0.239228 -0.021945
			-0.071813  0.245857 -0.024139
			-0.071445  0.224377 -0.005190
			-0.076733  0.248498 -0.007831
			-0.048599  0.255811  0.000009
			-0.038755  0.256337  0.019104
			-0.045996  0.255695  0.022621
			-0.038755  0.256337 -0.019104
			-0.045996  0.255695 -0.022621
			-0.055271  0.254247  0.022760
			-0.076651  0.256969  0.016235
			-0.068394  0.253230  0.014311
			-0.076080  0.254545  0.010211
			-0.064263  0.254612  0.017370
			-0.068394  0.253230 -0.014311
			-0.076651  0.256969 -0.016235
			-0.076080  0.254545 -0.010211
			-0.064263  0.254612 -0.017370
			-0.074848  0.250560  0.021546
			-0.074219  0.253504  0.018664
			-0.085032  0.256278  0.029042
			-0.084873  0.248448  0.021401
			-0.078041  0.251633  0.021586
			-0.081470  0.234505  0.012827
			-0.077154  0.236447  0.015703
			-0.091293  0.247175  0.025700
			-0.081030  0.246327  0.012865
			-0.069762  0.235398  0.012174
			-0.080812  0.230871  0.017297
			-0.075814  0.245697  0.026699
			-0.077205  0.243517  0.024348
			-0.079134  0.249803  0.024692
			-0.074819  0.228689  0.000003
			-0.071038  0.223486  0.006801
			-0.088114  0.233386  0.000004
			-0.083376  0.240068  0.005031
			-0.079258  0.239574  0.008422
			-0.079820  0.244991  0.010556
			-0.083376  0.240068 -0.005031
			-0.079258  0.239574 -0.008422
			-0.079820  0.244991 -0.010556
			-0.077912  0.248838  0.011518
			-0.055271  0.254247 -0.022760
			-0.074848  0.250560 -0.021546
			-0.074219  0.253504 -0.018664
			-0.077154  0.236447 -0.015703
			-0.081470  0.234505 -0.012827
			-0.084873  0.248448 -0.021401
			-0.091293  0.247175 -0.025700
			-0.081030  0.246327 -0.012865
			-0.085032  0.256278 -0.029042
			-0.078041  0.251633 -0.021586
			-0.069762  0.235398 -0.012174
			-0.080812  0.230871 -0.017297
			-0.077205  0.243517 -0.024348
			-0.075814  0.245697 -0.026699
			-0.079134  0.249803 -0.024692
			-0.071038  0.223486 -0.006801
			-0.077912  0.248838 -0.011518
			-0.090392  0.252778  0.024342
			-0.082246  0.251513  0.014127
			-0.090392  0.252778 -0.024342
			-0.082246  0.251513 -0.014127
			-0.088344  0.251665  0.032179
			-0.089908  0.249392  0.025005
			-0.083607  0.231682  0.010413
			-0.083263  0.227607  0.009853
			-0.079968  0.224951  0.017050
			-0.081568  0.236385  0.008980
			-0.071972  0.221323  0.013515
			-0.073875  0.226016  0.005408
			-0.077128  0.228743  0.000001
			-0.073875  0.226016 -0.005408
			-0.086809  0.235814  0.004248
			-0.091934  0.227803 -0.000016
			-0.085954  0.231831  0.004132
			-0.086809  0.235814 -0.004248
			-0.085954  0.231831 -0.004132
			-0.081568  0.236385 -0.008980
			-0.083263  0.227607 -0.009853
			-0.083607  0.231682 -0.010413
			-0.079968  0.224951 -0.017050
			-0.088344  0.251665 -0.032179
			-0.089908  0.249392 -0.025005
			-0.071972  0.221323 -0.013515
			-0.085642  0.227960  0.004311
			-0.084401  0.226176  0.005054
			-0.079004  0.225594  0.007751
			-0.083662  0.221783 -0.000000
			-0.079004  0.225594 -0.007751
			-0.084401  0.226176 -0.005054
			-0.097911  0.220185 -0.000021
			-0.085642  0.227960 -0.004311
			-0.089872  0.220871  0.004690
			-0.086315  0.219196  0.004708
			-0.089492  0.212897  0.003656
			-0.091262  0.212117 -0.000003
			-0.089492  0.212897 -0.003656
			-0.086315  0.219196 -0.004708
			-0.098932  0.216901  0.004742
			-0.099654  0.215527 -0.000022
			-0.089872  0.220871 -0.004690
			-0.098932  0.216901 -0.004742
			-0.096702  0.213946  0.005523
			-0.095461  0.212404 -0.000021
			-0.096702  0.213946 -0.005523
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
