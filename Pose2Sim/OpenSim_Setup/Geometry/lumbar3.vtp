<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.174016 -0.984743  0.000000
			 0.561836 -0.800555 -0.208452
			 0.797561 -0.603239  0.000000
			 0.679997 -0.502414 -0.534026
			 0.373165 -0.301553 -0.877390
			-0.836418 -0.548092  0.000000
			-0.611648 -0.790939  0.017372
			-0.833697 -0.475407 -0.280958
			-0.218555 -0.332870 -0.917296
			 0.561836 -0.800555  0.208452
			 0.679997 -0.502414  0.534026
			 0.373165 -0.301553  0.877390
			-0.611648 -0.790939 -0.017372
			-0.833697 -0.475407  0.280958
			-0.218555 -0.332870  0.917296
			 0.963404  0.083495 -0.254719
			 0.999731  0.023183  0.000000
			 0.963404  0.083495  0.254719
			 0.549248  0.399444 -0.734010
			 0.153705  0.355801 -0.921835
			-0.981306 -0.179022  0.070633
			-0.990435 -0.137983  0.000000
			-0.981306 -0.179022 -0.070633
			-0.785566 -0.236771 -0.571686
			-0.404613  0.240113 -0.882403
			-0.959627 -0.255013  0.118679
			 0.549248  0.399444  0.734010
			 0.153705  0.355801  0.921835
			-0.404612  0.240109  0.882404
			-0.785566 -0.236773  0.571685
			-0.959627 -0.255012 -0.118678
			 0.859334  0.330368 -0.390387
			 0.938408  0.345528  0.000000
			 0.859334  0.330368  0.390387
			 0.563921  0.248699 -0.787491
			 0.175421  0.159353 -0.971511
			-0.919529 -0.288892  0.266474
			-0.941538 -0.336908  0.000000
			-0.919529 -0.288893 -0.266471
			-0.001662 -0.602402 -0.798191
			-0.084066 -0.081376 -0.993132
			-0.593585 -0.656961  0.464822
			 0.563921  0.248699  0.787491
			 0.175424  0.159351  0.971511
			-0.084070 -0.081374  0.993132
			-0.001668 -0.602400  0.798192
			-0.593584 -0.656965 -0.464818
			 0.922138  0.061277 -0.381977
			 0.988879  0.148725  0.000000
			 0.922138  0.061277  0.381977
			 0.599968 -0.107209 -0.792808
			 0.272265 -0.212786 -0.938400
			-0.918374 -0.288880  0.270440
			-0.388391 -0.093077  0.916782
			-0.963286 -0.268477  0.000000
			-0.918374 -0.288880 -0.270440
			-0.388391 -0.093083 -0.916781
			 0.141355  0.008922 -0.989919
			 0.073038 -0.267547 -0.960773
			 0.038211 -0.764065 -0.644007
			 0.192127 -0.946223 -0.260287
			 0.620545 -0.313287 -0.718871
			 0.656839  0.321939  0.681849
			 0.599968 -0.107209  0.792808
			 0.272266 -0.212783  0.938400
			 0.073034 -0.267542  0.960774
			 0.141355  0.008922  0.989919
			 0.192128 -0.946223  0.260286
			 0.038211 -0.764065  0.644007
			 0.620545 -0.313287  0.718871
			 0.656840  0.321937 -0.681848
			 0.944690  0.027643 -0.326797
			 0.996829  0.079575  0.000000
			 0.944690  0.027643  0.326797
			 0.628127 -0.094688 -0.772328
			 0.244191 -0.250669 -0.936769
			-0.600005  0.695850  0.394699
			-0.189953 -0.057622  0.980101
			-0.825665  0.564161  0.000000
			-0.600005  0.695850 -0.394699
			-0.189953 -0.057622 -0.980101
			-0.226348  0.167894 -0.959468
			-0.040436 -0.190645 -0.980826
			 0.271179  0.740030 -0.615482
			-0.342411 -0.704756 -0.621348
			-0.579909 -0.446346 -0.681528
			-0.248871 -0.856556 -0.452079
			 0.893550 -0.150443 -0.423008
			 0.057293 -0.992638 -0.106710
			 0.770156  0.186204 -0.610072
			 0.914414  0.021949  0.404185
			 0.884967  0.465655  0.000000
			-0.628171  0.778076  0.000000
			-0.477728  0.395149  0.784623
			 0.628127 -0.094688  0.772328
			 0.244191 -0.250669  0.936769
			-0.040436 -0.190645  0.980826
			-0.226348  0.167894  0.959468
			 0.271179  0.740030  0.615482
			-0.579906 -0.446345  0.681531
			-0.342410 -0.704757  0.621348
			-0.248871 -0.856556  0.452079
			 0.893550 -0.150443  0.423008
			 0.770156  0.186204  0.610072
			 0.057293 -0.992638  0.106710
			 0.914414  0.021949 -0.404185
			-0.477728  0.395149 -0.784623
			 0.584009  0.725993 -0.363136
			 0.755709  0.654907  0.000000
			 0.584009  0.725993  0.363136
			 0.278736  0.786787 -0.550702
			 0.110610  0.536849 -0.836396
			-0.231428  0.421118 -0.876984
			 0.112823  0.912917 -0.392243
			-0.188382  0.961212  0.201457
			-0.671548  0.465912  0.576151
			-0.225048  0.974348  0.000000
			-0.382062  0.827156 -0.412118
			-0.188382  0.961212 -0.201457
			 0.112823  0.912917  0.392243
			-0.671548  0.465912 -0.576151
			-0.382062  0.827156  0.412118
			-0.378507  0.691345 -0.615447
			 0.253790  0.807915 -0.531849
			-0.042378  0.006378 -0.999081
			-0.857277  0.059621 -0.511392
			-0.317427 -0.636368 -0.703048
			-0.569519 -0.551659  0.609361
			-0.934799 -0.039785  0.352942
			-0.207319  0.562073 -0.800683
			-0.391576  0.549165 -0.738300
			 0.971217  0.000315 -0.238197
			 0.013346  0.118431 -0.992873
			-0.657158 -0.729912 -0.188073
			 0.318767 -0.107570 -0.941709
			-0.977121  0.033107  0.210093
			 0.870087 -0.178174  0.459567
			 0.982202 -0.187828  0.000000
			-0.610340  0.482894 -0.627932
			-0.795635  0.605776  0.000000
			-0.911888  0.346820 -0.219489
			-0.956805  0.196196  0.214551
			-0.610340  0.482894  0.627932
			-0.911888  0.346820  0.219489
			-0.956805  0.196196 -0.214551
			-0.763550  0.213427  0.609459
			 0.278736  0.786787  0.550702
			 0.110610  0.536849  0.836396
			-0.231428  0.421118  0.876984
			-0.378507  0.691345  0.615447
			 0.253790  0.807915  0.531849
			-0.207318  0.562075  0.800682
			-0.317428 -0.636365  0.703050
			-0.391576  0.549165  0.738300
			-0.857277  0.059621  0.511392
			-0.042378  0.006378  0.999081
			-0.569519 -0.551659 -0.609361
			-0.934799 -0.039785 -0.352942
			 0.971217  0.000315  0.238197
			 0.013346  0.118431  0.992873
			 0.318767 -0.107570  0.941709
			-0.657158 -0.729912  0.188073
			-0.977121  0.033107 -0.210093
			 0.870087 -0.178174 -0.459567
			-0.763550  0.213427 -0.609459
			-0.767856  0.341497  0.542013
			-0.374000  0.218005  0.901442
			-0.767856  0.341497 -0.542013
			-0.374000  0.218005 -0.901442
			-0.659159 -0.558722 -0.503329
			-0.762428  0.074055 -0.642821
			-0.744464 -0.571656 -0.344939
			-0.890778  0.341693 -0.299600
			-0.644009 -0.014667 -0.764877
			-0.751306 -0.654777 -0.082499
			 0.663802 -0.732477 -0.151145
			 0.540613 -0.702977  0.462126
			 0.902857 -0.429940  0.000000
			 0.540613 -0.702977 -0.462126
			-0.648734  0.331897 -0.684827
			-0.590620  0.268100 -0.761111
			-0.847440  0.530891  0.000000
			-0.648734  0.331897  0.684827
			-0.590620  0.268100  0.761111
			-0.890778  0.341693  0.299600
			-0.644013 -0.014663  0.764874
			-0.751309 -0.654775  0.082497
			-0.762428  0.074055  0.642821
			-0.659159 -0.558722  0.503329
			-0.744464 -0.571656  0.344939
			 0.663802 -0.732477  0.151145
			-0.407611 -0.065070 -0.910834
			-0.259381 -0.954564  0.146726
			 0.006776 -0.724665 -0.689068
			 0.557572 -0.830129  0.000000
			-0.259381 -0.954564 -0.146726
			 0.006777 -0.724666  0.689067
			-0.915922  0.401357 -0.000002
			-0.407609 -0.065067  0.910836
			-0.069197  0.174937 -0.982145
			-0.017087 -0.086456 -0.996109
			 0.256827 -0.573484 -0.777918
			 0.138749 -0.990328  0.000000
			 0.256832 -0.573486  0.777914
			-0.017072 -0.086458  0.996109
			-0.727419 -0.213030 -0.652288
			-0.830380 -0.557198  0.000000
			-0.727421 -0.213032  0.652285
			-0.069191  0.174936  0.982146
			-0.190828 -0.663138 -0.723763
			-0.538043 -0.842917  0.000000
			-0.190831 -0.663141  0.723760
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.001708  0.009111  0.000000
			 0.016093  0.014009 -0.009262
			 0.014420  0.009710  0.000000
			 0.011124  0.013168 -0.018954
			 0.003323  0.010945 -0.023673
			-0.016179  0.007270  0.000000
			-0.016003  0.006914 -0.005562
			-0.016450  0.007200 -0.016477
			-0.011247  0.009679 -0.023811
			 0.016093  0.014009  0.009262
			 0.011124  0.013168  0.018954
			 0.003323  0.010945  0.023673
			-0.016003  0.006914  0.005562
			-0.016450  0.007200  0.016477
			-0.011247  0.009679  0.023811
			 0.016565  0.015332 -0.011124
			 0.016312  0.016029  0.000000
			 0.016565  0.015332  0.011124
			 0.009699  0.016054 -0.018305
			 0.000896  0.016684 -0.022255
			-0.016837  0.012967 -0.005203
			-0.016772  0.012824  0.000000
			-0.016837  0.012967  0.005203
			-0.017220  0.014862 -0.016825
			-0.010353  0.013083 -0.022422
			-0.018186  0.014361 -0.009934
			 0.009699  0.016054  0.018305
			 0.000896  0.016684  0.022255
			-0.010353  0.013083  0.022422
			-0.017220  0.014862  0.016825
			-0.018186  0.014361  0.009934
			 0.012973  0.021389 -0.010828
			 0.014479  0.021262  0.000000
			 0.012973  0.021389  0.010828
			 0.006977  0.021260 -0.017846
			-0.000132  0.021651 -0.020238
			-0.018190  0.020038 -0.006682
			-0.017674  0.020215  0.000000
			-0.018190  0.020038  0.006682
			-0.016613  0.018818 -0.020062
			-0.010673  0.020711 -0.021215
			-0.020300  0.020148 -0.010250
			 0.006977  0.021260  0.017846
			-0.000132  0.021651  0.020238
			-0.010673  0.020711  0.021215
			-0.016613  0.018818  0.020062
			-0.020300  0.020148  0.010250
			 0.011143  0.031077 -0.010444
			 0.012369  0.032768  0.000000
			 0.011143  0.031077  0.010444
			 0.004790  0.029989 -0.018532
			-0.000473  0.028712 -0.020328
			-0.021571  0.026632 -0.006904
			-0.023777  0.026561 -0.010717
			-0.020825  0.026960  0.000000
			-0.021571  0.026632  0.006904
			-0.023777  0.026561  0.010717
			-0.016646  0.025981 -0.021785
			-0.011343  0.026221 -0.021365
			-0.020643  0.020624 -0.022166
			-0.023009  0.016551 -0.018073
			-0.023552  0.023907 -0.026513
			-0.029594  0.020457 -0.003907
			 0.004790  0.029989  0.018532
			-0.000473  0.028712  0.020328
			-0.011343  0.026221  0.021365
			-0.016646  0.025981  0.021785
			-0.023009  0.016551  0.018073
			-0.020643  0.020624  0.022166
			-0.023552  0.023907  0.026513
			-0.029594  0.020457  0.003907
			 0.011463  0.036826 -0.011558
			 0.012021  0.037458  0.000000
			 0.011463  0.036826  0.011558
			 0.006002  0.034871 -0.019617
			-0.000419  0.032499 -0.022482
			-0.023792  0.034768 -0.008814
			-0.024204  0.032581 -0.011508
			-0.021818  0.034186  0.000000
			-0.023792  0.034768  0.008814
			-0.024204  0.032581  0.011508
			-0.018716  0.030693 -0.021049
			-0.009970  0.031765 -0.023575
			-0.025715  0.030302 -0.021103
			-0.030990  0.019551 -0.018180
			-0.033629  0.024046 -0.024478
			-0.027132  0.017880 -0.026187
			-0.024508  0.012901 -0.011124
			-0.028620  0.020163 -0.030642
			-0.027725  0.023896 -0.033706
			-0.027088  0.007718 -0.005383
			-0.029596  0.018759  0.000000
			-0.038997  0.019992  0.000000
			-0.032999  0.026906 -0.008137
			 0.006002  0.034871  0.019617
			-0.000419  0.032499  0.022482
			-0.009970  0.031765  0.023575
			-0.018716  0.030693  0.021049
			-0.025715  0.030302  0.021103
			-0.033629  0.024046  0.024478
			-0.030990  0.019551  0.018180
			-0.027132  0.017880  0.026187
			-0.024508  0.012901  0.011124
			-0.027725  0.023896  0.033706
			-0.028620  0.020163  0.030642
			-0.027088  0.007718  0.005383
			-0.032999  0.026906  0.008137
			 0.010020  0.041978 -0.011770
			 0.010847  0.042487  0.000000
			 0.010020  0.041978  0.011770
			 0.004715  0.040319 -0.019887
			-0.002753  0.038550 -0.023569
			-0.012263  0.035486 -0.023722
			-0.025822  0.032455 -0.014901
			-0.033313  0.037715 -0.016914
			-0.032606  0.034661 -0.010620
			-0.005485  0.038337  0.000000
			-0.021653  0.034759 -0.018094
			-0.033313  0.037715  0.016914
			-0.025822  0.032455  0.014901
			-0.032606  0.034661  0.010620
			-0.021653  0.034759  0.018094
			-0.031120  0.029677 -0.030079
			-0.030608  0.033469 -0.019454
			-0.032857  0.011520 -0.016372
			-0.037272  0.008742 -0.013371
			-0.039517  0.024600 -0.022325
			-0.039809  0.023045 -0.014991
			-0.037229  0.021928 -0.013403
			-0.040801  0.034419 -0.020599
			-0.034505  0.030821 -0.022512
			-0.024321  0.005621 -0.012680
			-0.036409  0.004159 -0.018044
			-0.033241  0.020538 -0.034004
			-0.031891  0.023416 -0.037289
			-0.035554  0.028416 -0.034481
			-0.026359  0.001830 -0.007068
			-0.030558  0.008194  0.000000
			-0.039532  0.015696 -0.005219
			-0.047579  0.009914  0.000000
			-0.035208  0.015355 -0.008762
			-0.035904  0.020307 -0.010989
			-0.039532  0.015696  0.005219
			-0.035208  0.015355  0.008762
			-0.035904  0.020307  0.010989
			-0.034244  0.027259 -0.011991
			 0.004715  0.040319  0.019887
			-0.002753  0.038550  0.023569
			-0.012263  0.035486  0.023722
			-0.031120  0.029677  0.030079
			-0.030608  0.033469  0.019454
			-0.040801  0.034419  0.020599
			-0.039517  0.024600  0.022325
			-0.034505  0.030821  0.022512
			-0.037272  0.008742  0.013371
			-0.032857  0.011520  0.016372
			-0.039809  0.023045  0.014991
			-0.037229  0.021928  0.013403
			-0.024321  0.005621  0.012680
			-0.036409  0.004159  0.018044
			-0.031891  0.023416  0.037289
			-0.033241  0.020538  0.034004
			-0.035554  0.028416  0.034481
			-0.026359  0.001830  0.007068
			-0.034244  0.027259  0.011991
			-0.046231  0.029581 -0.017263
			-0.038745  0.028454 -0.014720
			-0.046231  0.029581  0.017263
			-0.038745  0.028454  0.014720
			-0.038816 -0.000177 -0.010267
			-0.039370  0.004993 -0.010850
			-0.035245 -0.003328 -0.017789
			-0.037467  0.011129 -0.009348
			-0.044035  0.028310 -0.022833
			-0.045564  0.025299 -0.017736
			-0.026578 -0.007195 -0.014090
			-0.028794 -0.001351 -0.005614
			-0.032473  0.001690  0.000000
			-0.028794 -0.001351  0.005614
			-0.042913  0.010034 -0.004406
			-0.045571  0.005065 -0.004289
			-0.056805  0.004591  0.000000
			-0.042913  0.010034  0.004406
			-0.045571  0.005065  0.004289
			-0.037467  0.011129  0.009348
			-0.044035  0.028310  0.022833
			-0.045564  0.025299  0.017736
			-0.039370  0.004993  0.010850
			-0.038816 -0.000177  0.010267
			-0.035245 -0.003328  0.017789
			-0.026578 -0.007195  0.014090
			-0.045060  0.000152 -0.004478
			-0.034276 -0.002449 -0.008068
			-0.039938 -0.002084 -0.005253
			-0.038960 -0.007636  0.000000
			-0.034276 -0.002449  0.008068
			-0.039938 -0.002084  0.005253
			-0.058172 -0.001105  0.000000
			-0.045060  0.000152  0.004478
			-0.048343 -0.004061 -0.004880
			-0.044551 -0.005949 -0.004897
			-0.045305 -0.010498 -0.003802
			-0.047119 -0.011618  0.000000
			-0.045305 -0.010498  0.003802
			-0.044550 -0.005949  0.004897
			-0.055349 -0.006050 -0.004941
			-0.056040 -0.007856  0.000000
			-0.055349 -0.006050  0.004941
			-0.048343 -0.004061  0.004880
			-0.052880 -0.009661 -0.005757
			-0.051514 -0.011544  0.000000
			-0.052880 -0.009661  0.005757
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 3 1 
					0 4 3 
					0 5 6 
					6 7 0 
					0 7 8 
					8 4 0 
					2 9 0 
					9 10 0 
					10 11 0 
					12 5 0 
					0 13 12 
					14 13 0 
					0 11 14 
					2 1 15 
					15 1 3 
					16 2 15 
					17 9 2 
					17 2 16 
					18 15 3 
					18 3 4 
					19 18 4 
					19 4 8 
					20 6 5 
					21 20 5 
					5 12 22 
					5 22 21 
					6 20 7 
					7 23 24 
					8 7 24 
					25 23 7 
					20 25 7 
					24 19 8 
					10 9 17 
					10 17 26 
					11 10 26 
					11 26 27 
					14 11 27 
					13 22 12 
					28 29 13 
					28 13 14 
					13 29 30 
					13 30 22 
					14 27 28 
					15 31 32 
					16 15 32 
					31 15 18 
					32 17 16 
					32 33 17 
					26 17 33 
					34 31 18 
					34 18 19 
					35 34 19 
					35 19 24 
					20 21 36 
					25 20 36 
					37 36 21 
					21 38 37 
					38 21 22 
					38 22 30 
					23 39 40 
					24 23 40 
					41 23 25 
					41 39 23 
					40 35 24 
					41 25 36 
					26 33 42 
					27 26 42 
					27 42 43 
					28 27 43 
					28 43 44 
					44 29 28 
					44 45 29 
					30 29 46 
					29 45 46 
					38 30 46 
					31 47 48 
					32 31 48 
					47 31 34 
					48 33 32 
					48 49 33 
					42 33 49 
					50 47 34 
					50 34 35 
					51 50 35 
					51 35 40 
					52 36 37 
					52 53 36 
					53 41 36 
					54 52 37 
					37 38 55 
					37 55 54 
					38 56 55 
					38 46 56 
					39 57 58 
					40 39 58 
					59 39 60 
					39 59 61 
					57 39 61 
					41 60 39 
					58 51 40 
					41 62 60 
					62 41 53 
					42 49 63 
					43 42 63 
					43 63 64 
					44 43 64 
					44 64 65 
					65 45 44 
					65 66 45 
					67 45 68 
					69 68 45 
					69 45 66 
					45 67 46 
					67 70 46 
					56 46 70 
					47 71 72 
					48 47 72 
					71 47 50 
					72 49 48 
					72 73 49 
					63 49 73 
					74 71 50 
					74 50 51 
					75 74 51 
					75 51 58 
					76 52 54 
					77 53 52 
					76 77 52 
					53 77 62 
					78 76 54 
					54 55 79 
					54 79 78 
					55 56 80 
					55 80 79 
					70 80 56 
					57 81 82 
					58 57 82 
					57 61 83 
					81 57 83 
					82 75 58 
					61 59 60 
					60 84 85 
					86 60 85 
					86 61 60 
					87 84 60 
					62 87 60 
					88 89 61 
					86 88 61 
					61 89 83 
					90 62 91 
					92 91 62 
					93 92 62 
					62 90 87 
					77 93 62 
					63 73 94 
					64 63 94 
					64 94 95 
					65 64 95 
					65 95 96 
					96 66 65 
					96 97 66 
					98 69 66 
					98 66 97 
					99 100 67 
					99 67 101 
					67 68 69 
					67 69 101 
					67 100 102 
					67 102 70 
					69 103 104 
					69 104 101 
					98 103 69 
					91 70 105 
					70 91 92 
					70 92 106 
					102 105 70 
					70 106 80 
					71 107 108 
					72 71 108 
					107 71 74 
					108 73 72 
					108 109 73 
					94 73 109 
					110 107 74 
					74 75 111 
					110 74 111 
					75 82 112 
					111 75 112 
					76 113 114 
					114 115 76 
					115 93 76 
					77 76 93 
					116 76 78 
					116 117 76 
					76 117 113 
					78 79 116 
					118 119 79 
					79 120 118 
					79 106 120 
					106 79 80 
					79 121 116 
					119 121 79 
					82 81 112 
					81 83 117 
					81 117 112 
					83 113 117 
					89 122 83 
					123 83 122 
					113 83 123 
					84 124 125 
					85 84 126 
					84 127 126 
					87 124 84 
					125 128 84 
					128 127 84 
					85 126 129 
					122 86 85 
					122 85 130 
					130 85 129 
					122 88 86 
					131 87 90 
					87 131 132 
					124 87 132 
					88 133 134 
					89 88 134 
					88 135 133 
					122 135 88 
					89 134 122 
					136 90 137 
					137 90 91 
					136 131 90 
					91 105 137 
					92 138 139 
					140 138 92 
					141 140 92 
					92 93 141 
					139 142 92 
					92 142 143 
					92 143 144 
					144 106 92 
					93 145 141 
					115 145 93 
					94 109 146 
					147 95 94 
					147 94 146 
					148 96 95 
					148 95 147 
					148 97 96 
					121 98 97 
					148 121 97 
					121 119 98 
					98 149 103 
					149 98 150 
					150 98 119 
					151 152 99 
					152 100 99 
					99 101 149 
					153 99 149 
					151 99 153 
					154 155 100 
					152 156 100 
					100 155 102 
					100 157 154 
					100 156 157 
					101 104 149 
					105 102 158 
					159 158 102 
					159 102 155 
					149 160 103 
					160 104 103 
					160 161 104 
					161 162 104 
					104 162 149 
					137 105 163 
					105 158 163 
					144 164 106 
					106 164 120 
					116 107 110 
					116 108 107 
					109 108 116 
					146 109 116 
					116 110 111 
					116 111 112 
					116 112 117 
					123 114 113 
					114 123 129 
					114 129 165 
					166 114 165 
					166 145 114 
					115 114 145 
					147 146 116 
					148 147 116 
					121 148 116 
					119 118 150 
					151 150 118 
					167 151 118 
					167 118 168 
					118 164 168 
					164 118 120 
					134 135 122 
					130 123 122 
					123 130 129 
					125 124 132 
					169 170 125 
					171 169 125 
					132 171 125 
					172 125 170 
					125 172 128 
					126 173 129 
					174 173 126 
					174 126 127 
					165 174 127 
					166 165 127 
					127 128 145 
					166 127 145 
					145 128 141 
					140 128 172 
					141 128 140 
					165 129 173 
					131 175 132 
					136 175 131 
					175 171 132 
					133 135 134 
					176 136 137 
					176 175 136 
					177 176 137 
					137 163 178 
					137 178 177 
					139 138 179 
					138 140 179 
					180 181 139 
					179 180 139 
					182 142 139 
					139 181 183 
					139 183 182 
					140 172 179 
					182 143 142 
					182 184 143 
					184 157 143 
					143 157 144 
					144 157 164 
					149 162 160 
					149 150 153 
					151 153 150 
					151 185 152 
					185 151 167 
					152 185 186 
					156 152 186 
					159 155 154 
					154 187 188 
					154 188 189 
					154 189 159 
					187 154 184 
					157 184 154 
					156 186 167 
					156 167 168 
					164 157 156 
					164 156 168 
					159 190 158 
					158 190 163 
					159 189 190 
					160 162 161 
					163 190 178 
					174 165 173 
					185 167 186 
					170 169 191 
					169 192 193 
					191 169 193 
					169 171 192 
					172 170 180 
					180 170 191 
					171 175 192 
					179 172 180 
					192 175 176 
					193 192 176 
					194 193 176 
					194 176 177 
					177 178 194 
					178 190 195 
					178 195 196 
					178 196 194 
					180 191 181 
					191 197 181 
					181 197 198 
					181 198 183 
					183 184 182 
					183 187 184 
					198 187 183 
					198 188 187 
					196 195 188 
					196 188 198 
					195 189 188 
					195 190 189 
					191 199 197 
					191 193 200 
					199 191 200 
					193 194 201 
					200 193 201 
					202 201 194 
					194 203 202 
					203 194 196 
					204 196 198 
					203 196 204 
					199 205 197 
					197 205 206 
					197 207 208 
					197 208 198 
					206 207 197 
					204 198 208 
					199 200 201 
					209 199 201 
					199 209 205 
					209 201 202 
					210 209 202 
					202 203 211 
					202 211 210 
					203 204 208 
					203 208 211 
					205 209 210 
					206 205 210 
					210 207 206 
					210 211 207 
					207 211 208 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
