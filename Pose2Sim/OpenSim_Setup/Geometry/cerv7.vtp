<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.413504 -0.910502  0.000254
			 0.220738 -0.964689  0.143701
			-0.251356 -0.967895  0.000011
			 0.899401 -0.365405  0.239911
			 0.863982 -0.503522  0.000171
			 0.220826 -0.964696 -0.143521
			 0.899484 -0.365352 -0.239680
			 0.286794 -0.868190  0.404963
			 0.078256 -0.716664  0.693015
			-0.569599 -0.821829 -0.012403
			-0.662568 -0.749002 -0.000011
			-0.613143 -0.777629  0.139102
			-0.359658 -0.629030  0.689179
			 0.286789 -0.868193 -0.404960
			 0.078255 -0.716674 -0.693004
			-0.569597 -0.821831  0.012384
			-0.613141 -0.777631 -0.139097
			-0.359657 -0.629034 -0.689176
			 0.998052 -0.062392  0.000140
			 0.957442 -0.007106  0.288540
			 0.735298 -0.048803  0.675985
			 0.957519 -0.007091 -0.288284
			 0.735299 -0.048807 -0.675983
			 0.313178  0.102302  0.944168
			-0.964925 -0.260717 -0.030777
			-0.996994  0.077480 -0.000016
			-0.964924 -0.260718  0.030773
			-0.282200  0.063409  0.957258
			-0.846059 -0.227611  0.482056
			-0.982578 -0.076139 -0.169540
			 0.313182  0.102305 -0.944167
			-0.846060 -0.227614 -0.482052
			-0.282202  0.063422 -0.957256
			-0.982578 -0.076138  0.169538
			 0.917270 -0.398266  0.000297
			 0.826104 -0.464870  0.318509
			 0.712848  0.036458  0.700370
			 0.826181 -0.464892 -0.318277
			 0.712851  0.036457 -0.700368
			 0.301068  0.116045  0.946515
			-0.866108  0.445084 -0.227500
			-0.849459  0.527654 -0.000062
			-0.866118  0.445079  0.227476
			-0.145674  0.079844  0.986105
			-0.496620 -0.414573  0.762560
			-0.980757  0.060992 -0.185461
			 0.301065  0.116051 -0.946516
			-0.496625 -0.414571 -0.762558
			-0.145672  0.079843 -0.986106
			-0.980757  0.060990  0.185463
			 0.879005 -0.476813  0.000172
			 0.815967 -0.512096  0.268247
			 0.474920 -0.498509  0.725217
			 0.816008 -0.512099 -0.268114
			 0.474917 -0.498505 -0.725221
			 0.108799 -0.433275  0.894671
			-0.850272  0.477671 -0.221059
			-0.320001  0.339699 -0.884423
			-0.901438  0.432909 -0.000126
			-0.850297  0.477660  0.220988
			-0.319991  0.339701  0.884426
			-0.123449 -0.352084  0.927791
			 0.245246  0.010416  0.969405
			-0.829025 -0.559178  0.006127
			-0.917104 -0.301168  0.261187
			 0.124101  0.088747  0.988293
			 0.691294 -0.232303 -0.684214
			 0.108795 -0.433273 -0.894672
			 0.245236  0.010405 -0.969408
			-0.123452 -0.352088 -0.927790
			-0.917107 -0.301156 -0.261189
			-0.829028 -0.559173 -0.006128
			 0.124099  0.088745 -0.988293
			 0.691461 -0.232352  0.684027
			 0.985928  0.167170  0.000149
			 0.908882  0.297312  0.292472
			 0.512449 -0.501896  0.696776
			 0.908916  0.297316 -0.292360
			 0.512452 -0.501895 -0.696774
			 0.051291 -0.463078  0.884832
			-0.124796  0.933263 -0.336818
			-0.263225  0.145978 -0.953626
			-0.421626  0.906770 -0.000178
			-0.124823  0.933304  0.336693
			-0.263211  0.145983  0.953629
			-0.233629 -0.126492  0.964063
			-0.210840  0.445774  0.869961
			 0.524369  0.605855  0.598311
			-0.396000 -0.415451  0.818893
			-0.544705 -0.460733  0.700730
			 0.398458 -0.463090  0.791694
			 0.504300 -0.776136  0.378543
			 0.412764  0.201180  0.888343
			-0.780621 -0.161759  0.603709
			 0.802236 -0.597007 -0.000250
			 0.330504 -0.862878 -0.382372
			 0.101720  0.994813  0.000029
			 0.045067  0.759682 -0.648732
			 0.051284 -0.463086 -0.884829
			-0.210844  0.445774 -0.869960
			-0.233635 -0.126493 -0.964061
			 0.524366  0.605857 -0.598313
			-0.544704 -0.460737 -0.700728
			-0.396213 -0.414776 -0.819131
			 0.398458 -0.463090 -0.791694
			 0.504306 -0.776132 -0.378543
			-0.780607 -0.161773 -0.603723
			 0.412764  0.201180 -0.888343
			 0.330481 -0.862919  0.382300
			 0.045177  0.759653  0.648758
			 0.324724  0.945809 -0.000003
			 0.719858  0.490825  0.490810
			 0.446198  0.344975  0.825772
			 0.719857  0.490831 -0.490806
			 0.446203  0.344979 -0.825768
			-0.119056  0.492671  0.862033
			 0.442083  0.880879 -0.169159
			 0.637129  0.727222  0.255373
			-0.217216  0.840868 -0.495740
			 0.086495  0.947219  0.308699
			 0.637129  0.727222 -0.255373
			 0.442193  0.880791  0.169332
			-0.217212  0.840872  0.495735
			 0.086509  0.947220 -0.308692
			 0.666019  0.411001  0.622493
			 0.762168  0.388529  0.517827
			 0.541222  0.378112  0.751072
			 0.003544 -0.620337  0.784327
			 0.581610  0.226211  0.781382
			-0.534370  0.721537  0.440264
			 0.027330  0.210352  0.977244
			-0.825919 -0.247664 -0.506478
			-0.507794  0.829593 -0.232208
			 0.632828 -0.710186  0.308489
			 0.142144  0.263716  0.954070
			 0.061283 -0.090097  0.994046
			-0.934445 -0.047556  0.352919
			-0.821083  0.569766  0.034485
			-0.451748 -0.892146 -0.000117
			-0.522240 -0.795040 -0.308509
			-0.424928  0.905227  0.000187
			-0.021103  0.962271  0.271275
			-0.297288  0.948884  0.106013
			-0.435076  0.880024 -0.190439
			-0.020921  0.962317 -0.271125
			-0.297138  0.948948 -0.105866
			-0.435066  0.880016  0.190498
			-0.460620  0.773505 -0.435339
			-0.119054  0.492680 -0.862028
			 0.666019  0.411001 -0.622493
			 0.761846  0.389201 -0.517797
			 0.027335  0.210353 -0.977243
			-0.534372  0.721537 -0.440262
			 0.002981 -0.619419 -0.785055
			-0.824356 -0.248517  0.508603
			-0.507792  0.829594  0.232210
			 0.540127  0.379782 -0.751018
			 0.581114  0.227475 -0.781384
			 0.632825 -0.710188 -0.308490
			 0.142150  0.263717 -0.954069
			-0.934436 -0.047566 -0.352940
			 0.061298 -0.090103 -0.994044
			-0.821082  0.569767 -0.034503
			-0.522158 -0.795110  0.308467
			-0.460622  0.773506  0.435335
			-0.572715  0.643684 -0.507610
			-0.601733  0.192476 -0.775158
			-0.571909  0.643279  0.509031
			-0.601386  0.191547  0.775658
			-0.595813  0.188050  0.780797
			-0.983916  0.096550 -0.150294
			-0.647830  0.666930  0.368131
			-0.931810  0.096990  0.349747
			-0.908625 -0.155637  0.387529
			-0.393468  0.898379  0.195189
			-0.124688 -0.957494  0.260114
			-0.321324 -0.912751 -0.252265
			 0.306728 -0.951797 -0.000074
			-0.321383 -0.912759  0.252157
			-0.388012  0.816277  0.427946
			-0.537498  0.843262 -0.002381
			-0.628426  0.593196  0.503189
			-0.388051  0.816293 -0.427881
			-0.628024  0.593377 -0.503477
			-0.393468  0.898379 -0.195188
			-0.931810  0.096995 -0.349745
			-0.647832  0.666932 -0.368125
			-0.908625 -0.155643 -0.387526
			-0.595657  0.189443 -0.780580
			-0.983229  0.097451  0.154158
			-0.124689 -0.957494 -0.260111
			-0.582881  0.455123  0.673137
			-0.403556 -0.856760  0.321099
			-0.562932 -0.801416 -0.202087
			 0.221595 -0.975139 -0.000106
			-0.562933 -0.801416  0.202088
			-0.400519 -0.856101 -0.326614
			-0.295823  0.955238 -0.003097
			-0.584252  0.453340 -0.673151
			-0.078175  0.285559  0.955167
			-0.119735  0.000269  0.992806
			 0.189158 -0.872041  0.451402
			-0.261869 -0.965103 -0.000793
			 0.185966 -0.870218 -0.456220
			-0.129447  0.006128 -0.991567
			-0.669389  0.512818  0.537528
			-0.925226  0.379416 -0.000963
			-0.084171  0.287664 -0.954026
			-0.668884  0.511858 -0.539068
			-0.577348 -0.507179  0.639874
			-0.880801 -0.473486 -0.000942
			-0.576133 -0.507564 -0.640664
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.012335 -0.001348  0.000119
			 0.011837 -0.000971  0.008088
			 0.001538  0.001883  0.000097
			 0.013815  0.001419  0.009683
			 0.013362 -0.000503  0.000120
			 0.011837 -0.000971 -0.007899
			 0.013815  0.001419 -0.009495
			 0.008715  0.000381  0.016381
			 0.003546  0.002106  0.020415
			-0.008311  0.004217  0.004901
			-0.008346  0.004440  0.000097
			-0.008538  0.004473  0.014243
			-0.004807  0.004358  0.020524
			 0.008715  0.000381 -0.016193
			 0.003546  0.002106 -0.020227
			-0.008311  0.004217 -0.004712
			-0.008538  0.004473 -0.014054
			-0.004807  0.004358 -0.020336
			 0.013939  0.002752  0.000121
			 0.013020  0.003389  0.010360
			 0.009951  0.003237  0.017397
			 0.013020  0.003389 -0.010172
			 0.009951  0.003237 -0.017209
			 0.003876  0.005676  0.021111
			-0.010494  0.007427  0.005041
			-0.010496  0.007333  0.000097
			-0.010494  0.007427 -0.004853
			-0.004531  0.008251  0.021260
			-0.010690  0.005588  0.015984
			-0.010888  0.008623  0.009496
			 0.003876  0.005676 -0.020922
			-0.010690  0.005588 -0.015795
			-0.004531  0.008251 -0.021071
			-0.010888  0.008623 -0.009307
			 0.016424  0.009176  0.000123
			 0.015079  0.008818  0.010002
			 0.009173  0.005617  0.016965
			 0.015079  0.008818 -0.009814
			 0.009173  0.005617 -0.016777
			 0.004795  0.008526  0.019213
			-0.008602  0.012210  0.006437
			-0.008211  0.012100  0.000099
			-0.008602  0.012210 -0.006248
			-0.003573  0.010693  0.020126
			-0.008016  0.011009  0.019036
			-0.009905  0.013070  0.009795
			 0.004795  0.008526 -0.019025
			-0.008016  0.011009 -0.018847
			-0.003573  0.010693 -0.019938
			-0.009905  0.013070 -0.009607
			 0.017762  0.011599  0.000124
			 0.017191  0.011502  0.011054
			 0.010686  0.010715  0.017613
			 0.017191  0.011502 -0.010866
			 0.010686  0.010715 -0.017425
			 0.006924  0.012103  0.019301
			-0.004978  0.017549  0.006648
			-0.006402  0.018357  0.010238
			-0.004392  0.017425  0.000102
			-0.004978  0.017549 -0.006460
			-0.006402  0.018357 -0.010049
			-0.000793  0.015044  0.020270
			-0.004234  0.016954  0.020663
			-0.009465  0.014766  0.017158
			-0.009971  0.013432  0.021015
			-0.007151  0.016974  0.025110
			-0.013143  0.020035  0.003818
			 0.006924  0.012103 -0.019113
			-0.004234  0.016954 -0.020474
			-0.000793  0.015044 -0.020082
			-0.009971  0.013432 -0.020827
			-0.009465  0.014766 -0.016970
			-0.007151  0.016974 -0.024921
			-0.013143  0.020035 -0.003629
			 0.018689  0.014502  0.000125
			 0.017988  0.014569  0.011255
			 0.013075  0.012635  0.018639
			 0.017988  0.014569 -0.011067
			 0.013075  0.012635 -0.018450
			 0.008216  0.013931  0.021331
			-0.003684  0.022370  0.008450
			-0.004673  0.021460  0.010985
			-0.002621  0.021333  0.000103
			-0.003684  0.022370 -0.008262
			-0.004673  0.021460 -0.010797
			 0.001917  0.017225  0.022355
			-0.001828  0.018439  0.019971
			-0.006394  0.020923  0.020018
			-0.013548  0.022294  0.023186
			-0.014326  0.020271  0.017256
			-0.014581  0.015517  0.024795
			-0.013358  0.014637  0.010612
			-0.009803  0.019998  0.023851
			-0.014348  0.017187  0.021691
			-0.015696  0.017046  0.000098
			-0.022195  0.011151  0.005202
			-0.018905  0.024157  0.000099
			-0.012207  0.023457  0.007801
			 0.008216  0.013931 -0.021143
			-0.001828  0.018439 -0.019782
			 0.001917  0.017225 -0.022166
			-0.006394  0.020923 -0.019829
			-0.014326  0.020271 -0.017067
			-0.013548  0.022294 -0.022998
			-0.014581  0.015517 -0.024606
			-0.013358  0.014637 -0.010424
			-0.014348  0.017187 -0.021503
			-0.009803  0.019998 -0.023662
			-0.022195  0.011151 -0.005013
			-0.012207  0.023457 -0.007613
			 0.006958  0.018721  0.000103
			 0.014069  0.015786  0.018895
			 0.008745  0.017777  0.022357
			 0.014069  0.015786 -0.018707
			 0.008745  0.017777 -0.022169
			 0.001701  0.019918  0.022493
			-0.008915  0.028863  0.016071
			-0.005743  0.022016  0.014178
			-0.009445  0.027103  0.010143
			-0.002337  0.021547  0.017188
			-0.005743  0.022016 -0.013990
			-0.008915  0.028863 -0.015883
			-0.009445  0.027103 -0.009955
			-0.002337  0.021547 -0.017000
			-0.010113  0.024104  0.021299
			-0.008552  0.025767  0.018462
			-0.024017  0.037115  0.032126
			-0.017153  0.024781  0.021156
			-0.011917  0.025938  0.021338
			-0.025463  0.019026  0.012718
			-0.022532  0.017780  0.015548
			-0.022003  0.027460  0.025386
			-0.017129  0.024211  0.012755
			-0.014427  0.013620  0.012075
			-0.026738  0.016599  0.017117
			-0.012640  0.021331  0.026370
			-0.014438  0.020431  0.024056
			-0.013375  0.025156  0.024394
			-0.022416  0.015229  0.000097
			-0.022337  0.010439  0.006787
			-0.030221  0.021452  0.000098
			-0.020855  0.022475  0.005045
			-0.018417  0.020331  0.008383
			-0.016955  0.022873  0.010483
			-0.020855  0.022475 -0.004856
			-0.018417  0.020331 -0.008195
			-0.016955  0.022873 -0.010294
			-0.012896  0.024098  0.011430
			 0.001701  0.019918 -0.022305
			-0.010113  0.024104 -0.021111
			-0.008552  0.025767 -0.018274
			-0.022532  0.017780 -0.015360
			-0.025463  0.019026 -0.012530
			-0.017153  0.024781 -0.020968
			-0.022003  0.027460 -0.025198
			-0.017129  0.024211 -0.012567
			-0.024017  0.037115 -0.031881
			-0.011917  0.025938 -0.021150
			-0.014427  0.013620 -0.011887
			-0.026738  0.016599 -0.016928
			-0.014438  0.020431 -0.023868
			-0.012640  0.021331 -0.026182
			-0.013375  0.025156 -0.024206
			-0.022337  0.010439 -0.006599
			-0.012896  0.024098 -0.011241
			-0.029072  0.036792  0.026947
			-0.015405  0.026376  0.013998
			-0.029072  0.036792 -0.026702
			-0.015405  0.026376 -0.013809
			-0.028076  0.035343  0.035583
			-0.030030  0.034446  0.027677
			-0.028142  0.018368  0.010342
			-0.029823  0.015830  0.009791
			-0.028961  0.012751  0.016874
			-0.024648  0.020169  0.008932
			-0.023925  0.009574  0.013395
			-0.023022  0.013217  0.005417
			-0.025381  0.013649  0.000096
			-0.023022  0.013217 -0.005228
			-0.028256  0.022265  0.004275
			-0.039384  0.017137  0.000078
			-0.029569  0.019543  0.004161
			-0.028256  0.022265 -0.004086
			-0.029569  0.019543 -0.003973
			-0.024648  0.020169 -0.008744
			-0.029823  0.015830 -0.009602
			-0.028142  0.018368 -0.010153
			-0.028961  0.012751 -0.016685
			-0.028076  0.035343 -0.035338
			-0.030030  0.034446 -0.027432
			-0.023925  0.009574 -0.013207
			-0.031176  0.017138  0.004337
			-0.031216  0.015521  0.005068
			-0.028046  0.012680  0.007722
			-0.032794  0.012613  0.000094
			-0.028046  0.012680 -0.007534
			-0.031216  0.015521 -0.004880
			-0.046733  0.013272  0.000120
			-0.031176  0.017138 -0.004149
			-0.042618  0.010352  0.003354
			-0.035693  0.012332  0.004728
			-0.045748  0.006064  0.002646
			-0.046315  0.006136  0.000138
			-0.045748  0.006064 -0.002363
			-0.035693  0.012332 -0.004539
			-0.049557  0.012042  0.003390
			-0.050577  0.011552  0.000127
			-0.042618  0.010352 -0.003071
			-0.049557  0.012042 -0.003107
			-0.049583  0.009302  0.003925
			-0.048760  0.007582  0.000120
			-0.049583  0.009302 -0.003641
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
