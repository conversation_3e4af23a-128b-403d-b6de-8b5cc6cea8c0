<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.056348 -0.998411  0.000000
			 0.368894 -0.905710 -0.208822
			 0.648433 -0.761272  0.000000
			 0.552321 -0.612731 -0.565246
			 0.281054 -0.352190 -0.892732
			-0.933967 -0.357359  0.000000
			-0.770240 -0.637522  0.017194
			-0.915553 -0.287867 -0.280882
			-0.290013 -0.276359 -0.916252
			 0.368894 -0.905710  0.208822
			 0.552321 -0.612731  0.565246
			 0.281054 -0.352190  0.892732
			-0.770240 -0.637522 -0.017194
			-0.915554 -0.287863  0.280884
			-0.290013 -0.276359  0.916252
			 0.963664 -0.051477 -0.262110
			 0.992327 -0.123641  0.000000
			 0.963664 -0.051477  0.262110
			 0.598247  0.328313 -0.730966
			 0.230014  0.315247 -0.920713
			-0.997046  0.029321  0.070987
			-0.997442  0.071475  0.000000
			-0.997046  0.029321 -0.070987
			-0.817157 -0.067491 -0.572451
			-0.338893  0.320130 -0.884686
			-0.991647 -0.049696  0.119026
			 0.598247  0.328313  0.730966
			 0.230014  0.315247  0.920713
			-0.338894  0.320132  0.884684
			-0.817156 -0.067486  0.572452
			-0.991648 -0.049688 -0.119017
			 0.902403  0.172755 -0.394747
			 0.975878  0.218316  0.000000
			 0.902403  0.172755  0.394747
			 0.603301  0.125531 -0.787572
			 0.206279  0.119055 -0.971223
			-0.959410 -0.091010  0.266927
			-0.991079 -0.133275  0.000002
			-0.959411 -0.091007 -0.266924
			-0.139934 -0.587442 -0.797076
			-0.100492 -0.061710 -0.993022
			-0.721962 -0.515990  0.461004
			 0.603301  0.125531  0.787572
			 0.206279  0.119055  0.971223
			-0.100491 -0.061709  0.993022
			-0.139932 -0.587444  0.797075
			-0.721970 -0.515989 -0.460994
			 0.913558 -0.133008 -0.384344
			 0.998162 -0.060594  0.000000
			 0.913558 -0.133008  0.384344
			 0.559445 -0.231047 -0.796014
			 0.215195 -0.265220 -0.939867
			-0.958102 -0.091189  0.271524
			-0.398292 -0.009838  0.917206
			-0.998072 -0.062075  0.000005
			-0.958101 -0.091189 -0.271527
			-0.398289 -0.009836 -0.917207
			 0.138972 -0.021462 -0.990064
			 0.009220 -0.276620 -0.960935
			-0.138246 -0.753158 -0.643150
			-0.027796 -0.965171 -0.260141
			 0.532971 -0.440059 -0.722697
			 0.710543  0.176616  0.681128
			 0.559445 -0.231047  0.796014
			 0.215195 -0.265220  0.939867
			 0.009221 -0.276621  0.960935
			 0.138973 -0.021461  0.990063
			-0.027797 -0.965171  0.260141
			-0.138245 -0.753156  0.643151
			 0.532968 -0.440058  0.722699
			 0.710542  0.176617 -0.681128
			 0.938628 -0.037474 -0.342889
			 0.999288 -0.037719  0.000000
			 0.938628 -0.037474  0.342889
			 0.606107 -0.193813 -0.771408
			 0.179264 -0.296297 -0.938122
			-0.428085  0.810492  0.399806
			-0.198152 -0.019080  0.979986
			-0.681299  0.732005  0.000000
			-0.428087  0.810491 -0.399807
			-0.198146 -0.019083 -0.979987
			-0.181025  0.211328 -0.960505
			-0.082918 -0.177759 -0.980575
			 0.428269  0.663950 -0.612990
			-0.489930 -0.612603 -0.620231
			-0.662404 -0.312338 -0.680930
			-0.427948 -0.782475 -0.452320
			 0.840972 -0.334894 -0.424985
			-0.166820 -0.979782 -0.110442
			 0.790326  0.016629 -0.612460
			 0.897232 -0.169965  0.407537
			 0.963756  0.266786  0.000000
			-0.437750  0.899097  0.000000
			-0.378561  0.487685  0.786674
			 0.606107 -0.193813  0.771408
			 0.179264 -0.296297  0.938122
			-0.082918 -0.177759  0.980575
			-0.181025  0.211328  0.960505
			 0.428267  0.663951  0.612989
			-0.662404 -0.312338  0.680930
			-0.489930 -0.612603  0.620231
			-0.427951 -0.782475  0.452318
			 0.840972 -0.334894  0.424985
			 0.790324  0.016632  0.612464
			-0.166824 -0.979781  0.110444
			 0.897232 -0.169965 -0.407537
			-0.378561  0.487685 -0.786674
			 0.675603  0.671658 -0.304034
			 0.757366  0.652991  0.000000
			 0.675603  0.671658  0.304034
			 0.479174  0.715137 -0.508892
			 0.229880  0.501022 -0.834346
			-0.127814  0.460662 -0.878325
			 0.312485  0.866258 -0.389807
			 0.033342  0.978231  0.204823
			-0.551492  0.599245  0.580313
			 0.007170  0.999974  0.000000
			-0.181869  0.891923 -0.414001
			 0.033342  0.978231 -0.204823
			 0.312485  0.866258  0.389807
			-0.551492  0.599245 -0.580313
			-0.181869  0.891923  0.414001
			-0.215403  0.756877 -0.617041
			 0.427651  0.732908 -0.529113
			-0.040058  0.015927 -0.999070
			-0.823302  0.239078 -0.514796
			-0.450757 -0.551670 -0.701768
			-0.674576 -0.415630  0.610081
			-0.920568  0.159454  0.356552
			-0.073681  0.594481 -0.800727
			-0.256203  0.620631 -0.741065
			 0.949908 -0.204329 -0.236482
			 0.037759  0.113890 -0.992776
			-0.797632 -0.573309 -0.187351
			 0.282812 -0.176308 -0.942832
			-0.948106  0.234861  0.214323
			 0.809767 -0.358745  0.464305
			 0.919656 -0.392724  0.000000
			-0.484160  0.603814 -0.633244
			-0.640455  0.767996  0.000000
			-0.814448  0.536052 -0.222089
			-0.891988  0.396308  0.217479
			-0.484160  0.603814  0.633244
			-0.814448  0.536052  0.222089
			-0.891988  0.396308 -0.217479
			-0.697070  0.370676  0.613753
			 0.479174  0.715137  0.508892
			 0.229880  0.501022  0.834346
			-0.127814  0.460662  0.878325
			-0.215403  0.756877  0.617041
			 0.427651  0.732908  0.529113
			-0.073681  0.594481  0.800727
			-0.450757 -0.551670  0.701768
			-0.256203  0.620631  0.741065
			-0.823305  0.239079  0.514791
			-0.040058  0.015927  0.999070
			-0.674576 -0.415630 -0.610081
			-0.920568  0.159454 -0.356552
			 0.949908 -0.204329  0.236482
			 0.037759  0.113890  0.992776
			 0.282812 -0.176308  0.942832
			-0.797632 -0.573309  0.187351
			-0.948106  0.234861 -0.214323
			 0.809767 -0.358745 -0.464305
			-0.697070  0.370676 -0.613753
			-0.672859  0.500224  0.545011
			-0.312795  0.291564  0.903963
			-0.672859  0.500224 -0.545011
			-0.312795  0.291564 -0.903963
			-0.764068 -0.404386 -0.502666
			-0.726208  0.233457 -0.646622
			-0.849694 -0.397929 -0.345938
			-0.794471  0.525871 -0.303769
			-0.630319  0.123586 -0.766436
			-0.874370 -0.478410 -0.081249
			 0.486528 -0.860464 -0.151304
			 0.367624 -0.803898  0.467548
			 0.787043 -0.616898  0.000000
			 0.367624 -0.803898 -0.467548
			-0.556097  0.462743 -0.690381
			-0.513544  0.387333 -0.765667
			-0.708563  0.705647  0.000000
			-0.556097  0.462743  0.690381
			-0.513543  0.387331  0.765668
			-0.794473  0.525868  0.303769
			-0.630319  0.123586  0.766436
			-0.874370 -0.478410  0.081249
			-0.726212  0.233458  0.646617
			-0.764073 -0.404380  0.502663
			-0.849694 -0.397929  0.345938
			 0.486528 -0.860464  0.151304
			-0.409797  0.022081 -0.911909
			-0.466432 -0.872415  0.146056
			-0.157800 -0.707556 -0.688813
			 0.353553 -0.935414  0.000000
			-0.466432 -0.872415 -0.146056
			-0.157800 -0.707556  0.688813
			-0.806784  0.590847  0.000000
			-0.409796  0.022083  0.911910
			-0.026844  0.185489 -0.982280
			-0.036375 -0.080912 -0.996057
			 0.118171 -0.615436 -0.779278
			-0.092100 -0.995750  0.000000
			 0.118171 -0.615436  0.779278
			-0.036375 -0.080912  0.996057
			-0.754139 -0.055617 -0.654356
			-0.929307 -0.369307  0.000000
			-0.754139 -0.055617  0.654356
			-0.026844  0.185489  0.982280
			-0.335115 -0.604969 -0.722296
			-0.708413 -0.705798  0.000000
			-0.335115 -0.604969  0.722296
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.005778  0.007432  0.000000
			 0.021715  0.008915 -0.009271
			 0.019169  0.005108  0.000000
			 0.016647  0.009246 -0.018973
			 0.007752  0.008847 -0.023697
			-0.012219  0.009779  0.000000
			-0.012120  0.009390 -0.005568
			-0.012501  0.009773 -0.016493
			-0.006859  0.010987 -0.023835
			 0.021715  0.008915  0.009271
			 0.016647  0.009246  0.018973
			 0.007752  0.008847  0.023697
			-0.012120  0.009390  0.005568
			-0.012501  0.009773  0.016493
			-0.006859  0.010987  0.023835
			 0.022457  0.010096 -0.011135
			 0.022354  0.010835  0.000000
			 0.022457  0.010096  0.011135
			 0.015100  0.012354 -0.018324
			 0.006564  0.015008 -0.022277
			-0.011674  0.015489 -0.005208
			-0.011640  0.015334  0.000000
			-0.011674  0.015489  0.005208
			-0.011655  0.017426 -0.016841
			-0.005266  0.014100 -0.022444
			-0.012710  0.017162 -0.009944
			 0.015100  0.012354  0.018324
			 0.006564  0.015008  0.022277
			-0.005266  0.014100  0.022444
			-0.011655  0.017426  0.016841
			-0.012710  0.017162  0.009944
			 0.019440  0.016801 -0.010839
			 0.020897  0.016328  0.000000
			 0.019440  0.016801  0.010839
			 0.013510  0.018064 -0.017864
			 0.006592  0.020091 -0.020258
			-0.011526  0.022700 -0.006689
			-0.010980  0.022754  0.000000
			-0.011526  0.022700  0.006689
			-0.010229  0.021144 -0.020082
			-0.003983  0.021615 -0.021237
			-0.013580  0.023296 -0.010260
			 0.013510  0.018064  0.017864
			 0.006592  0.020091  0.020258
			-0.003983  0.021615  0.021237
			-0.010229  0.021144  0.020082
			-0.013580  0.023296  0.010260
			 0.019667  0.026676 -0.010455
			 0.021228  0.028041  0.000000
			 0.019667  0.026676  0.010455
			 0.013184  0.027085 -0.018550
			 0.007734  0.027058 -0.020348
			-0.013475  0.029916 -0.006911
			-0.015661  0.030358 -0.010728
			-0.012671  0.030063  0.000000
			-0.013475  0.029916  0.006911
			-0.015661  0.030358  0.010728
			-0.008762  0.028141 -0.021807
			-0.003490  0.027147 -0.021386
			-0.013818  0.023841 -0.022188
			-0.017001  0.020415 -0.018091
			-0.015995  0.027717 -0.026540
			-0.022666  0.025751 -0.003911
			 0.013184  0.027085  0.018550
			 0.007734  0.027058  0.020348
			-0.003490  0.027147  0.021386
			-0.008762  0.028141  0.021807
			-0.017001  0.020415  0.018091
			-0.013818  0.023841  0.022188
			-0.015995  0.027717  0.026540
			-0.022666  0.025751  0.003911
			 0.021185  0.032210 -0.011570
			 0.021867  0.032697  0.000000
			 0.021185  0.032210  0.011570
			 0.015399  0.031567 -0.019637
			 0.008580  0.030741 -0.022504
			-0.013957  0.038367 -0.008823
			-0.014821  0.036330 -0.011520
			-0.012136  0.037343  0.000000
			-0.013957  0.038367  0.008823
			-0.014821  0.036330  0.011520
			-0.009813  0.033217 -0.021070
			-0.000977  0.032237 -0.023599
			-0.016786  0.034456 -0.021124
			-0.024230  0.025190 -0.018198
			-0.025888  0.030186 -0.024502
			-0.020782  0.022666 -0.026213
			-0.019241  0.017201 -0.011135
			-0.021769  0.025238 -0.030672
			-0.020106  0.028672 -0.033740
			-0.022867  0.012743 -0.005388
			-0.023024  0.024094  0.000000
			-0.032022  0.027475  0.000000
			-0.024669  0.032830 -0.008145
			 0.015399  0.031567  0.019637
			 0.008580  0.030741  0.022504
			-0.000977  0.032237  0.023599
			-0.009813  0.033217  0.021070
			-0.016786  0.034456  0.021124
			-0.025888  0.030186  0.024502
			-0.024230  0.025190  0.018198
			-0.020782  0.022666  0.026213
			-0.019241  0.017201  0.011135
			-0.020106  0.028672  0.033740
			-0.021769  0.025238  0.030672
			-0.022867  0.012743  0.005388
			-0.024669  0.032830  0.008145
			 0.019346  0.037497 -0.011782
			 0.020267  0.037802  0.000000
			 0.019346  0.037497  0.011782
			 0.015273  0.037180 -0.019907
			 0.007549  0.037184 -0.023593
			-0.002455  0.036398 -0.023745
			-0.016440  0.036581 -0.014915
			-0.022715  0.043448 -0.016931
			-0.022658  0.040305 -0.010630
			 0.004814  0.037609  0.000000
			-0.011853  0.037863 -0.018112
			-0.022715  0.043448  0.016931
			-0.016440  0.036581  0.014915
			-0.022658  0.040305  0.010630
			-0.011853  0.037863  0.018112
			-0.022238  0.035098 -0.030109
			-0.020940  0.038679 -0.019473
			-0.027751  0.017788 -0.016388
			-0.032679  0.016100 -0.013384
			-0.031569  0.032091 -0.022347
			-0.032182  0.030641 -0.015006
			-0.029876  0.028954 -0.013416
			-0.030778  0.041967 -0.020620
			-0.025333  0.036998 -0.022534
			-0.020581  0.010056 -0.012693
			-0.032789  0.011429 -0.018062
			-0.026240  0.026674 -0.034038
			-0.024309  0.029169 -0.037326
			-0.026869  0.034895 -0.034516
			-0.023381  0.006829 -0.007075
			-0.026183  0.014011  0.000000
			-0.033449  0.023408 -0.005224
			-0.042582  0.019631  0.000000
			-0.029262  0.022073 -0.008771
			-0.028911  0.027066 -0.011001
			-0.033449  0.023408  0.005224
			-0.029262  0.022073  0.008771
			-0.028911  0.027066  0.011001
			-0.025821  0.033464 -0.012003
			 0.015273  0.037180  0.019907
			 0.007549  0.037184  0.023593
			-0.002455  0.036398  0.023745
			-0.022238  0.035098  0.030109
			-0.020940  0.038679  0.019473
			-0.030778  0.041967  0.020620
			-0.031569  0.032091  0.022347
			-0.025333  0.036998  0.022534
			-0.032679  0.016100  0.013384
			-0.027751  0.017788  0.016388
			-0.032182  0.030641  0.015006
			-0.029876  0.028954  0.013416
			-0.020581  0.010056  0.012693
			-0.032789  0.011429  0.018062
			-0.024309  0.029169  0.037326
			-0.026240  0.026674  0.034038
			-0.026869  0.034895  0.034516
			-0.023381  0.006829  0.007075
			-0.025821  0.033464  0.012003
			-0.037137  0.038505 -0.017281
			-0.030002  0.035671 -0.014735
			-0.037137  0.038505  0.017281
			-0.030002  0.035671  0.014735
			-0.036067  0.007757 -0.010277
			-0.035529  0.012929 -0.010861
			-0.033210  0.003856 -0.017807
			-0.032371  0.018474 -0.009358
			-0.035241  0.036757 -0.022856
			-0.037377  0.034174 -0.017753
			-0.025486 -0.001924 -0.014104
			-0.026444  0.004291 -0.005620
			-0.029430  0.008109  0.000000
			-0.026444  0.004291  0.005620
			-0.037963  0.018667 -0.004411
			-0.041620  0.014435 -0.004293
			-0.052780  0.016575  0.000000
			-0.037963  0.018667  0.004411
			-0.041620  0.014435  0.004293
			-0.032371  0.018474  0.009358
			-0.035241  0.036757  0.022856
			-0.037377  0.034174  0.017753
			-0.035529  0.012929  0.010861
			-0.036067  0.007757  0.010277
			-0.033210  0.003856  0.017807
			-0.025486 -0.001924  0.014104
			-0.042146  0.009524 -0.004482
			-0.032072  0.004489 -0.008077
			-0.037571  0.006157 -0.005259
			-0.037770  0.000514  0.000000
			-0.032072  0.004489  0.008077
			-0.037571  0.006157  0.005259
			-0.055319  0.011335  0.000000
			-0.042146  0.009524  0.004482
			-0.046261  0.006175 -0.004884
			-0.042921  0.003455 -0.004902
			-0.044617 -0.000808 -0.003805
			-0.046636 -0.001481  0.000000
			-0.044617 -0.000808  0.003805
			-0.042921  0.003455  0.004902
			-0.053575  0.005857 -0.004946
			-0.054633  0.004256  0.000000
			-0.053575  0.005857  0.004946
			-0.046261  0.006175  0.004884
			-0.051900  0.001762 -0.005763
			-0.050950 -0.000391  0.000000
			-0.051900  0.001762  0.005763
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 3 1 
					0 4 3 
					0 5 6 
					6 7 0 
					0 7 8 
					8 4 0 
					2 9 0 
					9 10 0 
					10 11 0 
					12 5 0 
					0 13 12 
					14 13 0 
					0 11 14 
					2 1 15 
					15 1 3 
					16 2 15 
					17 9 2 
					17 2 16 
					18 15 3 
					18 3 4 
					19 18 4 
					19 4 8 
					20 6 5 
					21 20 5 
					5 12 22 
					5 22 21 
					6 20 7 
					7 23 24 
					8 7 24 
					25 23 7 
					20 25 7 
					24 19 8 
					10 9 17 
					10 17 26 
					11 10 26 
					11 26 27 
					14 11 27 
					13 22 12 
					28 29 13 
					28 13 14 
					13 29 30 
					13 30 22 
					14 27 28 
					15 31 32 
					16 15 32 
					31 15 18 
					32 17 16 
					32 33 17 
					26 17 33 
					34 31 18 
					34 18 19 
					35 34 19 
					35 19 24 
					20 21 36 
					25 20 36 
					37 36 21 
					21 38 37 
					38 21 22 
					38 22 30 
					23 39 40 
					24 23 40 
					41 23 25 
					41 39 23 
					40 35 24 
					41 25 36 
					26 33 42 
					27 26 42 
					27 42 43 
					28 27 43 
					28 43 44 
					44 29 28 
					44 45 29 
					30 29 46 
					29 45 46 
					38 30 46 
					31 47 48 
					32 31 48 
					47 31 34 
					48 33 32 
					48 49 33 
					42 33 49 
					50 47 34 
					50 34 35 
					51 50 35 
					51 35 40 
					52 36 37 
					52 53 36 
					53 41 36 
					54 52 37 
					37 38 55 
					37 55 54 
					38 56 55 
					38 46 56 
					39 57 58 
					40 39 58 
					59 39 60 
					39 59 61 
					57 39 61 
					41 60 39 
					58 51 40 
					41 62 60 
					62 41 53 
					42 49 63 
					43 42 63 
					43 63 64 
					44 43 64 
					44 64 65 
					65 45 44 
					65 66 45 
					67 45 68 
					69 68 45 
					69 45 66 
					45 67 46 
					67 70 46 
					56 46 70 
					47 71 72 
					48 47 72 
					71 47 50 
					72 49 48 
					72 73 49 
					63 49 73 
					74 71 50 
					74 50 51 
					75 74 51 
					75 51 58 
					76 52 54 
					77 53 52 
					76 77 52 
					53 77 62 
					78 76 54 
					54 55 79 
					54 79 78 
					55 56 80 
					55 80 79 
					70 80 56 
					57 81 82 
					58 57 82 
					57 61 83 
					81 57 83 
					82 75 58 
					61 59 60 
					60 84 85 
					86 60 85 
					86 61 60 
					87 84 60 
					62 87 60 
					88 89 61 
					86 88 61 
					61 89 83 
					90 62 91 
					92 91 62 
					93 92 62 
					62 90 87 
					77 93 62 
					63 73 94 
					64 63 94 
					64 94 95 
					65 64 95 
					65 95 96 
					96 66 65 
					96 97 66 
					98 69 66 
					98 66 97 
					99 100 67 
					99 67 101 
					67 68 69 
					67 69 101 
					67 100 102 
					67 102 70 
					69 103 104 
					69 104 101 
					98 103 69 
					91 70 105 
					70 91 92 
					70 92 106 
					102 105 70 
					70 106 80 
					71 107 108 
					72 71 108 
					107 71 74 
					108 73 72 
					108 109 73 
					94 73 109 
					110 107 74 
					74 75 111 
					110 74 111 
					75 82 112 
					111 75 112 
					76 113 114 
					114 115 76 
					115 93 76 
					77 76 93 
					116 76 78 
					116 117 76 
					76 117 113 
					78 79 116 
					118 119 79 
					79 120 118 
					79 106 120 
					106 79 80 
					79 121 116 
					119 121 79 
					82 81 112 
					81 83 117 
					81 117 112 
					83 113 117 
					89 122 83 
					123 83 122 
					113 83 123 
					84 124 125 
					85 84 126 
					84 127 126 
					87 124 84 
					125 128 84 
					128 127 84 
					85 126 129 
					122 86 85 
					122 85 130 
					130 85 129 
					122 88 86 
					131 87 90 
					87 131 132 
					124 87 132 
					88 133 134 
					89 88 134 
					88 135 133 
					122 135 88 
					89 134 122 
					136 90 137 
					137 90 91 
					136 131 90 
					91 105 137 
					92 138 139 
					140 138 92 
					141 140 92 
					92 93 141 
					139 142 92 
					92 142 143 
					92 143 144 
					144 106 92 
					93 145 141 
					115 145 93 
					94 109 146 
					147 95 94 
					147 94 146 
					148 96 95 
					148 95 147 
					148 97 96 
					121 98 97 
					148 121 97 
					121 119 98 
					98 149 103 
					149 98 150 
					150 98 119 
					151 152 99 
					152 100 99 
					99 101 149 
					153 99 149 
					151 99 153 
					154 155 100 
					152 156 100 
					100 155 102 
					100 157 154 
					100 156 157 
					101 104 149 
					105 102 158 
					159 158 102 
					159 102 155 
					149 160 103 
					160 104 103 
					160 161 104 
					161 162 104 
					104 162 149 
					137 105 163 
					105 158 163 
					144 164 106 
					106 164 120 
					116 107 110 
					116 108 107 
					109 108 116 
					146 109 116 
					116 110 111 
					116 111 112 
					116 112 117 
					123 114 113 
					114 123 129 
					114 129 165 
					166 114 165 
					166 145 114 
					115 114 145 
					147 146 116 
					148 147 116 
					121 148 116 
					119 118 150 
					151 150 118 
					167 151 118 
					167 118 168 
					118 164 168 
					164 118 120 
					134 135 122 
					130 123 122 
					123 130 129 
					125 124 132 
					169 170 125 
					171 169 125 
					132 171 125 
					172 125 170 
					125 172 128 
					126 173 129 
					174 173 126 
					174 126 127 
					165 174 127 
					166 165 127 
					127 128 145 
					166 127 145 
					145 128 141 
					140 128 172 
					141 128 140 
					165 129 173 
					131 175 132 
					136 175 131 
					175 171 132 
					133 135 134 
					176 136 137 
					176 175 136 
					177 176 137 
					137 163 178 
					137 178 177 
					139 138 179 
					138 140 179 
					180 181 139 
					179 180 139 
					182 142 139 
					139 181 183 
					139 183 182 
					140 172 179 
					182 143 142 
					182 184 143 
					184 157 143 
					143 157 144 
					144 157 164 
					149 162 160 
					149 150 153 
					151 153 150 
					151 185 152 
					185 151 167 
					152 185 186 
					156 152 186 
					159 155 154 
					154 187 188 
					154 188 189 
					154 189 159 
					187 154 184 
					157 184 154 
					156 186 167 
					156 167 168 
					164 157 156 
					164 156 168 
					159 190 158 
					158 190 163 
					159 189 190 
					160 162 161 
					163 190 178 
					174 165 173 
					185 167 186 
					170 169 191 
					169 192 193 
					191 169 193 
					169 171 192 
					172 170 180 
					180 170 191 
					171 175 192 
					179 172 180 
					192 175 176 
					193 192 176 
					194 193 176 
					194 176 177 
					177 178 194 
					178 190 195 
					178 195 196 
					178 196 194 
					180 191 181 
					191 197 181 
					181 197 198 
					181 198 183 
					183 184 182 
					183 187 184 
					198 187 183 
					198 188 187 
					196 195 188 
					196 188 198 
					195 189 188 
					195 190 189 
					191 199 197 
					191 193 200 
					199 191 200 
					193 194 201 
					200 193 201 
					202 201 194 
					194 203 202 
					203 194 196 
					204 196 198 
					203 196 204 
					199 205 197 
					197 205 206 
					197 207 208 
					197 208 198 
					206 207 197 
					204 198 208 
					199 200 201 
					209 199 201 
					199 209 205 
					209 201 202 
					210 209 202 
					202 203 211 
					202 211 210 
					203 204 208 
					203 208 211 
					205 209 210 
					206 205 210 
					210 207 206 
					210 211 207 
					207 211 208 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
