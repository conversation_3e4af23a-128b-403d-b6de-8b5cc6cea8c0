<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.463554 -0.886069  0.000329
			 0.238564 -0.953274  0.185353
			-0.283762 -0.958895  0.000015
			 0.909800 -0.310106  0.275859
			 0.897086 -0.441855  0.000214
			 0.238664 -0.953294 -0.185122
			 0.909898 -0.310060 -0.275586
			 0.285428 -0.829383  0.480265
			 0.046071 -0.671527  0.739547
			-0.604515 -0.796441 -0.015577
			-0.698625 -0.715488 -0.000014
			-0.638073 -0.752134  0.164796
			-0.360123 -0.600972  0.713543
			 0.285426 -0.829385 -0.480262
			 0.046076 -0.671525 -0.739548
			-0.604513 -0.796443  0.015551
			-0.638070 -0.752137 -0.164792
			-0.360119 -0.600970 -0.713547
			 0.999459 -0.032886  0.000161
			 0.946653  0.012533  0.322010
			 0.695248 -0.023727  0.718378
			 0.946751  0.012540 -0.321722
			 0.695248 -0.023735 -0.718378
			 0.282111  0.088732  0.955270
			-0.968440 -0.246759 -0.035131
			-0.999133  0.041631 -0.000019
			-0.968440 -0.246760  0.035127
			-0.262726  0.025497  0.964534
			-0.825988 -0.218541  0.519600
			-0.976778 -0.091941 -0.193525
			 0.282109  0.088735 -0.955270
			-0.825988 -0.218541 -0.519599
			-0.262726  0.025491 -0.964534
			-0.976778 -0.091940  0.193526
			 0.944138 -0.329551  0.000348
			 0.845289 -0.390695  0.364477
			 0.670197  0.043566  0.740904
			 0.845392 -0.390720 -0.364212
			 0.670193  0.043563 -0.740907
			 0.267836  0.097193  0.958550
			-0.889950  0.371636 -0.264341
			-0.891577  0.452869 -0.000074
			-0.889960  0.371632  0.264312
			-0.140165  0.064194  0.988045
			-0.243579 -0.490257  0.836850
			-0.852928 -0.275821 -0.443211
			 0.267834  0.097193 -0.958550
			-0.243582 -0.490260 -0.836847
			-0.140170  0.064192 -0.988044
			-0.852929 -0.275824  0.443208
			 0.914123 -0.405438  0.000204
			 0.841344 -0.439100  0.315169
			 0.465886 -0.411649  0.783260
			 0.841397 -0.439106 -0.315019
			 0.465884 -0.411648 -0.783261
			 0.099253 -0.351308  0.930984
			-0.878127  0.402478 -0.258658
			-0.333403  0.274962 -0.901798
			-0.931671  0.363304 -0.000148
			-0.878158  0.402467  0.258571
			-0.333402  0.274955  0.901800
			-0.111824 -0.282007  0.952873
			 0.230624  0.015701  0.972916
			-0.468771 -0.871845  0.141916
			-0.558576 -0.418313  0.716245
			 0.034745 -0.024869  0.999087
			 0.680252 -0.149162 -0.717641
			 0.099252 -0.351316 -0.930981
			 0.230626  0.015699 -0.972916
			-0.111825 -0.282005 -0.952874
			-0.558566 -0.418324 -0.716247
			-0.468771 -0.871845 -0.141915
			 0.034750 -0.024876 -0.999086
			 0.680425 -0.149156  0.717477
			 0.979184  0.202975  0.000169
			 0.890717  0.325273  0.317523
			 0.498354 -0.413700  0.761903
			 0.890759  0.325286 -0.317392
			 0.498352 -0.413707 -0.761900
			 0.047250 -0.374998  0.925821
			-0.213949  0.912229 -0.349376
			-0.270031  0.081615 -0.959387
			-0.438301  0.898828 -0.000227
			-0.213980  0.912274  0.349241
			-0.270023  0.081613  0.959389
			-0.208827 -0.093720  0.973452
			-0.196161  0.356924  0.913305
			 0.238605  0.673098  0.700005
			-0.364532 -0.466311  0.806021
			-0.530129 -0.405491  0.744675
			 0.414629 -0.280910  0.865547
			 0.682221 -0.566279  0.462497
			 0.212651  0.182368  0.959959
			-0.658642 -0.331694  0.675404
			 0.919277 -0.393611 -0.000220
			 0.527388 -0.714444 -0.459817
			-0.213742  0.976890  0.000050
			-0.249011  0.625576 -0.739356
			 0.047243 -0.375001 -0.925820
			-0.196151  0.356928 -0.913306
			-0.208820 -0.093713 -0.973454
			 0.238606  0.673103 -0.700000
			-0.530126 -0.405495 -0.744674
			-0.364624 -0.465517 -0.806439
			 0.414608 -0.280919 -0.865554
			 0.682223 -0.566270 -0.462504
			-0.658646 -0.331698 -0.675397
			 0.212658  0.182363 -0.959959
			 0.527367 -0.714500  0.459753
			-0.248918  0.625582  0.739383
			 0.365539  0.930796 -0.000003
			 0.700784  0.502260  0.506593
			 0.431332  0.377696  0.819328
			 0.700783  0.502269 -0.506586
			 0.431330  0.377701 -0.819327
			-0.083106  0.473145  0.877056
			 0.185857  0.974334 -0.127007
			 0.322864  0.879385  0.349917
			-0.420710  0.740596 -0.523946
			 0.105417  0.923846  0.367961
			 0.322864  0.879385 -0.349916
			 0.186025  0.974271  0.127242
			-0.420710  0.740596  0.523946
			 0.105436  0.923848 -0.367951
			 0.511238  0.453325  0.730159
			 0.572315  0.468840  0.672789
			 0.632815  0.126966  0.763822
			-0.116941 -0.790862  0.600718
			 0.609675  0.015652  0.792497
			-0.714087  0.525496  0.462530
			-0.121575  0.224217  0.966926
			-0.908749 -0.330825 -0.254422
			-0.799975  0.523453 -0.293320
			 0.786412 -0.535633  0.307656
			-0.009945  0.251707  0.967753
			 0.108435 -0.040322  0.993286
			-0.948961 -0.154093  0.275189
			-0.869577  0.450552 -0.202087
			-0.266278 -0.963896 -0.000120
			-0.371034 -0.844128 -0.387017
			-0.686296  0.727322  0.000195
			-0.319900  0.878646  0.354464
			-0.578102  0.796109  0.178910
			-0.755372  0.619822 -0.212684
			-0.319720  0.878783 -0.354287
			-0.577974  0.796238 -0.178749
			-0.755364  0.619810  0.212745
			-0.674277  0.524995 -0.519356
			-0.083101  0.473150 -0.877054
			 0.511231  0.453334 -0.730159
			 0.571931  0.469255 -0.672826
			-0.121570  0.224223 -0.966925
			-0.714086  0.525491 -0.462537
			-0.115297 -0.789130 -0.603309
			-0.908023 -0.333284  0.253804
			-0.799982  0.523449  0.293308
			 0.631495  0.129230 -0.764535
			 0.609180  0.017037 -0.792849
			 0.786409 -0.535635 -0.307662
			-0.009943  0.251714 -0.967751
			-0.948970 -0.154088 -0.275161
			 0.108430 -0.040326 -0.993286
			-0.869567  0.450558  0.202118
			-0.370929 -0.844193  0.386977
			-0.674279  0.524995  0.519354
			-0.671229  0.675827 -0.304481
			-0.598891  0.343593 -0.723377
			-0.671538  0.674608  0.306497
			-0.599145  0.341164  0.724316
			-0.664710 -0.111602  0.738719
			-0.857052 -0.499910  0.124709
			-0.783740  0.460121  0.417182
			-0.917331 -0.103507  0.384435
			-0.834757 -0.364694  0.412527
			-0.651778  0.715314  0.252014
			 0.172982 -0.951983  0.252599
			-0.024711 -0.953331 -0.300915
			 0.613027 -0.790062 -0.000049
			-0.024733 -0.953362  0.300814
			-0.609681  0.604545  0.512654
			-0.753550  0.657387 -0.002183
			-0.734049  0.388866  0.556736
			-0.609741  0.604553 -0.512574
			-0.733723  0.389136 -0.556978
			-0.651781  0.715312 -0.252013
			-0.917329 -0.103514 -0.384438
			-0.783741  0.460117 -0.417184
			-0.834758 -0.364695 -0.412525
			-0.664710 -0.109702 -0.739003
			-0.856251 -0.500737 -0.126876
			 0.172985 -0.951983 -0.252596
			-0.644414  0.260221  0.719038
			-0.161397 -0.913313  0.373912
			-0.325080 -0.905778 -0.271825
			 0.504339 -0.863506 -0.000115
			-0.325082 -0.905779  0.271821
			-0.157265 -0.911545 -0.379938
			-0.543142  0.839636 -0.002898
			-0.645145  0.258360 -0.719054
			-0.122075  0.221019  0.967599
			-0.075238 -0.040373  0.996348
			 0.405553 -0.777390  0.480824
			-0.063362 -0.997990 -0.001036
			 0.402646 -0.775926 -0.485608
			-0.083386 -0.038725 -0.995765
			-0.761121  0.321575  0.563280
			-0.984413  0.175869 -0.000997
			-0.127607  0.221249 -0.966833
			-0.760387  0.320550 -0.564853
			-0.480029 -0.578022  0.659896
			-0.793233 -0.608918 -0.001157
			-0.478586 -0.578013 -0.660951
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.026216  0.405048  0.000620
			-0.026714  0.405479  0.007455
			-0.036901  0.408702  0.000601
			-0.024828  0.408246  0.008824
			-0.025227  0.406028  0.000621
			-0.026714  0.405479 -0.006258
			-0.024828  0.408246 -0.007627
			-0.029814  0.407017  0.014569
			-0.034933  0.408971  0.018029
			-0.046633  0.411328  0.004721
			-0.046673  0.411584  0.000601
			-0.046863  0.411622  0.012734
			-0.043194  0.411512  0.018122
			-0.029814  0.407017 -0.013372
			-0.034933  0.408971 -0.016832
			-0.046633  0.411328 -0.003524
			-0.046863  0.411622 -0.011537
			-0.043194  0.411512 -0.016925
			-0.024738  0.409783  0.000621
			-0.025657  0.410510  0.009404
			-0.028668  0.410316  0.015440
			-0.025657  0.410510 -0.008207
			-0.028668  0.410316 -0.014243
			-0.034695  0.413087  0.018625
			-0.048855  0.415013  0.004842
			-0.048855  0.414905  0.000601
			-0.048855  0.415013 -0.003645
			-0.043017  0.416000  0.018753
			-0.049003  0.412892  0.014228
			-0.049271  0.416388  0.008663
			-0.034695  0.413087 -0.017428
			-0.049003  0.412892 -0.013031
			-0.043017  0.416000 -0.017556
			-0.049271  0.416388 -0.007466
			-0.022451  0.417200  0.000623
			-0.023764  0.416780  0.009097
			-0.029490  0.413054  0.015070
			-0.023764  0.416780 -0.007900
			-0.029490  0.413054 -0.013873
			-0.033861  0.416377  0.016998
			-0.047111  0.420536  0.006039
			-0.046725  0.420412  0.000602
			-0.047111  0.420536 -0.004842
			-0.042134  0.418821  0.017781
			-0.046506  0.419157  0.016846
			-0.048411  0.421519  0.008920
			-0.033861  0.416377 -0.015801
			-0.046506  0.419157 -0.015649
			-0.042134  0.418821 -0.016584
			-0.048411  0.421519 -0.007723
			-0.021195  0.420001  0.000624
			-0.021754  0.419886  0.009999
			-0.028126  0.418937  0.015626
			-0.021754  0.419886 -0.008802
			-0.028126  0.418937 -0.014429
			-0.031855  0.420513  0.017073
			-0.043679  0.426712  0.006220
			-0.045097  0.427634  0.009299
			-0.043100  0.426572  0.000605
			-0.043679  0.426712 -0.005023
			-0.045097  0.427634 -0.008102
			-0.039507  0.423851  0.017905
			-0.042934  0.426031  0.018241
			-0.050555  0.420310  0.015235
			-0.048486  0.421936  0.018543
			-0.045800  0.426035  0.022056
			-0.053470  0.425002  0.003792
			-0.031855  0.420513 -0.015876
			-0.042934  0.426031 -0.017044
			-0.039507  0.423851 -0.016708
			-0.048486  0.421936 -0.017346
			-0.050555  0.420310 -0.014038
			-0.045800  0.426035 -0.020859
			-0.053470  0.425002 -0.002595
			-0.020354  0.423353  0.000625
			-0.021045  0.423425  0.010172
			-0.025825  0.421165  0.016505
			-0.021045  0.423425 -0.008975
			-0.025825  0.421165 -0.015308
			-0.030630  0.422628  0.018814
			-0.042523  0.432275  0.007766
			-0.043473  0.431220  0.009940
			-0.041454  0.431086  0.000606
			-0.042523  0.432275 -0.006569
			-0.043473  0.431220 -0.008743
			-0.036897  0.426382  0.019693
			-0.040605  0.427757  0.017648
			-0.045151  0.430591  0.017688
			-0.055143  0.428656  0.020406
			-0.054651  0.425003  0.015319
			-0.053989  0.419617  0.021786
			-0.052646  0.418904  0.009620
			-0.051296  0.426302  0.020976
			-0.054085  0.421542  0.019123
			-0.055353  0.421083  0.000602
			-0.060474  0.413024  0.004980
			-0.059795  0.428336  0.000603
			-0.053921  0.430097  0.007209
			-0.030630  0.422628 -0.017617
			-0.040605  0.427757 -0.016451
			-0.036897  0.426382 -0.018496
			-0.045151  0.430591 -0.016491
			-0.054651  0.425003 -0.014122
			-0.055143  0.428656 -0.019209
			-0.053989  0.419617 -0.020588
			-0.052646  0.418904 -0.008423
			-0.054085  0.421542 -0.017926
			-0.051296  0.426302 -0.019779
			-0.060474  0.413024 -0.003782
			-0.053921  0.430097 -0.006012
			-0.031981  0.428139  0.000607
			-0.024924  0.424802  0.016725
			-0.030202  0.427063  0.019694
			-0.024924  0.424802 -0.015528
			-0.030202  0.427063 -0.018497
			-0.037174  0.429484  0.019811
			-0.051122  0.436568  0.014303
			-0.044537  0.431854  0.012679
			-0.051503  0.434503  0.009218
			-0.041180  0.431335  0.015261
			-0.044537  0.431854 -0.011482
			-0.051122  0.436568 -0.013106
			-0.051503  0.434503 -0.008021
			-0.041180  0.431335 -0.014064
			-0.051921  0.431002  0.018787
			-0.050523  0.433034  0.016354
			-0.061316  0.441012  0.032461
			-0.058867  0.431240  0.018664
			-0.053832  0.432973  0.018820
			-0.065467  0.419863  0.011427
			-0.062063  0.420379  0.013854
			-0.063826  0.433949  0.022293
			-0.058099  0.428793  0.011459
			-0.053480  0.417526  0.010875
			-0.066178  0.416838  0.015199
			-0.054178  0.427617  0.023137
			-0.055867  0.426445  0.021152
			-0.055197  0.431961  0.021442
			-0.061465  0.417546  0.000601
			-0.060474  0.412194  0.006339
			-0.070528  0.421393  0.000602
			-0.061348  0.426016  0.004845
			-0.058595  0.424157  0.007708
			-0.057675  0.427333  0.009509
			-0.061348  0.426016 -0.003648
			-0.058595  0.424157 -0.006511
			-0.057675  0.427333 -0.008312
			-0.054645  0.430782  0.010321
			-0.037174  0.429484 -0.018614
			-0.051921  0.431002 -0.017590
			-0.050523  0.433034 -0.015157
			-0.062063  0.420379 -0.012657
			-0.065467  0.419863 -0.010230
			-0.058867  0.431240 -0.017467
			-0.063826  0.433949 -0.021096
			-0.058099  0.428793 -0.010262
			-0.061316  0.441012 -0.031182
			-0.053832  0.432973 -0.017623
			-0.053480  0.417526 -0.009678
			-0.066178  0.416838 -0.014002
			-0.055867  0.426445 -0.019955
			-0.054178  0.427617 -0.021940
			-0.055197  0.431961 -0.020245
			-0.060474  0.412194 -0.005142
			-0.054645  0.430782 -0.009124
			-0.066239  0.440252  0.027311
			-0.057280  0.433210  0.012524
			-0.066239  0.440252 -0.026033
			-0.057280  0.433210 -0.011327
			-0.065151  0.438662  0.035898
			-0.066993  0.437481  0.028037
			-0.067892  0.418465  0.009388
			-0.068967  0.415215  0.008916
			-0.067496  0.411992  0.014991
			-0.064928  0.421341  0.008179
			-0.061835  0.410870  0.012008
			-0.061662  0.415155  0.005164
			-0.064260  0.413880  0.000600
			-0.061662  0.415155 -0.003967
			-0.068819  0.422787  0.004184
			-0.078387  0.414307  0.000585
			-0.069504  0.419424  0.004087
			-0.068819  0.422787 -0.002987
			-0.069504  0.419424 -0.002890
			-0.064928  0.421341 -0.006982
			-0.068967  0.415215 -0.007719
			-0.067892  0.418465 -0.008192
			-0.067496  0.411992 -0.013794
			-0.065151  0.438662 -0.034620
			-0.066993  0.437481 -0.026759
			-0.061835  0.410870 -0.010811
			-0.070536  0.416341  0.004238
			-0.070235  0.414526  0.004865
			-0.066606  0.412139  0.007142
			-0.071134  0.410889  0.000598
			-0.066606  0.412139 -0.005945
			-0.070235  0.414526 -0.003668
			-0.084607  0.408173  0.000621
			-0.070536  0.416341 -0.003041
			-0.080057  0.405932  0.003395
			-0.073848  0.409857  0.004573
			-0.082152  0.400370  0.002787
			-0.082709  0.400311  0.000636
			-0.082152  0.400370 -0.001509
			-0.073848  0.409857 -0.003376
			-0.087051  0.406100  0.003426
			-0.087923  0.405301  0.000626
			-0.080057  0.405932 -0.002116
			-0.087051  0.406100 -0.002147
			-0.086500  0.403035  0.003884
			-0.085352  0.401319  0.000620
			-0.086500  0.403035 -0.002606
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
