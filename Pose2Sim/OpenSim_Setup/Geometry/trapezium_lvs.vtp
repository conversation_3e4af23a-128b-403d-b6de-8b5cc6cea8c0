<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="105" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="206">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.502749 -0.404620 -0.763889
			-0.848746 -0.281744 -0.447492
			-0.656189 -0.003993 -0.754586
			-0.371313 -0.279873 -0.885324
			-0.610653 -0.789799  0.057625
			-0.366064 -0.921313  0.131072
			 0.259010 -0.451371 -0.853919
			 0.014139 -0.532100 -0.846563
			-0.890744  0.152056 -0.428316
			-0.991261 -0.126862 -0.036150
			-0.671967  0.028559 -0.740030
			-0.495295 -0.445850 -0.745587
			-0.090860 -0.590633 -0.801809
			-0.709966 -0.270507  0.650210
			 0.082478 -0.500543  0.861774
			 0.025259 -0.971960 -0.233787
			 0.454757 -0.890471  0.016076
			 0.184543 -0.732966 -0.654756
			 0.383651 -0.786621 -0.483777
			 0.486958 -0.541515 -0.685299
			 0.726238 -0.633123 -0.267832
			 0.469285 -0.882128 -0.040266
			-0.863954  0.279059 -0.419177
			-0.912789  0.402909  0.066944
			-0.802686 -0.129589 -0.582153
			-0.638273 -0.648893 -0.414180
			-0.114869 -0.820280 -0.560308
			-0.592020  0.394577  0.702724
			-0.368713  0.135714  0.919583
			 0.131212 -0.247815  0.959881
			 0.155730 -0.301064  0.940802
			 0.370888 -0.631240  0.681160
			 0.170393 -0.796332 -0.580364
			 0.868983 -0.494203 -0.025131
			 0.554344 -0.772890 -0.308779
			 0.738097 -0.656908  0.153902
			 0.359065 -0.781501 -0.510224
			 0.572041 -0.283665  0.769612
			-0.945669  0.202930 -0.254028
			-0.918183  0.387647  0.081668
			-0.887123  0.401034  0.228439
			-0.959158 -0.276653 -0.058983
			-0.925007 -0.226184 -0.305290
			-0.152825 -0.862985 -0.481561
			-0.562747 -0.628096 -0.537412
			-0.737270  0.416310  0.532089
			-0.149465  0.495808  0.855474
			 0.000787 -0.240315  0.970695
			 0.588148 -0.484305  0.647712
			 0.799472 -0.389215  0.457554
			-0.145026 -0.297362 -0.943686
			 0.530307 -0.385803 -0.754938
			 0.294033  0.228867 -0.927990
			 0.863205 -0.328549  0.383318
			 0.744099 -0.640266 -0.190727
			 0.752259 -0.292011  0.590624
			 0.716432 -0.394875 -0.575151
			-0.988179  0.054903  0.143137
			-0.922748  0.318989  0.216293
			-0.982487  0.180191  0.047441
			-0.828591  0.555556 -0.069237
			-0.976108  0.042536  0.213080
			-0.532536  0.216059 -0.818366
			-0.822618  0.434041 -0.367300
			-0.743727  0.529134  0.408518
			-0.101731  0.777716  0.620329
			-0.046286  0.291623  0.955413
			-0.056499  0.781245  0.621662
			 0.676366  0.100083  0.729734
			 0.966483 -0.198647  0.162634
			 0.926195 -0.343916  0.154546
			-0.024873  0.563501 -0.825741
			 0.576158  0.274220 -0.769965
			 0.107978  0.524090 -0.844790
			 0.874474  0.030367  0.484121
			 0.965932 -0.149428 -0.211295
			-0.897282  0.285572  0.336650
			-0.701102  0.712553 -0.026909
			-0.543628  0.812723  0.209641
			-0.477058  0.846605 -0.235957
			-0.457753  0.849128 -0.263520
			-0.457302  0.603329 -0.653352
			-0.078454  0.603340 -0.793616
			-0.243841  0.884881  0.396896
			-0.128659  0.877120  0.462716
			 0.473000  0.717614  0.511176
			 0.638645  0.615061  0.462420
			 0.986654  0.085819  0.138379
			 0.247104  0.331212 -0.910625
			 0.174738  0.483739 -0.857592
			-0.028343  0.738100 -0.674096
			 0.041987  0.658078 -0.751778
			 0.793107  0.426997  0.434344
			 0.892468  0.312558  0.325282
			 0.734973  0.176604 -0.654695
			-0.254039  0.889882 -0.378911
			-0.454330  0.876865 -0.157134
			-0.324941  0.847522 -0.419667
			-0.387894  0.919287  0.066700
			-0.051690  0.984384 -0.168273
			 0.355096  0.856683  0.374168
			-0.048396  0.739645 -0.671255
			-0.295763  0.698734 -0.651379
			 0.076906  0.702065 -0.707948
			 0.617196  0.782883 -0.078507
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.013378 -0.030132 -0.023299
			-0.014759 -0.029577 -0.021955
			-0.013478 -0.028482 -0.023544
			-0.010990 -0.026772 -0.025112
			-0.014058 -0.031391 -0.020388
			-0.010899 -0.033452 -0.022304
			-0.009159 -0.029500 -0.023953
			-0.010059 -0.032760 -0.023660
			-0.014960 -0.027568 -0.021816
			-0.015345 -0.029030 -0.019783
			-0.012625 -0.026658 -0.023891
			-0.010508 -0.025801 -0.025726
			-0.008968 -0.026663 -0.025670
			-0.014891 -0.029595 -0.016850
			-0.010521 -0.029222 -0.018194
			-0.009873 -0.033762 -0.022566
			-0.008902 -0.033417 -0.022277
			-0.006700 -0.026355 -0.025429
			-0.002167 -0.026926 -0.022370
			-0.003558 -0.028474 -0.021903
			-0.002533 -0.029459 -0.020678
			-0.005456 -0.031668 -0.020837
			-0.013815 -0.025067 -0.022343
			-0.014902 -0.026972 -0.019831
			-0.011736 -0.024530 -0.024965
			-0.010025 -0.024350 -0.027542
			-0.008699 -0.024955 -0.027509
			-0.012039 -0.022690 -0.017168
			-0.009591 -0.020332 -0.016713
			-0.005885 -0.025107 -0.017849
			-0.006876 -0.028655 -0.018262
			-0.004056 -0.030579 -0.019846
			-0.004471 -0.023036 -0.030596
			-0.001450 -0.027266 -0.020885
			-0.000578 -0.025557 -0.024296
			-0.000266 -0.025426 -0.022554
			-0.003290 -0.024116 -0.028294
			-0.002142 -0.026613 -0.018900
			-0.012207 -0.022703 -0.023746
			-0.011342 -0.020065 -0.022917
			-0.010156 -0.020327 -0.017999
			-0.010911 -0.022734 -0.027461
			-0.012453 -0.020541 -0.028862
			-0.008006 -0.024000 -0.030073
			-0.009687 -0.023024 -0.030425
			-0.009832 -0.019345 -0.017597
			-0.008492 -0.019202 -0.016823
			-0.004335 -0.021510 -0.016502
			 0.000023 -0.021811 -0.017076
			-0.001261 -0.024995 -0.019414
			-0.007879 -0.021915 -0.032817
			-0.002836 -0.021991 -0.031109
			-0.004730 -0.020990 -0.031989
			-0.000387 -0.023757 -0.021157
			 0.001183 -0.024175 -0.023534
			 0.001772 -0.022635 -0.022373
			-0.001471 -0.022517 -0.029116
			-0.011490 -0.020078 -0.025490
			-0.010737 -0.017821 -0.023901
			-0.010539 -0.018491 -0.020962
			-0.009885 -0.016895 -0.022929
			-0.012479 -0.019077 -0.027941
			-0.009851 -0.021245 -0.031520
			-0.012217 -0.018415 -0.027951
			-0.010476 -0.016731 -0.020158
			-0.007846 -0.017488 -0.018681
			-0.003628 -0.020184 -0.016119
			-0.004993 -0.016321 -0.020618
			 0.000639 -0.019188 -0.016258
			 0.000130 -0.021502 -0.018520
			 0.000467 -0.022136 -0.019816
			-0.007133 -0.019841 -0.031365
			-0.002452 -0.020227 -0.030090
			-0.005468 -0.019390 -0.030757
			 0.001275 -0.018865 -0.022414
			 0.002789 -0.021309 -0.023832
			-0.011664 -0.018398 -0.026580
			-0.011836 -0.017780 -0.027920
			-0.010229 -0.016254 -0.020862
			-0.008539 -0.015414 -0.023029
			-0.007883 -0.016026 -0.024376
			-0.010175 -0.017343 -0.029140
			-0.008338 -0.017966 -0.030325
			-0.003856 -0.014558 -0.022886
			-0.001233 -0.012933 -0.024164
			 0.000448 -0.014450 -0.021926
			 0.000777 -0.015797 -0.020333
			 0.000785 -0.015900 -0.021483
			-0.002013 -0.016116 -0.028774
			-0.004435 -0.018333 -0.030175
			-0.006851 -0.016590 -0.028912
			-0.005751 -0.016931 -0.029528
			 0.001087 -0.014687 -0.023347
			 0.002969 -0.015425 -0.024783
			 0.002597 -0.015487 -0.026211
			-0.009030 -0.016713 -0.028751
			-0.004832 -0.014509 -0.024840
			-0.004517 -0.015017 -0.026721
			-0.002484 -0.012955 -0.024647
			-0.000706 -0.012608 -0.025673
			-0.000075 -0.013117 -0.023707
			-0.004785 -0.016457 -0.028346
			-0.003131 -0.014810 -0.027747
			-0.000456 -0.013656 -0.027272
			 0.001373 -0.013173 -0.025719
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 2 3 
					0 4 1 
					5 4 0 
					6 0 3 
					0 6 7 
					5 0 7 
					1 8 2 
					9 8 1 
					4 9 1 
					8 10 2 
					3 2 10 
					3 10 11 
					3 12 6 
					12 3 11 
					4 13 9 
					4 14 13 
					5 14 4 
					5 7 15 
					5 15 16 
					14 5 16 
					17 6 12 
					6 18 19 
					6 19 20 
					6 20 21 
					7 6 16 
					16 6 21 
					17 18 6 
					16 15 7 
					10 8 22 
					23 22 8 
					8 9 23 
					13 23 9 
					22 24 10 
					10 24 11 
					11 24 25 
					26 11 25 
					26 12 11 
					26 17 12 
					23 13 27 
					14 27 13 
					14 28 27 
					14 29 28 
					30 29 14 
					31 14 21 
					31 30 14 
					14 16 21 
					32 18 17 
					26 32 17 
					19 18 33 
					34 35 18 
					34 18 36 
					18 32 36 
					18 35 33 
					33 20 19 
					31 20 37 
					20 33 37 
					21 20 31 
					38 24 22 
					23 39 22 
					22 39 38 
					23 27 40 
					23 40 39 
					41 24 38 
					25 24 41 
					25 41 42 
					43 25 44 
					43 26 25 
					25 42 44 
					26 43 32 
					27 28 40 
					40 28 45 
					46 45 28 
					29 47 28 
					46 28 47 
					30 37 29 
					29 48 47 
					48 29 49 
					37 49 29 
					30 31 37 
					32 43 50 
					32 51 36 
					50 52 32 
					51 32 52 
					37 33 49 
					33 53 49 
					53 33 35 
					34 54 35 
					36 54 34 
					35 55 53 
					55 35 54 
					54 36 56 
					51 56 36 
					57 41 38 
					58 57 38 
					58 38 39 
					59 39 40 
					39 59 60 
					39 60 58 
					40 45 59 
					61 42 41 
					61 41 57 
					62 42 63 
					63 42 61 
					62 44 42 
					50 43 44 
					50 44 62 
					45 64 59 
					45 46 65 
					64 45 65 
					66 67 46 
					46 67 65 
					47 66 46 
					47 68 66 
					68 47 48 
					49 69 48 
					68 48 69 
					70 69 49 
					49 53 70 
					71 50 62 
					50 71 52 
					52 72 51 
					72 56 51 
					52 71 73 
					52 73 72 
					55 74 53 
					53 74 70 
					55 54 75 
					75 54 56 
					55 75 74 
					56 72 75 
					57 76 61 
					57 58 76 
					76 58 77 
					60 77 58 
					59 64 78 
					60 59 78 
					60 78 79 
					80 60 79 
					60 80 77 
					61 76 63 
					62 63 81 
					62 81 82 
					62 82 71 
					76 77 63 
					63 77 81 
					65 78 64 
					65 67 78 
					66 68 67 
					78 67 83 
					67 84 83 
					85 84 67 
					67 68 86 
					85 67 86 
					86 68 87 
					87 68 69 
					87 69 74 
					70 74 69 
					82 73 71 
					75 72 88 
					72 89 88 
					89 72 73 
					73 82 90 
					73 91 89 
					73 90 91 
					92 87 74 
					75 93 74 
					92 74 93 
					88 94 75 
					93 75 94 
					77 95 81 
					95 77 80 
					78 83 79 
					83 96 79 
					80 79 96 
					80 97 95 
					80 96 97 
					82 81 95 
					90 82 95 
					84 98 83 
					83 98 96 
					99 98 84 
					100 99 84 
					85 100 84 
					86 87 85 
					87 92 85 
					92 100 85 
					101 88 89 
					102 103 88 
					88 103 94 
					88 101 102 
					101 89 91 
					97 90 95 
					101 90 97 
					90 101 91 
					93 104 92 
					92 104 100 
					104 93 94 
					94 103 104 
					97 96 98 
					103 97 99 
					102 101 97 
					97 103 102 
					97 98 99 
					103 99 104 
					99 100 104 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
