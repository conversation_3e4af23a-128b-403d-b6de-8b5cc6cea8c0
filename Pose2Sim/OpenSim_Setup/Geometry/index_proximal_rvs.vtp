<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="116" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="227">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.809324 -0.238921  0.536574
			-0.698867 -0.327655  0.635788
			-0.461884 -0.015337  0.886808
			-0.864227 -0.501899  0.034773
			-0.899010 -0.435865  0.042466
			-0.374093  0.154231  0.914477
			-0.724232 -0.349427  0.594465
			 0.033931 -0.073760  0.996699
			-0.467488 -0.605486  0.644082
			-0.738696 -0.663004  0.121468
			-0.050730  0.391337  0.918848
			-0.828252 -0.445657  0.339690
			-0.775586 -0.439179 -0.453417
			-0.796318 -0.424832 -0.430576
			-0.878982 -0.402115 -0.256309
			-0.699661 -0.713908 -0.028455
			-0.365063 -0.211176  0.906716
			 0.465735  0.138331  0.874046
			 0.283087  0.751198  0.596291
			-0.178485  0.786012  0.591885
			-0.074880 -0.722193  0.687627
			 0.339415 -0.543070  0.768032
			 0.457300 -0.254077  0.852245
			 0.616329  0.577756  0.535104
			 0.886566  0.107918  0.449838
			-0.199955 -0.977167  0.071850
			 0.287327  0.919225  0.269201
			-0.829723 -0.446243 -0.335300
			-0.356457 -0.269592  0.894572
			-0.272761 -0.421929  0.864626
			-0.810588 -0.492264  0.317212
			-0.344243 -0.319898 -0.882702
			-0.458094 -0.223174 -0.860432
			-0.242857  0.130782 -0.961206
			-0.720743 -0.583594 -0.374097
			-0.282722 -0.958626  0.033235
			 0.013554 -0.998537  0.052356
			 0.308706 -0.039329  0.950344
			 0.727316  0.159633  0.667480
			 0.886531  0.318217  0.335858
			 0.642465  0.764120  0.057959
			 0.405911 -0.884579  0.229686
			 0.866090 -0.321676  0.382638
			 0.782855 -0.595082  0.181704
			 0.789772  0.606025 -0.094836
			 0.990145  0.105926 -0.091613
			 0.566154 -0.817444 -0.106087
			 0.519192  0.837593 -0.169938
			-0.960285 -0.242448  0.138101
			-0.938664 -0.269615 -0.214982
			-0.763438 -0.244640 -0.597758
			 0.404519 -0.245864  0.880861
			-0.753896 -0.342605  0.560591
			-0.142547 -0.286500  0.947416
			 0.128894 -0.220455 -0.966843
			 0.418679  0.007175 -0.908106
			-0.048145 -0.207867 -0.976972
			 0.577833  0.213737 -0.787671
			-0.334231 -0.041381 -0.941582
			-0.118271  0.182313 -0.976101
			 0.108780 -0.410594 -0.905306
			-0.377509 -0.426025 -0.822186
			 0.065916  0.116082 -0.991050
			 0.385114 -0.006097 -0.922849
			 0.284855  0.641358 -0.712402
			 0.176598  0.763107 -0.621676
			-0.246351 -0.795010 -0.554320
			 0.455967 -0.880519 -0.129539
			 0.496733 -0.867672 -0.020045
			 0.897079  0.086847  0.433252
			 0.928000  0.236482 -0.287911
			 0.907497  0.407268 -0.102873
			 0.672459  0.739019 -0.040630
			 0.917507 -0.385899 -0.096247
			 0.826969  0.552859 -0.102322
			 0.997892 -0.064886 -0.001514
			 0.807664 -0.589154  0.024028
			-0.948730  0.316041 -0.005518
			-0.892508  0.444774  0.074863
			-0.833085  0.272960  0.481105
			-0.564674  0.062222 -0.822965
			-0.920690  0.207131 -0.330796
			 0.412733 -0.104319  0.904859
			 0.784656 -0.069990  0.615968
			-0.640029 -0.062900  0.765772
			 0.287970  0.150185  0.945789
			-0.162086  0.107846  0.980866
			 0.565750 -0.018777 -0.824363
			-0.113812  0.313174 -0.942851
			 0.342373  0.134004 -0.929959
			 0.708998  0.630460 -0.315979
			 0.573618  0.535772 -0.619606
			 0.755906 -0.417295 -0.504451
			 0.891724  0.051796 -0.449605
			 0.431930  0.882161 -0.187694
			 0.672873  0.736847 -0.065564
			 0.916696 -0.394223  0.065241
			 0.839235  0.510649  0.186875
			 0.857717  0.182209 -0.480751
			 0.962816  0.269655 -0.016451
			-0.636469  0.716055 -0.286656
			-0.497801  0.866154  0.044394
			-0.667944  0.742843  0.045101
			-0.423571  0.880031  0.214788
			-0.319104  0.497470  0.806657
			 0.673131  0.148113  0.724539
			 0.211148  0.457890  0.863570
			 0.527582  0.471548  0.706611
			-0.242081  0.885045 -0.397609
			 0.222938  0.534525 -0.815219
			 0.550746  0.624881 -0.553356
			 0.049496  0.998580  0.019717
			 0.454644  0.848837 -0.269769
			 0.138175  0.852639  0.503900
			-0.172201  0.983740 -0.051019
			 0.177343  0.865103 -0.469197
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.017906 -0.136291  0.023189
			 0.018744 -0.138557  0.023689
			 0.019025 -0.135168  0.024391
			 0.012101 -0.128174  0.019542
			 0.017361 -0.136156  0.021333
			 0.018914 -0.134444  0.024175
			 0.015407 -0.132276  0.022157
			 0.024450 -0.136713  0.025397
			 0.020801 -0.141051  0.023786
			 0.019680 -0.140809  0.022046
			 0.021551 -0.134310  0.025027
			 0.009299 -0.123015  0.020387
			 0.011666 -0.126005  0.017303
			 0.017876 -0.136064  0.017009
			 0.018679 -0.138737  0.017635
			 0.019617 -0.140549  0.019606
			 0.014782 -0.126729  0.024178
			 0.016705 -0.126226  0.024034
			 0.020694 -0.133519  0.023500
			 0.020570 -0.133892  0.024617
			 0.021621 -0.141356  0.023897
			 0.024092 -0.140310  0.024467
			 0.024655 -0.138599  0.024992
			 0.024897 -0.135115  0.024564
			 0.025831 -0.137304  0.024291
			 0.021316 -0.141764  0.022257
			 0.022622 -0.133608  0.024002
			 0.004746 -0.112919  0.016314
			 0.012389 -0.121230  0.024483
			 0.009171 -0.112493  0.026386
			 0.003927 -0.111470  0.022659
			 0.007657 -0.113507  0.014293
			 0.020290 -0.138961  0.015213
			 0.018721 -0.134695  0.015706
			 0.020429 -0.141016  0.016084
			 0.021733 -0.142173  0.017164
			 0.021425 -0.141576  0.019555
			 0.012139 -0.115072  0.025330
			 0.014007 -0.117588  0.024223
			 0.015408 -0.120538  0.023160
			 0.021326 -0.133245  0.021680
			 0.024027 -0.141171  0.023137
			 0.025754 -0.138301  0.024098
			 0.024862 -0.140272  0.023528
			 0.025150 -0.135529  0.021985
			 0.026020 -0.137595  0.021987
			 0.023845 -0.141127  0.020449
			 0.023183 -0.133915  0.022029
			 0.002022 -0.107642  0.021912
			 0.002435 -0.107910  0.015800
			 0.002907 -0.106327  0.013814
			 0.010243 -0.112381  0.026277
			 0.003498 -0.108867  0.025202
			 0.007508 -0.107590  0.028663
			 0.009280 -0.110776  0.013130
			 0.010926 -0.113198  0.013722
			 0.007680 -0.108747  0.012306
			 0.017625 -0.128646  0.015640
			 0.021436 -0.136457  0.014316
			 0.022187 -0.136196  0.014120
			 0.024476 -0.139832  0.014173
			 0.021608 -0.140980  0.015012
			 0.024170 -0.137361  0.013868
			 0.024632 -0.138139  0.013874
			 0.020313 -0.133909  0.015894
			 0.022073 -0.134576  0.014889
			 0.021762 -0.142009  0.015819
			 0.023740 -0.141799  0.015567
			 0.023708 -0.141013  0.019659
			 0.012527 -0.109837  0.024651
			 0.012037 -0.112294  0.016001
			 0.016675 -0.123003  0.020196
			 0.021250 -0.133472  0.019610
			 0.025852 -0.138443  0.021767
			 0.025102 -0.136787  0.019481
			 0.025384 -0.137737  0.019498
			 0.024450 -0.140250  0.019627
			 0.001732 -0.105580  0.018894
			 0.001773 -0.105514  0.021364
			 0.003939 -0.103609  0.026719
			 0.004139 -0.103953  0.012308
			 0.002306 -0.104116  0.014897
			 0.009959 -0.107132  0.028303
			 0.010824 -0.107269  0.027563
			 0.004339 -0.105198  0.027308
			 0.008160 -0.105052  0.028649
			 0.006752 -0.105267  0.028873
			 0.010463 -0.111578  0.013600
			 0.004899 -0.103350  0.012219
			 0.009008 -0.105208  0.012403
			 0.021191 -0.133433  0.017330
			 0.024177 -0.135908  0.014576
			 0.025054 -0.140213  0.014895
			 0.025471 -0.138091  0.014563
			 0.022644 -0.134213  0.017013
			 0.024489 -0.135425  0.016836
			 0.025561 -0.139874  0.017278
			 0.011648 -0.101844  0.024413
			 0.010763 -0.104564  0.013566
			 0.025895 -0.137692  0.017096
			 0.003591 -0.102520  0.013459
			 0.007107 -0.102246  0.018880
			 0.004184 -0.102453  0.025132
			 0.005844 -0.101538  0.026462
			 0.006074 -0.102570  0.027958
			 0.010584 -0.105984  0.027829
			 0.008600 -0.102662  0.027847
			 0.010419 -0.102395  0.026727
			 0.005202 -0.101789  0.013285
			 0.007971 -0.102561  0.012860
			 0.009758 -0.102599  0.013867
			 0.010138 -0.100530  0.023351
			 0.008453 -0.101388  0.014870
			 0.007969 -0.101064  0.026443
			 0.007688 -0.100981  0.015700
			 0.007163 -0.101416  0.013884
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 4 0 
					2 5 0 
					6 0 5 
					1 0 4 
					0 6 3 
					7 1 8 
					2 1 7 
					9 8 1 
					1 4 9 
					10 2 7 
					2 10 5 
					3 6 11 
					3 11 12 
					12 13 3 
					4 3 13 
					4 13 14 
					4 15 9 
					14 15 4 
					5 16 6 
					16 5 17 
					18 17 5 
					19 18 5 
					10 19 5 
					11 6 16 
					8 20 7 
					20 21 7 
					21 22 7 
					10 7 23 
					7 24 23 
					24 7 22 
					20 8 25 
					8 9 25 
					25 9 15 
					26 19 10 
					23 26 10 
					11 27 12 
					28 11 16 
					11 28 29 
					11 29 30 
					27 11 30 
					31 13 12 
					12 27 31 
					13 32 14 
					33 13 31 
					34 14 32 
					34 15 14 
					35 15 34 
					25 15 36 
					36 15 35 
					16 37 28 
					17 37 16 
					17 38 37 
					18 39 17 
					39 38 17 
					18 19 26 
					18 40 39 
					18 26 40 
					41 20 25 
					41 21 20 
					21 42 22 
					21 41 43 
					42 21 43 
					22 42 24 
					23 24 44 
					26 23 44 
					45 44 24 
					24 42 45 
					25 46 41 
					36 46 25 
					26 44 47 
					47 40 26 
					48 27 30 
					49 27 48 
					27 49 50 
					31 27 50 
					29 28 37 
					37 51 29 
					29 52 30 
					51 53 29 
					29 53 52 
					52 48 30 
					31 54 55 
					31 50 56 
					56 54 31 
					31 57 33 
					55 57 31 
					32 58 59 
					60 61 32 
					32 61 34 
					62 32 59 
					63 32 62 
					63 60 32 
					32 33 58 
					57 64 33 
					65 33 64 
					59 58 33 
					65 59 33 
					34 66 35 
					61 66 34 
					66 67 35 
					67 36 35 
					68 36 67 
					68 46 36 
					69 51 37 
					37 38 69 
					39 69 38 
					70 69 39 
					71 70 39 
					39 40 71 
					71 40 72 
					40 47 72 
					43 41 46 
					42 43 73 
					73 45 42 
					73 43 46 
					44 74 47 
					74 44 45 
					74 45 75 
					45 73 75 
					46 76 73 
					46 68 76 
					72 47 74 
					77 49 48 
					78 77 48 
					48 79 78 
					79 48 52 
					50 49 77 
					50 80 56 
					77 81 50 
					80 50 81 
					53 51 82 
					83 51 69 
					82 51 83 
					79 52 84 
					52 53 84 
					85 53 82 
					86 84 53 
					86 53 85 
					87 55 54 
					87 54 56 
					57 55 70 
					70 55 87 
					88 56 80 
					89 87 56 
					88 89 56 
					71 90 57 
					64 57 90 
					70 71 57 
					62 59 91 
					65 91 59 
					60 67 66 
					61 60 66 
					63 92 60 
					92 67 60 
					91 63 62 
					63 93 92 
					91 93 63 
					90 94 64 
					65 64 94 
					91 65 95 
					65 94 95 
					68 67 76 
					67 96 76 
					92 96 67 
					69 97 83 
					70 98 69 
					98 97 69 
					98 70 87 
					90 71 72 
					72 74 95 
					94 72 95 
					94 90 72 
					76 75 73 
					95 74 99 
					75 99 74 
					75 76 96 
					75 96 99 
					100 81 77 
					78 101 77 
					100 77 101 
					78 102 101 
					102 78 79 
					79 103 102 
					104 103 79 
					104 79 84 
					100 80 81 
					80 100 88 
					83 105 82 
					85 82 105 
					105 83 97 
					84 86 104 
					85 106 86 
					105 107 85 
					106 85 107 
					106 104 86 
					89 98 87 
					88 100 108 
					89 88 109 
					88 108 109 
					109 110 89 
					98 89 110 
					93 91 99 
					91 95 99 
					92 93 96 
					96 93 99 
					111 97 112 
					110 112 97 
					110 97 98 
					107 105 97 
					107 97 113 
					111 113 97 
					108 100 101 
					111 101 103 
					101 102 103 
					114 108 101 
					101 111 114 
					113 103 104 
					103 113 111 
					106 113 104 
					106 107 113 
					115 109 108 
					108 114 115 
					110 109 115 
					110 115 112 
					111 112 114 
					112 115 114 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
