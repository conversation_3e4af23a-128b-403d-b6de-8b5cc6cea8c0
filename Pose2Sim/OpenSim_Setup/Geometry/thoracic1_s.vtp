<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.450958 -0.892545  0.000305
			 0.227088 -0.958477  0.172493
			-0.297959 -0.954579  0.000013
			 0.909953 -0.324840  0.257809
			 0.890657 -0.454675  0.000199
			 0.227186 -0.958492 -0.172278
			 0.910044 -0.324789 -0.257552
			 0.285100 -0.842157  0.457700
			 0.048490 -0.683731  0.728121
			-0.615915 -0.787682 -0.014367
			-0.708712 -0.705498 -0.000010
			-0.649994 -0.743891  0.155349
			-0.376666 -0.603529  0.702763
			 0.285097 -0.842160 -0.457697
			 0.048488 -0.683735 -0.728118
			-0.615904 -0.787690  0.014342
			-0.649986 -0.743900 -0.155345
			-0.376663 -0.603536 -0.702758
			 0.998912 -0.046644  0.000149
			 0.953494 -0.000398  0.301412
			 0.719051 -0.037126  0.693965
			 0.953580 -0.000386 -0.301141
			 0.719052 -0.037129 -0.693964
			 0.301665  0.089105  0.949241
			-0.971938 -0.232974 -0.032545
			-0.998458  0.055520 -0.000010
			-0.971937 -0.232979  0.032549
			-0.272675  0.034997  0.961469
			-0.839146 -0.215148  0.499545
			-0.980443 -0.078787 -0.180343
			 0.301666  0.089113 -0.949240
			-0.839145 -0.215153 -0.499545
			-0.272676  0.035001 -0.961469
			-0.980445 -0.078780  0.180338
			 0.939492 -0.342571  0.000321
			 0.847124 -0.405856  0.343019
			 0.696388  0.035467  0.716789
			 0.847215 -0.405878 -0.342769
			 0.696387  0.035473 -0.716789
			 0.288454  0.099441  0.952316
			-0.888904  0.385953 -0.246756
			-0.885157  0.465293 -0.000068
			-0.888910  0.385956  0.246733
			-0.145181  0.068165  0.987054
			-0.260185 -0.505206  0.822843
			-0.865353 -0.268003 -0.423483
			 0.288452  0.099449 -0.952316
			-0.260186 -0.505209 -0.822841
			-0.145175  0.068159 -0.987056
			-0.865355 -0.268001  0.423480
			 0.908392 -0.418119  0.000188
			 0.839977 -0.454932  0.295764
			 0.478288 -0.434956  0.762925
			 0.840023 -0.454939 -0.295622
			 0.478281 -0.434955 -0.762929
			 0.101331 -0.373914  0.921911
			-0.876366  0.416900 -0.241200
			-0.337249  0.292135 -0.894942
			-0.926553  0.376164 -0.000138
			-0.876393  0.416890  0.241118
			-0.337245  0.292138  0.894942
			-0.124022 -0.299078  0.946135
			 0.239290  0.012621  0.970866
			-0.485453 -0.864090  0.132983
			-0.582396 -0.431434  0.688970
			 0.032166 -0.028380  0.999080
			 0.702956 -0.156350 -0.693836
			 0.101327 -0.373920 -0.921910
			 0.239287  0.012619 -0.970867
			-0.124020 -0.299083 -0.946133
			-0.582399 -0.431422 -0.688974
			-0.485457 -0.864088 -0.132982
			 0.032171 -0.028382 -0.999079
			 0.703130 -0.156346  0.693660
			 0.981939  0.189196  0.000156
			 0.902989  0.309391  0.298141
			 0.511252 -0.438564  0.739110
			 0.903026  0.309398 -0.298021
			 0.511249 -0.438565 -0.739111
			 0.044899 -0.398849  0.915917
			-0.200727  0.921082 -0.333641
			-0.272538  0.097699 -0.957172
			-0.426155  0.904650 -0.000212
			-0.200758  0.921123  0.333511
			-0.272534  0.097713  0.957171
			-0.225074 -0.099651  0.969233
			-0.202151  0.381955  0.901801
			 0.256979  0.689874  0.676783
			-0.378266 -0.490276  0.785203
			-0.551083 -0.409545  0.727035
			 0.419857 -0.273801  0.865305
			 0.682397 -0.575829  0.450283
			 0.227980  0.189448  0.955058
			-0.692369 -0.334464  0.639342
			 0.915159 -0.403093 -0.000233
			 0.524923 -0.732192 -0.433993
			-0.186296  0.982494  0.000034
			-0.238460  0.653910 -0.718010
			 0.044897 -0.398857 -0.915914
			-0.202151  0.381955 -0.901801
			-0.225074 -0.099651 -0.969233
			 0.256976  0.689875 -0.676783
			-0.551083 -0.409547 -0.727034
			-0.378400 -0.489507 -0.785619
			 0.419882 -0.273792 -0.865296
			 0.682401 -0.575829 -0.450278
			-0.692365 -0.334478 -0.639339
			 0.227982  0.189443 -0.955058
			 0.524894 -0.732251  0.433931
			-0.238369  0.653918  0.718033
			 0.379232  0.925301 -0.000003
			 0.723695  0.486667  0.489307
			 0.453850  0.362064  0.814205
			 0.723692  0.486675 -0.489302
			 0.453852  0.362067 -0.814203
			-0.088922  0.478123  0.873780
			 0.218082  0.967530 -0.127775
			 0.341102  0.880784  0.328435
			-0.412369  0.760210 -0.502029
			 0.117553  0.929374  0.349921
			 0.341102  0.880783 -0.328438
			 0.218322  0.967440  0.128047
			-0.412369  0.760210  0.502029
			 0.117567  0.929375 -0.349915
			 0.531573  0.466208  0.707163
			 0.597849  0.476001  0.644980
			 0.706155  0.141037  0.693869
			-0.128278 -0.820643  0.556857
			 0.631049  0.017823  0.775538
			-0.716350  0.542862  0.438342
			-0.117051  0.232556  0.965514
			-0.897372 -0.362544 -0.251565
			-0.794588  0.543099 -0.271429
			 0.783329 -0.540472  0.307061
			 0.013246  0.259274  0.965713
			 0.117066 -0.048847  0.991922
			-0.958012 -0.141919  0.249141
			-0.865018  0.459583 -0.201314
			-0.272515 -0.962152 -0.000125
			-0.387572 -0.848031 -0.361430
			-0.676449  0.736489  0.000185
			-0.308800  0.890277  0.334736
			-0.565227  0.808419  0.164246
			-0.748260  0.632569 -0.199910
			-0.308612  0.890409 -0.334561
			-0.565089  0.808549 -0.164082
			-0.748247  0.632560  0.199983
			-0.677464  0.546227 -0.492623
			-0.088917  0.478126 -0.873779
			 0.531574  0.466215 -0.707158
			 0.597421  0.476460 -0.645038
			-0.117048  0.232552 -0.965515
			-0.716351  0.542860 -0.438343
			-0.126823 -0.819222 -0.559278
			-0.896413 -0.365496  0.250710
			-0.794588  0.543099  0.271429
			 0.704724  0.143702 -0.694777
			 0.630540  0.019432 -0.775913
			 0.783327 -0.540473 -0.307064
			 0.013251  0.259272 -0.965713
			-0.958010 -0.141923 -0.249147
			 0.117070 -0.048851 -0.991921
			-0.865023  0.459581  0.201300
			-0.387489 -0.848088  0.361386
			-0.677464  0.546227  0.492623
			-0.668252  0.665143 -0.333203
			-0.620389  0.327470 -0.712658
			-0.668608  0.663807  0.335148
			-0.620566  0.324725  0.713758
			-0.709542 -0.008609  0.704610
			-0.863632 -0.501567  0.050692
			-0.787581  0.474934  0.392624
			-0.928075 -0.089256  0.361540
			-0.841624 -0.358117  0.404253
			-0.644879  0.727882  0.233063
			 0.169791 -0.951581  0.256252
			-0.037693 -0.959119 -0.280482
			 0.602053 -0.798456 -0.000042
			-0.037724 -0.959144  0.280391
			-0.611791  0.623071  0.487334
			-0.745681  0.666299 -0.002241
			-0.743980  0.406339  0.530454
			-0.611843  0.623083 -0.487254
			-0.743637  0.406606 -0.530731
			-0.644880  0.727882 -0.233058
			-0.928073 -0.089260 -0.361542
			-0.787584  0.474928 -0.392625
			-0.841625 -0.358118 -0.404252
			-0.709257 -0.006058 -0.704924
			-0.862873 -0.502694 -0.052436
			 0.169795 -0.951580 -0.256252
			-0.659459  0.280863  0.697302
			-0.175571 -0.919618  0.351394
			-0.334796 -0.906082 -0.258702
			 0.489644 -0.871922 -0.000111
			-0.334794 -0.906082  0.258705
			-0.171907 -0.917978 -0.357441
			-0.532021  0.846726 -0.002957
			-0.660302  0.278965 -0.697266
			-0.128032  0.233936  0.963785
			-0.094717 -0.032530  0.994973
			 0.396170 -0.785417  0.475572
			-0.078771 -0.996892 -0.000980
			 0.392929 -0.784096 -0.480418
			-0.104168 -0.030049 -0.994106
			-0.761112  0.342143  0.551041
			-0.982727  0.185056 -0.000947
			-0.133987  0.234390 -0.962865
			-0.760418  0.341148 -0.552614
			-0.488744 -0.576372  0.654923
			-0.801291 -0.598274 -0.001099
			-0.487323 -0.576441 -0.655921
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.020033  0.426599  0.000106
			-0.020508  0.427021  0.007257
			-0.030295  0.430278  0.000087
			-0.018651  0.429664  0.008689
			-0.019065  0.427530  0.000108
			-0.020508  0.427021 -0.007088
			-0.018651  0.429664 -0.008520
			-0.023478  0.428551  0.014698
			-0.028392  0.430510  0.018318
			-0.039651  0.432953  0.004397
			-0.039686  0.433201  0.000087
			-0.039869  0.433240  0.012780
			-0.036330  0.433081  0.018416
			-0.023478  0.428551 -0.014529
			-0.028392  0.430510 -0.018149
			-0.039651  0.432953 -0.004228
			-0.039869  0.433240 -0.012611
			-0.036330  0.433081 -0.018247
			-0.018544  0.431146  0.000108
			-0.019421  0.431861  0.009296
			-0.022330  0.431717  0.015610
			-0.019421  0.431861 -0.009127
			-0.022330  0.431717 -0.015441
			-0.028108  0.434477  0.018942
			-0.041746  0.436541  0.004523
			-0.041748  0.436436  0.000087
			-0.041746  0.436541 -0.004354
			-0.036100  0.437409  0.019076
			-0.041917  0.434497  0.014342
			-0.042130  0.437874  0.008520
			-0.028108  0.434477 -0.018773
			-0.041917  0.434497 -0.014173
			-0.036100  0.437409 -0.018907
			-0.042130  0.437874 -0.008351
			-0.016240  0.438269  0.000110
			-0.017512  0.437882  0.008975
			-0.023086  0.434370  0.015223
			-0.017512  0.437882 -0.008806
			-0.023086  0.434370 -0.015053
			-0.027260  0.437640  0.017240
			-0.039991  0.441844  0.005776
			-0.039620  0.441719  0.000089
			-0.039991  0.441844 -0.005607
			-0.035211  0.440117  0.018059
			-0.039426  0.440505  0.017080
			-0.041233  0.442811  0.008789
			-0.027260  0.437640 -0.017071
			-0.039426  0.440505 -0.016911
			-0.035211  0.440117 -0.017890
			-0.041233  0.442811 -0.008620
			-0.014991  0.440953  0.000111
			-0.015532  0.440850  0.009919
			-0.021693  0.440026  0.015804
			-0.015532  0.440850 -0.009750
			-0.021693  0.440026 -0.015635
			-0.025270  0.441601  0.017319
			-0.036599  0.447753  0.005965
			-0.037955  0.448663  0.009186
			-0.036042  0.447610  0.000091
			-0.036599  0.447753 -0.005796
			-0.037955  0.448663 -0.009017
			-0.032611  0.444933  0.018188
			-0.035888  0.447085  0.018540
			-0.043317  0.441676  0.015396
			-0.041299  0.443215  0.018856
			-0.038654  0.447131  0.022531
			-0.046069  0.446245  0.003425
			-0.025270  0.441601 -0.017150
			-0.035888  0.447085 -0.018371
			-0.032611  0.444933 -0.018019
			-0.041299  0.443215 -0.018687
			-0.043317  0.441676 -0.015227
			-0.038654  0.447131 -0.022362
			-0.046069  0.446245 -0.003256
			-0.014136  0.444175  0.000112
			-0.014801  0.444254  0.010099
			-0.019443  0.442142  0.016724
			-0.014801  0.444254 -0.009930
			-0.019443  0.442142 -0.016555
			-0.024061  0.443624  0.019140
			-0.035410  0.453103  0.007582
			-0.036341  0.452099  0.009857
			-0.034394  0.451942  0.000093
			-0.035410  0.453103 -0.007413
			-0.036341  0.452099 -0.009688
			-0.030058  0.447337  0.020059
			-0.033619  0.448717  0.017919
			-0.037968  0.451517  0.017961
			-0.047635  0.449794  0.020805
			-0.047209  0.446263  0.015483
			-0.046640  0.441057  0.022248
			-0.045354  0.440350  0.009522
			-0.043954  0.447468  0.021401
			-0.046707  0.442916  0.019463
			-0.047937  0.442491  0.000088
			-0.052984  0.434790  0.004667
			-0.052128  0.449554  0.000089
			-0.046437  0.451167  0.007000
			-0.024061  0.443624 -0.018971
			-0.033619  0.448717 -0.017750
			-0.030058  0.447337 -0.019890
			-0.037968  0.451517 -0.017792
			-0.047209  0.446263 -0.015314
			-0.047635  0.449794 -0.020636
			-0.046640  0.441057 -0.022079
			-0.045354  0.440350 -0.009353
			-0.046707  0.442916 -0.019294
			-0.043954  0.447468 -0.021232
			-0.052984  0.434790 -0.004498
			-0.046437  0.451167 -0.006831
			-0.025291  0.448960  0.000093
			-0.018526  0.445639  0.016954
			-0.023589  0.447896  0.020060
			-0.018526  0.445639 -0.016785
			-0.023589  0.447896 -0.019891
			-0.030285  0.450333  0.020183
			-0.043651  0.457370  0.014420
			-0.037359  0.452727  0.012722
			-0.044046  0.455383  0.009102
			-0.034126  0.452177  0.015423
			-0.037359  0.452727 -0.012553
			-0.043651  0.457370 -0.014252
			-0.044046  0.455383 -0.008932
			-0.034126  0.452177 -0.015254
			-0.044495  0.452012  0.019111
			-0.043119  0.453952  0.016566
			-0.058433  0.465633  0.041375
			-0.051195  0.452342  0.018983
			-0.046313  0.453941  0.019146
			-0.057712  0.441461  0.011412
			-0.054421  0.441909  0.013951
			-0.055944  0.455027  0.022779
			-0.050485  0.449970  0.011445
			-0.046176  0.439032  0.010835
			-0.058438  0.438552  0.015358
			-0.046717  0.448779  0.023661
			-0.048363  0.447672  0.021585
			-0.047644  0.452984  0.021889
			-0.053881  0.439167  0.000087
			-0.052996  0.433989  0.006090
			-0.062576  0.443010  0.000088
			-0.053657  0.447338  0.004527
			-0.051025  0.445504  0.007522
			-0.050096  0.448555  0.009406
			-0.053657  0.447338 -0.004358
			-0.051025  0.445504 -0.007353
			-0.050096  0.448555 -0.009237
			-0.047127  0.451838  0.010256
			-0.030285  0.450333 -0.020014
			-0.044495  0.452012 -0.018942
			-0.043119  0.453952 -0.016397
			-0.054421  0.441909 -0.013782
			-0.057712  0.441461 -0.011243
			-0.051195  0.452342 -0.018814
			-0.055944  0.455027 -0.022610
			-0.050485  0.449970 -0.011276
			-0.058433  0.465633 -0.041060
			-0.046313  0.453941 -0.018977
			-0.046176  0.439032 -0.010666
			-0.058438  0.438552 -0.015189
			-0.048363  0.447672 -0.021416
			-0.046717  0.448779 -0.023492
			-0.047644  0.452984 -0.021720
			-0.052996  0.433989 -0.005921
			-0.047127  0.451838 -0.010087
			-0.063194  0.464971  0.034705
			-0.049638  0.454219  0.012560
			-0.063194  0.464971 -0.034390
			-0.049638  0.454219 -0.012391
			-0.062164  0.463421  0.045827
			-0.063957  0.462308  0.035645
			-0.060071  0.440147  0.009280
			-0.061151  0.437027  0.008785
			-0.059774  0.433896  0.015140
			-0.057173  0.442879  0.008015
			-0.054326  0.432731  0.012019
			-0.054103  0.436863  0.004860
			-0.056627  0.435671  0.000086
			-0.054103  0.436863 -0.004691
			-0.060908  0.444331  0.003836
			-0.070253  0.436287  0.000070
			-0.061613  0.441095  0.003734
			-0.060908  0.444331 -0.003667
			-0.061613  0.441095 -0.003565
			-0.057173  0.442879 -0.007846
			-0.061151  0.437027 -0.008616
			-0.060071  0.440147 -0.009111
			-0.059774  0.433896 -0.014972
			-0.062164  0.463421 -0.045512
			-0.063957  0.462308 -0.035330
			-0.054326  0.432731 -0.011850
			-0.062650  0.438136  0.003892
			-0.062384  0.436380  0.004548
			-0.058913  0.434024  0.006929
			-0.063299  0.432885  0.000084
			-0.058913  0.434024 -0.006760
			-0.062384  0.436380 -0.004379
			-0.076335  0.430459  0.000108
			-0.062650  0.438136 -0.003723
			-0.071974  0.428231  0.003010
			-0.065931  0.431928  0.004242
			-0.074069  0.422896  0.002374
			-0.074607  0.422846  0.000124
			-0.074069  0.422896 -0.002120
			-0.065931  0.431928 -0.004073
			-0.078720  0.428495  0.003042
			-0.079572  0.427736  0.000113
			-0.071974  0.428231 -0.002756
			-0.078720  0.428495 -0.002788
			-0.078229  0.425530  0.003521
			-0.077144  0.423857  0.000108
			-0.078229  0.425530 -0.003267
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
