<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="83" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="162">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.258180  0.781421  0.568088
			-0.324815  0.084002  0.942040
			 0.121126 -0.174726  0.977138
			 0.257943  0.806183  0.532479
			-0.853329  0.479610  0.204459
			-0.628527  0.773796 -0.078695
			-0.039496  0.919321  0.391521
			-0.740014 -0.398208  0.542043
			 0.272477 -0.766924  0.581019
			 0.302514 -0.788904  0.534898
			 0.289927 -0.025192  0.956717
			 0.187781 -0.695542  0.693513
			 0.542471  0.495205  0.678600
			 0.816589  0.469746  0.335442
			 0.758921  0.651023  0.014417
			 0.275945  0.902133  0.331678
			-0.916764  0.231952 -0.325179
			-0.255015  0.961583 -0.101617
			-0.459553  0.671144 -0.581702
			-0.750053  0.366918 -0.550265
			 0.174452  0.931814  0.318259
			-0.945360  0.178554 -0.272788
			-0.704388 -0.683895  0.190065
			-0.325644 -0.892909  0.310919
			 0.608309  0.103392  0.786937
			 0.458327 -0.474901  0.751270
			 0.265087 -0.568289  0.778959
			 0.696003 -0.431218  0.574135
			 0.983421  0.165292  0.074578
			 0.838952 -0.026542  0.543557
			 0.691305  0.576722 -0.435303
			 0.726768  0.353360 -0.589021
			 0.467102  0.882631 -0.052713
			 0.990814  0.088974  0.101840
			-0.809548 -0.076952 -0.581988
			-0.720434  0.155845 -0.675786
			-0.277891  0.703034 -0.654614
			-0.116343  0.904435 -0.410440
			 0.068339  0.996046 -0.056767
			-0.412986  0.398628 -0.818864
			-0.638631 -0.145249 -0.755681
			-0.796091 -0.026515 -0.604596
			-0.714971 -0.283249 -0.639208
			-0.554803 -0.831911 -0.010862
			-0.115021 -0.750945  0.650271
			 0.603702 -0.304887  0.736605
			-0.063231 -0.551945  0.831480
			 0.150006  0.824335 -0.545866
			 0.244316  0.327248 -0.912808
			 0.057236  0.561976 -0.825171
			 0.748137  0.100312 -0.655918
			 0.256675 -0.156895 -0.953678
			 0.223038  0.139402 -0.964791
			 0.895071 -0.160173 -0.416164
			 0.983231 -0.168160 -0.070558
			-0.512599  0.028200 -0.858165
			-0.340058  0.096125 -0.935479
			-0.507988 -0.373055 -0.776388
			-0.452072 -0.877814  0.158344
			-0.487862  0.460183 -0.741769
			-0.385791 -0.894229 -0.226980
			-0.599170 -0.379411 -0.705012
			 0.810044 -0.502707  0.301851
			 0.286329 -0.519749  0.804908
			-0.272153 -0.875004  0.400376
			-0.144201 -0.941768  0.303776
			 0.081101  0.189026 -0.978617
			-0.165698  0.274456 -0.947216
			 0.380884 -0.486706 -0.786158
			-0.092989 -0.868000 -0.487780
			 0.015638 -0.608559 -0.793354
			 0.134202  0.133576 -0.981910
			 0.755615 -0.476475 -0.449464
			 0.460053 -0.705847 -0.538637
			 0.731715 -0.628895 -0.262837
			-0.615556  0.338610 -0.711642
			-0.702892  0.049703 -0.709558
			-0.563189  0.508228 -0.651554
			-0.225069  0.019410 -0.974150
			 0.380698 -0.915478  0.130264
			 0.239338 -0.907714 -0.344635
			-0.268015  0.438983 -0.857591
			-0.711139  0.522322 -0.470597
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.006227 -0.005999 -0.019078
			-0.008368 -0.012117 -0.013576
			-0.002791 -0.010802 -0.013712
			-0.001091 -0.006372 -0.017515
			-0.009177 -0.009207 -0.017151
			-0.006400 -0.005650 -0.020172
			-0.003519 -0.005579 -0.019102
			-0.009708 -0.014488 -0.015533
			-0.007909 -0.012536 -0.014026
			-0.007617 -0.012699 -0.014688
			-0.000987 -0.010584 -0.014097
			-0.000494 -0.012897 -0.016195
			 0.001978 -0.010222 -0.015438
			 0.003013 -0.010693 -0.016508
			-0.000791 -0.005790 -0.020325
			-0.001689 -0.005491 -0.019841
			-0.009096 -0.011887 -0.020008
			-0.005218 -0.005167 -0.020918
			-0.004644 -0.005772 -0.022469
			-0.006913 -0.007970 -0.022782
			-0.002003 -0.005135 -0.020423
			-0.010015 -0.014486 -0.019839
			-0.010097 -0.016565 -0.018456
			-0.009056 -0.017487 -0.020225
			 0.002216 -0.010920 -0.015356
			 0.002385 -0.011413 -0.015601
			 0.003258 -0.016033 -0.020187
			 0.003239 -0.012300 -0.017176
			 0.003476 -0.011808 -0.017776
			 0.002814 -0.011144 -0.015953
			-0.000745 -0.006716 -0.023729
			 0.002424 -0.016092 -0.027901
			-0.001315 -0.005069 -0.021285
			 0.003732 -0.015708 -0.020432
			-0.007653 -0.011263 -0.022316
			-0.008356 -0.012954 -0.021333
			-0.003552 -0.005604 -0.022888
			-0.002989 -0.005079 -0.022427
			-0.002607 -0.004869 -0.021445
			-0.004808 -0.009255 -0.025027
			-0.006451 -0.011171 -0.024242
			-0.009092 -0.016033 -0.022175
			-0.007076 -0.016816 -0.023808
			-0.008198 -0.018093 -0.021001
			-0.006940 -0.018561 -0.021244
			 0.004109 -0.019388 -0.022363
			-0.003027 -0.020575 -0.022666
			-0.002104 -0.005485 -0.023104
			 0.001621 -0.014616 -0.027841
			-0.001692 -0.007977 -0.025261
			 0.003153 -0.017429 -0.027982
			 0.002323 -0.018405 -0.028242
			 0.002073 -0.016547 -0.028148
			 0.003779 -0.020365 -0.026713
			 0.004536 -0.021513 -0.024183
			-0.005184 -0.013940 -0.023544
			-0.000978 -0.011626 -0.027022
			-0.003518 -0.012404 -0.025346
			-0.004869 -0.020067 -0.022683
			-0.004911 -0.016507 -0.025546
			-0.002708 -0.021179 -0.023933
			-0.002433 -0.019899 -0.028090
			 0.004300 -0.022027 -0.023604
			 0.003804 -0.022214 -0.023276
			-0.002635 -0.021163 -0.023152
			 0.002064 -0.022265 -0.023175
			 0.001685 -0.017078 -0.028214
			-0.000290 -0.014894 -0.027606
			 0.003351 -0.020710 -0.027076
			 0.000956 -0.021507 -0.025801
			-0.000378 -0.020255 -0.028184
			 0.000936 -0.017747 -0.028623
			 0.004065 -0.021356 -0.025394
			 0.003565 -0.021688 -0.025807
			 0.004235 -0.022046 -0.024398
			-0.002814 -0.015117 -0.023811
			-0.001664 -0.013749 -0.026419
			-0.003015 -0.016826 -0.027882
			-0.002162 -0.018023 -0.028592
			 0.003819 -0.022497 -0.023781
			 0.003651 -0.022426 -0.024456
			-0.000637 -0.016111 -0.028194
			-0.002091 -0.015488 -0.026291
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 0 2 
					0 4 1 
					5 4 0 
					0 3 6 
					6 5 0 
					1 4 7 
					2 1 8 
					8 1 7 
					2 8 9 
					2 10 3 
					10 2 11 
					9 11 2 
					3 12 13 
					3 13 14 
					3 14 15 
					3 15 6 
					10 12 3 
					7 4 16 
					16 4 5 
					17 18 5 
					17 5 6 
					19 16 5 
					19 5 18 
					17 6 20 
					20 6 15 
					16 21 7 
					21 22 7 
					9 8 7 
					22 9 7 
					22 23 9 
					9 23 11 
					10 24 12 
					11 25 10 
					10 25 24 
					23 26 11 
					27 25 11 
					27 11 26 
					24 13 12 
					14 13 28 
					28 13 29 
					29 13 24 
					30 14 31 
					32 14 30 
					32 20 14 
					20 15 14 
					14 33 31 
					33 14 28 
					16 19 34 
					35 21 16 
					34 35 16 
					36 18 17 
					37 36 17 
					17 38 37 
					17 20 38 
					18 39 19 
					36 39 18 
					40 34 19 
					39 40 19 
					38 20 32 
					41 22 21 
					35 41 21 
					23 22 41 
					23 42 43 
					23 44 26 
					41 42 23 
					23 43 44 
					25 29 24 
					25 27 29 
					45 33 26 
					44 46 26 
					26 33 27 
					45 26 46 
					29 27 28 
					33 28 27 
					30 47 32 
					30 31 48 
					47 30 49 
					49 30 48 
					31 50 51 
					48 31 52 
					31 51 52 
					50 31 33 
					38 32 47 
					50 33 53 
					54 53 33 
					54 33 45 
					34 55 35 
					55 34 40 
					55 41 35 
					47 36 37 
					49 36 47 
					39 36 49 
					38 47 37 
					56 39 49 
					56 40 39 
					40 57 55 
					56 57 40 
					41 55 42 
					58 43 42 
					42 55 59 
					60 58 42 
					61 60 42 
					59 61 42 
					43 58 44 
					58 46 44 
					45 62 54 
					63 62 45 
					46 63 45 
					64 65 46 
					65 63 46 
					64 46 58 
					66 67 48 
					66 48 52 
					48 56 49 
					48 67 56 
					51 50 68 
					53 68 50 
					68 69 51 
					51 66 52 
					70 51 69 
					71 51 70 
					66 51 71 
					54 72 53 
					68 53 73 
					53 72 73 
					62 74 54 
					74 72 54 
					75 59 55 
					75 55 57 
					76 57 56 
					67 76 56 
					76 75 57 
					60 64 58 
					59 77 61 
					75 77 59 
					61 70 60 
					64 60 65 
					70 69 60 
					69 65 60 
					77 78 61 
					61 78 70 
					63 79 62 
					79 74 62 
					79 63 65 
					69 80 65 
					80 79 65 
					67 66 71 
					81 76 67 
					67 71 81 
					68 73 69 
					69 73 80 
					71 70 78 
					78 81 71 
					73 72 80 
					74 80 72 
					79 80 74 
					77 75 82 
					82 75 76 
					81 82 76 
					77 82 81 
					78 77 81 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
