<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="137" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="270">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.200079 -0.374467 -0.905397
			 0.145013 -0.243089 -0.959103
			-0.010725 -0.512819 -0.858429
			-0.469645 -0.416352 -0.778515
			 0.020614 -0.005991 -0.999770
			-0.235307 -0.204963 -0.950064
			 0.541930 -0.544645 -0.640058
			 0.648075  0.457977 -0.608487
			 0.897174 -0.015783 -0.441394
			-0.525903 -0.626268 -0.575512
			-0.181907 -0.939706 -0.289590
			 0.481676 -0.874666  0.054297
			-0.827824 -0.560931  0.008001
			-0.786406 -0.476822 -0.392691
			-0.048902  0.652865 -0.755894
			-0.299317  0.418511 -0.857472
			 0.316671  0.844111 -0.432662
			 0.161236  0.692562 -0.703108
			-0.279310 -0.331937 -0.901001
			 0.184465 -0.141882 -0.972544
			 0.877742 -0.448693 -0.168057
			 0.784476  0.619178 -0.034863
			 0.988706  0.073502  0.130604
			-0.664370 -0.746178 -0.042789
			-0.259746 -0.959024  0.113154
			 0.865042 -0.431563  0.255844
			 0.535628 -0.839125  0.094722
			 0.016260 -0.984672  0.173658
			-0.890502 -0.451704  0.054495
			-0.871103 -0.487760 -0.057177
			-0.638751 -0.571714 -0.514917
			-0.304439 -0.366779 -0.879085
			-0.883263 -0.438734  0.165403
			 0.503599  0.863926 -0.004426
			 0.592825  0.790447 -0.154118
			 0.770310  0.260103 -0.582210
			 0.211048 -0.159029 -0.964453
			 0.809940  0.585714  0.030613
			 0.980350 -0.187729  0.060597
			-0.742702 -0.667698  0.050722
			-0.470572 -0.866354  0.167311
			 0.871958 -0.489573  0.002747
			 0.581749 -0.794896  0.172362
			 0.228689 -0.940958  0.249599
			-0.185357 -0.979305  0.081264
			-0.798924 -0.549216 -0.245117
			-0.718762 -0.269582  0.640864
			-0.880553 -0.448710  0.152597
			-0.134239 -0.463976 -0.875618
			-0.648425 -0.561607 -0.513948
			-0.813971 -0.560939 -0.150993
			 0.706866  0.037418 -0.706357
			-0.740167 -0.571461  0.354380
			-0.938858 -0.291167  0.183758
			-0.838957 -0.268199  0.473519
			 0.544095  0.838835 -0.017818
			 0.617275  0.423426  0.663085
			 0.641989  0.766670  0.008177
			 0.991050  0.133294  0.007291
			 0.656243  0.729769  0.191787
			 0.801606 -0.418382  0.427065
			 0.432910 -0.694637  0.574515
			-0.245435 -0.800465  0.546825
			 0.135556 -0.661513  0.737580
			-0.762057 -0.328440  0.558029
			-0.441553 -0.015224  0.897106
			-0.301910 -0.125529  0.945036
			-0.975764 -0.214814 -0.041717
			-0.898726 -0.370399  0.234726
			 0.130657 -0.564069 -0.815325
			-0.485515 -0.588389 -0.646586
			 0.470366 -0.318601 -0.822951
			 0.363039 -0.211372 -0.907483
			-0.825230 -0.469290 -0.314266
			 0.940338  0.194955  0.278848
			 0.876176 -0.091949 -0.473140
			-0.804008 -0.232787  0.547157
			-0.371373 -0.286039  0.883326
			-0.910519 -0.176595  0.373858
			-0.714922 -0.206154  0.668122
			-0.704072 -0.125575  0.698937
			 0.271640  0.661321  0.699189
			 0.354833  0.217792  0.909209
			 0.190717  0.809912  0.554680
			 0.761787  0.199592  0.616314
			 0.157179  0.361720  0.918942
			 0.204837 -0.245609  0.947480
			-0.909118 -0.159733  0.384695
			-0.455788 -0.248163  0.854793
			-0.344293  0.283288  0.895103
			-0.037318 -0.096764  0.994607
			-0.788806 -0.379673 -0.483358
			-0.928623 -0.233540  0.288302
			-0.985747  0.117975  0.119934
			-0.833691  0.529107 -0.158132
			-0.977564 -0.072447  0.197787
			-0.892141  0.364578 -0.266772
			-0.920264 -0.234939  0.312918
			-0.364708 -0.566470 -0.738986
			 0.072600 -0.509010 -0.857694
			 0.386742 -0.218700 -0.895880
			 0.668323 -0.298358 -0.681415
			 0.975524  0.090969 -0.200194
			 0.416409  0.088545  0.904855
			 0.837800  0.282274  0.467346
			-0.416865 -0.076093  0.905778
			-0.291704  0.150400  0.944610
			 0.174110  0.145493  0.973919
			-0.927646  0.312259  0.204862
			-0.708360 -0.004637  0.705836
			-0.111048 -0.051963  0.992456
			-0.700584 -0.150543 -0.697509
			-0.733716  0.668196  0.123186
			-0.194244  0.969122 -0.151895
			-0.714223  0.260335 -0.649701
			-0.530873  0.769201 -0.355673
			-0.310431 -0.297597 -0.902812
			-0.111443 -0.029653 -0.993328
			 0.445879  0.419411 -0.790751
			 0.005782  0.307202 -0.951627
			 0.746449  0.002703 -0.665437
			 0.787804  0.566807 -0.241029
			 0.234214  0.509775  0.827812
			 0.619571  0.687671  0.378470
			 0.742795  0.666361  0.064948
			-0.468979  0.240997  0.849693
			-0.132503  0.365550  0.921312
			-0.473259  0.083406 -0.876966
			-0.194491  0.973297 -0.121928
			-0.364471  0.778065  0.511640
			-0.314240  0.931314 -0.184139
			 0.422042  0.906073 -0.030198
			 0.150152  0.877455 -0.455551
			-0.386403  0.388286 -0.836616
			-0.173413  0.717276 -0.674865
			 0.012641  0.940852  0.338581
			 0.385873  0.905718  0.175435
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.015775 -0.116212  0.040419
			 0.017964 -0.115112  0.038993
			 0.017104 -0.118801  0.040698
			 0.012575 -0.116619  0.040680
			 0.016220 -0.113480  0.038647
			 0.012883 -0.113091  0.039166
			 0.018001 -0.117695  0.040090
			 0.018411 -0.113550  0.039467
			 0.019205 -0.115383  0.040075
			 0.013766 -0.118370  0.041032
			 0.015253 -0.119120  0.041025
			 0.017521 -0.118845  0.041714
			 0.013068 -0.117739  0.041662
			 0.008726 -0.109109  0.040509
			 0.014445 -0.112588  0.038843
			 0.013759 -0.112848  0.038864
			 0.016335 -0.112182  0.039608
			 0.014551 -0.112054  0.039768
			 0.009335 -0.106533  0.038169
			 0.010289 -0.106414  0.038156
			 0.018898 -0.117153  0.040767
			 0.018591 -0.113437  0.041783
			 0.019333 -0.115213  0.042131
			 0.013584 -0.118333  0.041684
			 0.014899 -0.119126  0.042502
			 0.018888 -0.116730  0.042743
			 0.017059 -0.117859  0.044672
			 0.014928 -0.118401  0.044815
			 0.013067 -0.117364  0.042349
			 0.004112 -0.102624  0.041538
			 0.002863 -0.097950  0.037875
			 0.007264 -0.101823  0.037071
			 0.012696 -0.116797  0.044479
			 0.016820 -0.112096  0.041441
			 0.015110 -0.111486  0.041543
			 0.012172 -0.107777  0.038878
			 0.010169 -0.103958  0.037439
			 0.018068 -0.113276  0.044020
			 0.018569 -0.115656  0.044412
			 0.013379 -0.118239  0.042493
			 0.013915 -0.118081  0.044722
			 0.018764 -0.116366  0.046591
			 0.017117 -0.117938  0.047375
			 0.016106 -0.118234  0.047634
			 0.014877 -0.118458  0.045983
			 0.002244 -0.098009  0.038969
			 0.006262 -0.104716  0.044079
			 0.002044 -0.097902  0.041162
			 0.005466 -0.097050  0.035345
			 0.000493 -0.094776  0.036094
			 0.000042 -0.094687  0.037366
			 0.009257 -0.101610  0.037059
			 0.013968 -0.117317  0.047729
			 0.012585 -0.114317  0.046657
			 0.012198 -0.112799  0.046861
			 0.017203 -0.112625  0.043904
			 0.009681 -0.102042  0.045860
			 0.014819 -0.110719  0.045573
			 0.019136 -0.114417  0.046403
			 0.017463 -0.112269  0.048262
			 0.018247 -0.116248  0.048712
			 0.017633 -0.117004  0.048531
			 0.015166 -0.118092  0.048143
			 0.016036 -0.117950  0.048238
			-0.000972 -0.090814  0.042414
			 0.009651 -0.107251  0.046526
			 0.002327 -0.092522  0.044097
			-0.002322 -0.090140  0.038480
			-0.001579 -0.091279  0.040130
			 0.003417 -0.091494  0.032172
			 0.000515 -0.093337  0.035177
			 0.006105 -0.093147  0.033949
			 0.007212 -0.096587  0.035468
			-0.000558 -0.093474  0.036839
			 0.007640 -0.094211  0.042567
			 0.007693 -0.091829  0.035175
			 0.012643 -0.114400  0.047224
			 0.015041 -0.117052  0.048676
			 0.012401 -0.113087  0.047330
			 0.012135 -0.111749  0.047295
			 0.012501 -0.111893  0.047704
			 0.013683 -0.110335  0.047090
			 0.006777 -0.095011  0.044629
			 0.015307 -0.110743  0.047795
			 0.018690 -0.114321  0.048665
			 0.016800 -0.112654  0.048922
			 0.017713 -0.115804  0.049269
			-0.001706 -0.089035  0.042147
			 0.001034 -0.090155  0.044354
			 0.012451 -0.110961  0.047712
			 0.004729 -0.091802  0.044466
			-0.000546 -0.091485  0.034050
			-0.002245 -0.090037  0.039999
			-0.002037 -0.087042  0.041164
			-0.002147 -0.087768  0.039251
			-0.002129 -0.088490  0.040638
			-0.001705 -0.087510  0.035879
			-0.001739 -0.089451  0.041148
			 0.001125 -0.090478  0.031652
			 0.003112 -0.090636  0.031389
			 0.004974 -0.088174  0.030971
			 0.006875 -0.090026  0.033016
			 0.007731 -0.086612  0.034098
			 0.005655 -0.091526  0.044355
			 0.006116 -0.085388  0.043655
			 0.013267 -0.113701  0.047975
			 0.013594 -0.111910  0.048380
			 0.017896 -0.114252  0.049258
			-0.001534 -0.085783  0.042384
			-0.001111 -0.087389  0.043515
			 0.002087 -0.088629  0.045054
			 0.000695 -0.089577  0.031570
			-0.000306 -0.084109  0.043322
			 0.005782 -0.083612  0.034675
			 0.000076 -0.087259  0.032333
			 0.001109 -0.085663  0.032572
			 0.001712 -0.089581  0.031011
			 0.002581 -0.088828  0.030605
			 0.005942 -0.085599  0.031620
			 0.004394 -0.086368  0.030926
			 0.006500 -0.087790  0.031795
			 0.007129 -0.084844  0.033918
			 0.003513 -0.083542  0.044499
			 0.005149 -0.083614  0.043458
			 0.006630 -0.084295  0.036356
			 0.000230 -0.085293  0.044427
			 0.002564 -0.083816  0.044649
			 0.001426 -0.088693  0.031029
			 0.002827 -0.082567  0.042154
			 0.001076 -0.083175  0.043646
			 0.003969 -0.084338  0.033043
			 0.005016 -0.083089  0.038220
			 0.005062 -0.084123  0.032882
			 0.001089 -0.086915  0.031775
			 0.003836 -0.084749  0.031965
			 0.002746 -0.082627  0.043491
			 0.003949 -0.082713  0.042545
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					1 0 4 
					0 5 4 
					0 3 5 
					2 1 6 
					7 1 4 
					7 8 1 
					6 1 8 
					2 9 3 
					2 10 9 
					10 2 11 
					11 2 6 
					3 9 12 
					13 3 12 
					5 3 13 
					14 4 15 
					4 14 16 
					15 4 5 
					4 16 7 
					5 17 15 
					13 18 5 
					19 5 18 
					19 17 5 
					6 8 20 
					11 6 20 
					21 8 7 
					21 7 16 
					8 21 22 
					22 20 8 
					12 9 23 
					23 9 10 
					23 10 24 
					24 10 11 
					25 11 20 
					25 26 11 
					27 11 26 
					24 11 27 
					28 12 23 
					28 13 12 
					13 29 30 
					13 31 18 
					31 13 30 
					32 13 28 
					13 32 29 
					15 17 14 
					16 14 17 
					33 21 16 
					34 16 17 
					16 34 33 
					34 17 35 
					19 35 17 
					18 36 19 
					36 18 31 
					19 36 35 
					20 22 25 
					33 37 21 
					22 21 37 
					38 25 22 
					38 22 37 
					23 39 28 
					23 24 39 
					40 39 24 
					24 27 40 
					38 26 25 
					38 41 26 
					42 26 41 
					43 44 26 
					42 43 26 
					26 44 27 
					44 40 27 
					28 39 32 
					30 29 45 
					46 47 29 
					47 45 29 
					46 29 32 
					31 30 48 
					48 30 49 
					30 45 50 
					49 30 50 
					31 48 51 
					51 36 31 
					52 53 32 
					39 52 32 
					54 46 32 
					32 53 54 
					55 33 34 
					33 55 37 
					34 56 57 
					56 34 35 
					55 34 57 
					56 35 51 
					35 36 51 
					37 58 38 
					55 59 37 
					58 37 59 
					38 58 41 
					52 39 40 
					44 52 40 
					58 60 41 
					42 41 60 
					61 43 42 
					61 42 60 
					44 43 62 
					43 61 63 
					62 43 63 
					52 44 62 
					45 47 50 
					47 46 64 
					65 56 46 
					66 64 46 
					46 56 66 
					65 46 54 
					50 47 67 
					47 68 67 
					68 47 64 
					69 48 70 
					48 49 70 
					48 69 71 
					48 71 72 
					51 48 72 
					73 70 49 
					49 50 73 
					73 50 67 
					74 51 75 
					51 74 56 
					75 51 72 
					76 53 52 
					76 52 77 
					52 62 77 
					78 53 76 
					53 78 54 
					79 65 54 
					54 78 80 
					79 54 80 
					59 55 57 
					81 57 56 
					82 66 56 
					65 81 56 
					82 56 74 
					83 59 57 
					57 81 83 
					84 60 58 
					58 59 84 
					59 85 84 
					85 59 83 
					84 86 60 
					86 61 60 
					86 63 61 
					62 63 77 
					63 86 77 
					68 64 87 
					87 64 88 
					88 64 66 
					79 89 65 
					89 81 65 
					82 90 66 
					66 90 88 
					73 67 91 
					68 92 67 
					93 94 67 
					95 67 92 
					95 93 67 
					96 67 94 
					96 91 67 
					87 97 68 
					97 92 68 
					69 70 98 
					98 99 69 
					69 99 100 
					101 69 100 
					101 71 69 
					70 73 91 
					91 98 70 
					72 71 75 
					75 71 101 
					74 75 102 
					82 74 103 
					104 103 74 
					102 104 74 
					102 75 101 
					80 78 76 
					76 77 105 
					80 76 105 
					105 77 86 
					79 80 89 
					106 89 80 
					106 80 105 
					89 83 81 
					90 82 103 
					89 106 83 
					83 106 85 
					84 85 107 
					86 84 107 
					85 105 107 
					85 106 105 
					105 86 107 
					95 87 93 
					87 108 93 
					95 97 87 
					88 109 87 
					87 109 108 
					90 110 88 
					110 109 88 
					103 110 90 
					98 91 111 
					111 91 96 
					92 97 95 
					94 93 108 
					108 112 94 
					113 96 94 
					112 113 94 
					114 111 96 
					113 115 96 
					114 96 115 
					98 111 116 
					116 99 98 
					117 99 116 
					100 99 117 
					118 100 119 
					120 101 100 
					119 100 117 
					100 118 120 
					120 102 101 
					121 104 102 
					120 121 102 
					103 104 122 
					110 103 122 
					104 123 122 
					104 124 123 
					124 104 121 
					108 109 112 
					112 109 125 
					110 125 109 
					125 110 126 
					110 122 126 
					111 114 127 
					116 111 127 
					113 112 128 
					125 129 112 
					112 129 128 
					113 130 115 
					131 121 113 
					121 132 113 
					128 131 113 
					130 113 132 
					133 114 115 
					127 114 133 
					133 115 134 
					130 134 115 
					116 127 117 
					133 117 127 
					117 133 119 
					134 118 119 
					118 134 132 
					120 118 121 
					132 121 118 
					134 119 133 
					121 131 124 
					122 129 126 
					129 122 135 
					122 136 135 
					136 122 123 
					124 136 123 
					136 124 131 
					125 126 129 
					135 128 129 
					135 136 128 
					128 136 131 
					130 132 134 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
