<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="174" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="344">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.380113 -0.924160 -0.037970
			 0.162223 -0.985658 -0.046487
			-0.345890 -0.922614  0.170715
			-0.495954 -0.859808 -0.121490
			-0.665008 -0.746704 -0.014052
			 0.249162 -0.808056  0.533821
			-0.122221 -0.794946  0.594241
			 0.131997 -0.990993 -0.022564
			 0.845854 -0.532766  0.026280
			 0.864734 -0.502166  0.008060
			-0.573791 -0.708323  0.411149
			-0.416124 -0.887131 -0.199597
			-0.646104 -0.745618 -0.163104
			-0.686035 -0.683017 -0.250688
			-0.589484 -0.806698  0.041813
			-0.025458 -0.270146  0.962483
			 0.489794 -0.577343  0.653281
			 0.254963 -0.959783 -0.117516
			 0.755076 -0.652123  0.067798
			 0.965728  0.242259  0.093170
			 0.999835  0.011083  0.014387
			 0.944429 -0.141910  0.296507
			 0.943566  0.317090 -0.095590
			-0.554322 -0.491933  0.671363
			-0.620641 -0.725739  0.296829
			-0.380491 -0.752942 -0.536940
			-0.524167 -0.844952 -0.106326
			-0.551801 -0.704190 -0.446803
			-0.639433 -0.638361 -0.428510
			-0.607144 -0.429513 -0.668502
			-0.467884 -0.875806  0.118525
			 0.481476 -0.309779  0.819889
			 0.439944 -0.004393  0.898014
			 0.018333  0.097688  0.995048
			-0.190210  0.211391  0.958715
			-0.345993 -0.336319  0.875887
			 0.190836  0.353647  0.915705
			 0.476648  0.316163  0.820273
			 0.190266 -0.703926 -0.684315
			 0.691621 -0.524129 -0.496941
			 0.475749  0.878066  0.051611
			 0.707700  0.702018  0.079573
			 0.880123 -0.101687 -0.463728
			 0.810992  0.259760 -0.524229
			 0.652945  0.618142 -0.437679
			 0.785095  0.416938  0.458027
			-0.491856 -0.139334  0.859455
			-0.513158 -0.713009  0.477795
			-0.182662 -0.399425 -0.898385
			-0.622243 -0.767958  0.151834
			-0.470076 -0.592510 -0.654187
			-0.566792 -0.767122  0.300451
			-0.521622 -0.783184 -0.338426
			-0.237841 -0.466912 -0.851719
			-0.191258 -0.251990 -0.948642
			-0.231637 -0.185093 -0.955031
			-0.630852 -0.400530 -0.664531
			-0.409959  0.036412 -0.911377
			-0.262030 -0.213760 -0.941088
			 0.325766  0.744361  0.582927
			-0.264513  0.857502  0.441274
			 0.086901 -0.305563 -0.948198
			 0.356855 -0.076737 -0.931003
			 0.286884  0.932745  0.218367
			 0.211032  0.977454 -0.007071
			 0.286073  0.887563 -0.361102
			 0.257580  0.235343 -0.937159
			-0.024902  0.397381 -0.917316
			-0.299427 -0.452285  0.840108
			 0.211730  0.327483  0.920828
			-0.117206  0.679782  0.723989
			-0.390510 -0.280188 -0.876925
			-0.265834 -0.552066  0.790288
			-0.193304 -0.623715  0.757373
			-0.437632 -0.858519  0.267253
			-0.534213 -0.726240 -0.432656
			-0.558396 -0.827458  0.059223
			-0.471857 -0.398962 -0.786245
			-0.302891 -0.429789 -0.850611
			 0.149296 -0.071458 -0.986207
			 0.394272  0.371472 -0.840570
			 0.108152  0.015834 -0.994008
			 0.340460  0.430559 -0.835886
			-0.091440  0.325962 -0.940950
			 0.345871  0.651682 -0.675043
			-0.254820 -0.067364 -0.964639
			-0.100245  0.819265 -0.564584
			 0.087999  0.804131 -0.587903
			 0.628923  0.757272  0.176055
			 0.467485  0.421291  0.777156
			 0.294062  0.949004 -0.113666
			 0.560040  0.825662 -0.068105
			 0.272353 -0.129952  0.953382
			 0.251324 -0.205574  0.945820
			 0.109248 -0.299864  0.947706
			-0.422065 -0.761434  0.492015
			-0.062197 -0.406601  0.911486
			-0.320263 -0.599214  0.733740
			 0.336015 -0.160296  0.928116
			-0.591941 -0.750684  0.293392
			-0.743898 -0.522042 -0.417239
			-0.751964 -0.643630 -0.142445
			-0.640173 -0.312578 -0.701765
			-0.966497  0.021088  0.255810
			-0.455396  0.108737 -0.883623
			-0.788244  0.382697 -0.481887
			-0.767682  0.240953 -0.593807
			 0.579350  0.381104 -0.720495
			 0.728773  0.682037 -0.060948
			-0.554181  0.544277 -0.629798
			 0.369227  0.426522 -0.825682
			 0.718058  0.486590  0.497618
			 0.530047  0.210131  0.821520
			-0.593685 -0.579485  0.558332
			-0.649709 -0.207727  0.731251
			 0.075300 -0.128635  0.988829
			-0.601137  0.405304  0.688740
			 0.296011  0.017337  0.955027
			 0.733436  0.097560  0.672721
			 0.776911  0.470808  0.418031
			-0.835186  0.163606  0.525069
			-0.892818  0.276101 -0.355872
			-0.915685  0.400541 -0.032988
			-0.237569  0.957244  0.165060
			-0.293009  0.946663  0.134070
			-0.717282  0.695362  0.044463
			-0.454788  0.886572  0.084602
			 0.787842  0.475790 -0.391062
			 0.852041  0.522768 -0.027197
			-0.519813  0.822774 -0.229864
			-0.875263  0.381006  0.297907
			 0.653573  0.639167 -0.405349
			-0.795178  0.218823  0.565515
			-0.678160  0.687332  0.260143
			-0.642283  0.704549  0.301800
			 0.141036  0.370234  0.918170
			-0.081819  0.344359  0.935266
			-0.465781  0.590681  0.658896
			-0.501888  0.862501  0.064804
			-0.788480  0.557048  0.260760
			-0.688166  0.723590 -0.053333
			 0.688801  0.291815  0.663624
			 0.379028  0.589499  0.713322
			 0.890539  0.353345  0.286509
			 0.836887  0.344891  0.425053
			-0.457824  0.884075  0.093855
			-0.422515  0.905989 -0.025804
			-0.220512  0.974003  0.051886
			-0.187537  0.982086 -0.018338
			-0.402374  0.913128  0.065512
			-0.801211  0.582408  0.137341
			 0.297338  0.954186 -0.033466
			 0.888301  0.424193  0.176017
			 0.698016  0.662370  0.272103
			-0.664435  0.744430 -0.065948
			-0.929290  0.273568  0.248155
			-0.983665  0.156412  0.089105
			-0.978769  0.151649  0.137888
			-0.392153  0.721221  0.571013
			-0.832480  0.547905 -0.082330
			-0.552787  0.833263 -0.009941
			-0.683363  0.687016  0.247032
			-0.908973  0.413304 -0.054299
			-0.858779  0.463390  0.218559
			-0.120634  0.907797  0.401686
			-0.670813  0.741469 -0.015284
			-0.725716  0.642365 -0.246380
			-0.873387  0.419331 -0.247702
			-0.839865  0.542463  0.019004
			-0.795455  0.565575  0.217662
			-0.969194  0.237804  0.064136
			-0.992102  0.117504  0.043897
			-0.926232  0.164792  0.339024
			-0.917964  0.386555  0.088978
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.043698 -0.159427 -0.026273
			 0.044827 -0.159608 -0.026171
			 0.043977 -0.159486 -0.025501
			 0.043473 -0.158958 -0.028692
			 0.042576 -0.158610 -0.026321
			 0.045208 -0.159288 -0.024747
			 0.044639 -0.159406 -0.024902
			 0.044156 -0.159207 -0.028748
			 0.046925 -0.157411 -0.028306
			 0.047087 -0.158089 -0.025180
			 0.042548 -0.158516 -0.024906
			 0.043818 -0.158833 -0.030553
			 0.041346 -0.156965 -0.028552
			 0.042753 -0.158215 -0.029583
			 0.040845 -0.156500 -0.026831
			 0.045858 -0.154977 -0.022820
			 0.046558 -0.158215 -0.024214
			 0.045502 -0.159080 -0.030946
			 0.046985 -0.157905 -0.030266
			 0.047023 -0.155305 -0.028044
			 0.047735 -0.155837 -0.030234
			 0.047548 -0.156147 -0.024277
			 0.047456 -0.155236 -0.025962
			 0.041642 -0.157289 -0.024487
			 0.040555 -0.156160 -0.025490
			 0.044561 -0.158774 -0.032026
			 0.040098 -0.156243 -0.028243
			 0.033097 -0.150423 -0.029931
			 0.040929 -0.155738 -0.030629
			 0.041842 -0.156096 -0.031864
			 0.039654 -0.156092 -0.026712
			 0.046899 -0.156981 -0.023574
			 0.046417 -0.155543 -0.022970
			 0.044367 -0.154222 -0.022712
			 0.043551 -0.154120 -0.022754
			 0.041579 -0.155973 -0.023816
			 0.045301 -0.153984 -0.022857
			 0.045978 -0.154422 -0.023000
			 0.045448 -0.158771 -0.032162
			 0.046740 -0.157680 -0.032418
			 0.045428 -0.153621 -0.027815
			 0.047128 -0.154162 -0.030082
			 0.047417 -0.155998 -0.032494
			 0.047273 -0.155125 -0.032484
			 0.046925 -0.154312 -0.032453
			 0.047007 -0.154742 -0.023651
			 0.040792 -0.154984 -0.024099
			 0.039566 -0.155700 -0.025650
			 0.045332 -0.158272 -0.032445
			 0.036116 -0.153315 -0.026871
			 0.040768 -0.155070 -0.031284
			 0.035206 -0.152494 -0.026247
			 0.030638 -0.148513 -0.029745
			 0.029697 -0.146345 -0.031504
			 0.034129 -0.149604 -0.031037
			 0.039204 -0.153590 -0.031403
			 0.041253 -0.155162 -0.031573
			 0.041861 -0.154609 -0.032238
			 0.042846 -0.156329 -0.032309
			 0.045345 -0.153466 -0.023151
			 0.042991 -0.153139 -0.023729
			 0.046251 -0.157071 -0.032848
			 0.046828 -0.155838 -0.033049
			 0.041979 -0.153121 -0.025815
			 0.045098 -0.153095 -0.030134
			 0.045027 -0.153287 -0.032253
			 0.046269 -0.154609 -0.033086
			 0.044960 -0.153965 -0.032936
			 0.039223 -0.154114 -0.024352
			 0.040228 -0.153038 -0.024136
			 0.041631 -0.153767 -0.024370
			 0.040733 -0.154665 -0.031676
			 0.036295 -0.151246 -0.023635
			 0.034328 -0.149039 -0.022623
			 0.031123 -0.148249 -0.023224
			 0.024091 -0.144983 -0.029802
			 0.022450 -0.144488 -0.025150
			 0.024504 -0.143701 -0.030980
			 0.026698 -0.143382 -0.032451
			 0.031091 -0.145153 -0.031941
			 0.036942 -0.149851 -0.031154
			 0.028999 -0.143589 -0.032526
			 0.039255 -0.151765 -0.031301
			 0.041016 -0.153773 -0.031621
			 0.040133 -0.152397 -0.031163
			 0.042834 -0.155021 -0.032637
			 0.043957 -0.153164 -0.032141
			 0.041290 -0.153082 -0.031174
			 0.039666 -0.151693 -0.026333
			 0.039453 -0.152398 -0.024178
			 0.041889 -0.152819 -0.029758
			 0.040491 -0.152158 -0.028414
			 0.038072 -0.151669 -0.023675
			 0.037517 -0.150824 -0.023253
			 0.035026 -0.148776 -0.022416
			 0.025950 -0.145708 -0.022247
			 0.027899 -0.143713 -0.019763
			 0.026432 -0.144650 -0.020692
			 0.030697 -0.145038 -0.020555
			 0.023096 -0.144582 -0.023717
			 0.022763 -0.143837 -0.029493
			 0.022260 -0.144274 -0.026532
			 0.023191 -0.143413 -0.030282
			 0.021725 -0.143665 -0.025281
			 0.025266 -0.141782 -0.032349
			 0.023287 -0.142539 -0.030649
			 0.022842 -0.142992 -0.030042
			 0.031606 -0.144696 -0.031680
			 0.035391 -0.147483 -0.027756
			 0.025885 -0.140365 -0.031895
			 0.028902 -0.140905 -0.032010
			 0.038380 -0.151020 -0.024021
			 0.035493 -0.148440 -0.022523
			 0.024106 -0.144565 -0.022426
			 0.024849 -0.143319 -0.020702
			 0.027828 -0.141939 -0.019129
			 0.026599 -0.141658 -0.019136
			 0.028713 -0.141439 -0.019177
			 0.031037 -0.143576 -0.020793
			 0.032237 -0.144514 -0.021828
			 0.022308 -0.143780 -0.023850
			 0.022290 -0.143127 -0.029225
			 0.021681 -0.143367 -0.026439
			 0.022246 -0.143092 -0.026248
			 0.022872 -0.143411 -0.024109
			 0.025495 -0.140990 -0.031920
			 0.023564 -0.142427 -0.030221
			 0.031018 -0.143025 -0.031099
			 0.032010 -0.143764 -0.025564
			 0.026673 -0.139062 -0.031036
			 0.027201 -0.140091 -0.028956
			 0.029928 -0.140201 -0.030709
			 0.023179 -0.143721 -0.022799
			 0.024765 -0.142913 -0.021105
			 0.023956 -0.143309 -0.021959
			 0.028335 -0.140936 -0.019303
			 0.027524 -0.141267 -0.019029
			 0.027231 -0.140816 -0.019515
			 0.025141 -0.142744 -0.021534
			 0.027102 -0.140541 -0.019938
			 0.025775 -0.142193 -0.022584
			 0.030263 -0.141533 -0.019880
			 0.029364 -0.140067 -0.020057
			 0.031675 -0.143486 -0.021920
			 0.030487 -0.141020 -0.020748
			 0.024329 -0.142994 -0.022363
			 0.022463 -0.142856 -0.028188
			 0.023142 -0.142689 -0.028722
			 0.023935 -0.143399 -0.024226
			 0.025609 -0.142895 -0.026724
			 0.026985 -0.141452 -0.027056
			 0.028139 -0.137968 -0.025805
			 0.030648 -0.140565 -0.021587
			 0.029793 -0.139213 -0.021727
			 0.027503 -0.138392 -0.029943
			 0.027348 -0.138876 -0.030055
			 0.027341 -0.138843 -0.029212
			 0.027686 -0.139944 -0.027790
			 0.027787 -0.139134 -0.020533
			 0.027639 -0.139610 -0.021419
			 0.025357 -0.142883 -0.025558
			 0.026757 -0.141884 -0.025218
			 0.027996 -0.140171 -0.024095
			 0.027695 -0.140739 -0.024843
			 0.028264 -0.138824 -0.021097
			 0.026376 -0.141866 -0.025634
			 0.026670 -0.142011 -0.026134
			 0.027525 -0.140719 -0.026152
			 0.026936 -0.141247 -0.025740
			 0.028424 -0.138185 -0.022272
			 0.027410 -0.138622 -0.028317
			 0.027568 -0.139834 -0.025672
			 0.027852 -0.140051 -0.024930
			 0.027275 -0.140445 -0.025632
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					1 0 3 
					0 4 3 
					2 4 0 
					5 6 1 
					6 2 1 
					3 7 1 
					7 8 1 
					8 9 1 
					9 5 1 
					2 6 10 
					2 10 4 
					11 7 3 
					4 12 3 
					3 12 13 
					13 11 3 
					4 14 12 
					10 14 4 
					6 5 15 
					5 16 15 
					9 16 5 
					15 10 6 
					7 11 17 
					8 7 18 
					18 7 17 
					9 8 19 
					8 20 19 
					18 20 8 
					21 16 9 
					21 9 22 
					22 9 19 
					15 23 10 
					23 24 10 
					10 24 14 
					25 17 11 
					25 11 13 
					26 12 14 
					12 26 27 
					28 12 27 
					12 28 13 
					29 25 13 
					28 29 13 
					30 14 24 
					26 14 30 
					16 31 15 
					31 32 15 
					33 34 15 
					34 35 15 
					35 23 15 
					33 15 36 
					15 32 37 
					15 37 36 
					21 31 16 
					25 38 17 
					18 17 39 
					17 38 39 
					20 18 39 
					40 22 19 
					20 41 19 
					19 41 40 
					42 43 20 
					41 20 44 
					20 43 44 
					42 20 39 
					45 21 22 
					21 45 31 
					22 40 45 
					46 23 35 
					24 23 46 
					47 24 46 
					24 47 30 
					48 38 25 
					48 25 29 
					30 49 26 
					27 26 49 
					27 50 28 
					49 51 27 
					52 27 51 
					53 54 27 
					53 27 52 
					54 55 27 
					55 50 27 
					28 50 56 
					29 28 56 
					57 58 29 
					58 48 29 
					57 29 56 
					47 49 30 
					45 32 31 
					32 45 37 
					33 36 34 
					34 36 59 
					60 46 34 
					35 34 46 
					34 59 60 
					36 37 59 
					37 45 59 
					38 61 39 
					61 38 48 
					62 42 39 
					62 39 61 
					63 40 64 
					64 40 41 
					63 60 40 
					59 45 40 
					60 59 40 
					64 41 65 
					65 41 44 
					42 62 43 
					66 44 43 
					66 43 62 
					66 67 44 
					65 44 67 
					68 46 69 
					46 68 47 
					69 46 70 
					46 60 70 
					49 47 68 
					58 61 48 
					51 49 68 
					55 71 50 
					50 71 57 
					50 57 56 
					51 68 72 
					51 72 73 
					74 51 73 
					52 51 74 
					75 52 76 
					52 74 76 
					75 77 52 
					78 52 77 
					53 52 78 
					79 80 53 
					53 78 81 
					53 81 79 
					54 53 80 
					80 82 54 
					82 55 54 
					55 83 71 
					84 83 55 
					82 84 55 
					67 85 57 
					86 67 57 
					83 57 71 
					87 86 57 
					87 57 83 
					58 57 85 
					62 61 58 
					66 62 58 
					66 58 67 
					85 67 58 
					70 60 63 
					88 89 63 
					90 91 63 
					63 91 88 
					64 90 63 
					69 63 89 
					70 63 69 
					90 64 87 
					65 86 64 
					86 87 64 
					86 65 67 
					72 68 92 
					89 92 68 
					69 89 68 
					92 93 72 
					73 72 93 
					93 94 73 
					73 95 74 
					73 96 97 
					73 98 96 
					73 94 98 
					73 97 95 
					95 99 74 
					99 76 74 
					100 75 101 
					75 76 101 
					77 75 102 
					75 100 102 
					101 76 103 
					103 76 99 
					104 77 105 
					106 105 77 
					106 77 102 
					104 78 77 
					78 104 81 
					107 80 79 
					107 79 81 
					108 91 80 
					82 80 91 
					107 108 80 
					109 81 104 
					110 107 81 
					109 110 81 
					84 82 91 
					84 87 83 
					90 84 91 
					87 84 90 
					108 111 88 
					108 88 91 
					88 111 89 
					111 92 89 
					92 111 93 
					111 112 93 
					112 94 93 
					112 98 94 
					113 95 114 
					114 95 97 
					113 99 95 
					115 116 96 
					114 96 116 
					96 117 115 
					96 114 97 
					98 117 96 
					118 98 119 
					117 98 118 
					98 112 119 
					99 120 103 
					113 120 99 
					102 100 121 
					121 100 101 
					101 122 121 
					103 122 101 
					121 106 102 
					123 122 103 
					103 124 123 
					103 120 124 
					104 125 109 
					105 125 104 
					126 105 106 
					125 105 126 
					126 106 121 
					127 108 107 
					110 127 107 
					127 128 108 
					108 128 119 
					119 111 108 
					129 110 109 
					130 109 125 
					130 129 109 
					127 110 131 
					129 131 110 
					111 119 112 
					113 132 120 
					114 132 113 
					116 133 114 
					134 132 114 
					133 134 114 
					115 117 135 
					135 136 115 
					136 116 115 
					116 136 137 
					116 138 133 
					139 116 137 
					139 140 116 
					140 138 116 
					141 142 117 
					135 117 142 
					118 141 117 
					118 119 143 
					118 143 144 
					141 118 144 
					143 119 128 
					120 132 145 
					120 145 124 
					146 121 122 
					121 147 126 
					147 121 146 
					122 123 146 
					146 123 147 
					124 148 123 
					123 148 149 
					147 123 149 
					145 148 124 
					149 125 126 
					149 150 125 
					150 130 125 
					126 147 149 
					128 127 131 
					131 151 128 
					152 143 128 
					151 153 128 
					152 128 153 
					131 129 154 
					154 129 155 
					155 129 130 
					156 155 130 
					130 157 156 
					150 157 130 
					131 154 151 
					132 134 145 
					134 133 138 
					134 138 145 
					136 135 158 
					135 142 158 
					136 158 137 
					158 139 137 
					145 138 140 
					159 139 158 
					139 159 140 
					145 140 160 
					140 161 160 
					162 163 140 
					159 162 140 
					163 161 140 
					142 141 153 
					141 144 153 
					164 142 153 
					164 158 142 
					143 152 144 
					152 153 144 
					148 145 160 
					149 148 160 
					165 166 149 
					150 149 166 
					149 160 165 
					167 150 168 
					150 167 157 
					150 166 168 
					169 153 151 
					170 169 151 
					151 154 170 
					169 164 153 
					170 154 156 
					155 156 154 
					170 156 157 
					157 171 170 
					171 157 167 
					158 164 159 
					159 164 169 
					162 159 169 
					161 165 160 
					165 161 168 
					163 168 161 
					162 169 172 
					162 172 163 
					163 173 168 
					173 163 172 
					166 165 168 
					173 171 167 
					173 167 168 
					171 169 170 
					171 172 169 
					172 171 173 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
