<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="90" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="176">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.850679 -0.525458 -0.015466
			-0.795084  0.254980  0.550296
			-0.925442  0.270628  0.265173
			-0.957385 -0.195340 -0.212735
			-0.786748 -0.539937  0.299158
			-0.103721 -0.993953  0.036035
			-0.204106 -0.927770 -0.312385
			-0.374321 -0.766884 -0.521319
			-0.821271  0.539541  0.185499
			-0.254865 -0.469416  0.845395
			-0.615790  0.441827  0.652374
			 0.135474  0.086726  0.986978
			-0.939578  0.334432 -0.073133
			-0.872961 -0.112735 -0.474583
			 0.367093 -0.573977  0.731979
			-0.019325 -0.955343 -0.294867
			 0.308760 -0.893900  0.324976
			-0.495147 -0.758038 -0.424509
			-0.676226 -0.286344 -0.678768
			-0.364806 -0.517208 -0.774217
			-0.708473  0.685778 -0.166655
			-0.658253  0.483144  0.577300
			-0.951291  0.262146 -0.162253
			-0.755394  0.650652  0.077664
			-0.131130  0.272086  0.953296
			 0.488331 -0.306142  0.817196
			 0.247957 -0.193108  0.949329
			-0.774129  0.401012 -0.489810
			-0.039770 -0.921917  0.385339
			-0.222336 -0.734528 -0.641120
			-0.339310 -0.920901 -0.191862
			-0.626070  0.215179 -0.749489
			-0.310900 -0.254044 -0.915862
			-0.420070 -0.086330 -0.903376
			-0.017282 -0.190023 -0.981628
			-0.843011  0.483333 -0.236055
			-0.730245  0.426863 -0.533413
			-0.789158  0.482735 -0.379731
			-0.976620  0.172534  0.128238
			-0.440370  0.552290  0.707849
			-0.932443 -0.246849 -0.263848
			-0.385800  0.502020  0.774037
			 0.025627 -0.069445  0.997256
			 0.491809 -0.395722  0.775583
			 0.397779 -0.406705  0.822413
			 0.620143 -0.368548  0.692527
			 0.650682 -0.559415  0.513485
			 0.143615 -0.952240  0.269468
			 0.628274 -0.776538 -0.047544
			 0.650046 -0.371545 -0.662868
			-0.098080 -0.250140 -0.963229
			-0.048434 -0.095860 -0.994216
			-0.687957  0.261092 -0.677160
			 0.770810  0.004089 -0.637052
			 0.444365 -0.154277 -0.882462
			-0.905221 -0.116897 -0.408546
			-0.866787 -0.130919 -0.481187
			-0.987206  0.077368 -0.139421
			-0.840747  0.485030  0.240604
			-0.931736  0.178444 -0.316268
			-0.048727  0.692554  0.719719
			-0.285891  0.898515  0.333071
			-0.112598  0.179653  0.977265
			 0.402918  0.296681  0.865816
			 0.638689 -0.275261  0.718546
			 0.817194 -0.254137  0.517310
			 0.763002 -0.474080  0.439404
			 0.964964 -0.247507  0.087090
			 0.988855 -0.109437  0.100941
			 0.946904 -0.311703 -0.078833
			 0.178592 -0.025083 -0.983603
			 0.532393 -0.218947 -0.817692
			-0.361718  0.303742 -0.881420
			-0.549232  0.341252 -0.762818
			-0.822303  0.218524 -0.525419
			-0.839916 -0.035947 -0.541524
			-0.755592  0.055711 -0.652670
			-0.374929  0.922588 -0.090881
			-0.368155  0.613703 -0.698448
			 0.430273  0.813913  0.390398
			 0.146747  0.984281  0.098271
			 0.918267  0.271851  0.287894
			 0.928388 -0.290593  0.231628
			 0.969575  0.003742 -0.244765
			 0.816323 -0.208968 -0.538469
			 0.127301  0.392256 -0.911005
			 0.576341  0.248793 -0.778417
			-0.494310  0.288851 -0.819892
			 0.092853  0.844924 -0.526765
			 0.667814  0.656730 -0.350328
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.013803 -0.026908  0.003993
			-0.011822 -0.023896  0.008425
			-0.013661 -0.025863  0.004057
			-0.013905 -0.026050  0.002741
			-0.012682 -0.027714  0.006819
			-0.010398 -0.028567  0.006015
			-0.011745 -0.027669  0.002485
			-0.011697 -0.026265 -0.000328
			-0.012024 -0.022477  0.003478
			-0.011910 -0.027010  0.009027
			-0.010435 -0.020082  0.007488
			-0.009268 -0.022702  0.008323
			-0.013424 -0.022140  0.000880
			-0.012616 -0.024168 -0.000911
			-0.009081 -0.027998  0.007584
			-0.005247 -0.027214  0.002928
			-0.004244 -0.027459  0.004371
			-0.003405 -0.024908 -0.003243
			-0.011494 -0.024014 -0.003175
			-0.003557 -0.022498 -0.005508
			-0.011637 -0.019667 -0.000295
			-0.007323 -0.014970  0.006505
			-0.006878 -0.014404  0.004275
			-0.008640 -0.017907  0.001675
			-0.004688 -0.014503  0.006745
			-0.001620 -0.024554  0.003862
			-0.002948 -0.016417  0.006924
			-0.011125 -0.021380 -0.002219
			-0.002214 -0.027800  0.002398
			 0.000716 -0.029113 -0.006303
			 0.000702 -0.029953 -0.004556
			-0.010003 -0.021571 -0.004733
			-0.002754 -0.020649 -0.006465
			-0.005615 -0.019156 -0.006846
			 0.000138 -0.027014 -0.007157
			-0.006393 -0.015370 -0.000483
			-0.006390 -0.015810 -0.001282
			-0.003838 -0.016383 -0.004267
			-0.007103 -0.013523  0.004834
			-0.004837 -0.012741  0.005838
			-0.006147 -0.012992 -0.000610
			-0.002895 -0.009789  0.005437
			 0.000477 -0.013368  0.006868
			 0.001478 -0.025646  0.001873
			 0.003186 -0.014688  0.005971
			 0.003067 -0.016721  0.004963
			 0.001377 -0.028923 -0.000399
			 0.000486 -0.029865 -0.001249
			 0.002655 -0.029199 -0.003518
			 0.002639 -0.028099 -0.005935
			-0.001701 -0.018797 -0.007322
			-0.000225 -0.021341 -0.007428
			-0.004373 -0.017152 -0.007763
			 0.002701 -0.020423 -0.006324
			 0.001681 -0.021416 -0.006890
			-0.004951 -0.014096 -0.002447
			-0.003800 -0.015528 -0.004281
			-0.003356 -0.014774 -0.005632
			-0.007375 -0.012289  0.003101
			-0.006894 -0.011255 -0.001543
			 0.002452 -0.008688  0.006459
			-0.000168 -0.007025  0.002638
			 0.002393 -0.010394  0.007203
			 0.004049 -0.009966  0.007045
			 0.005027 -0.012240  0.006566
			 0.004406 -0.019733  0.002237
			 0.004847 -0.014447  0.004353
			 0.005129 -0.016008  0.001241
			 0.005578 -0.019518 -0.000184
			 0.004435 -0.026331 -0.003310
			 0.001265 -0.015036 -0.008488
			 0.002095 -0.019074 -0.007308
			-0.001663 -0.013427 -0.007558
			-0.002683 -0.014074 -0.007181
			-0.003250 -0.013934 -0.006653
			-0.005424 -0.012075 -0.004458
			-0.003336 -0.013121 -0.006533
			 0.000162 -0.006194 -0.000696
			-0.001251 -0.009225 -0.005493
			 0.004049 -0.007243  0.003664
			 0.003330 -0.005896  0.000086
			 0.006673 -0.010579  0.003715
			 0.006642 -0.011715  0.003326
			 0.006810 -0.011927 -0.003525
			 0.005528 -0.014984 -0.005418
			 0.000932 -0.013540 -0.008327
			 0.004651 -0.011557 -0.005686
			-0.002479 -0.013306 -0.007087
			 0.004036 -0.006438 -0.002998
			 0.005030 -0.006711 -0.002757
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 2 3 
					4 1 0 
					5 0 6 
					0 5 4 
					0 7 6 
					0 3 7 
					1 8 2 
					1 4 9 
					8 1 10 
					11 1 9 
					10 1 11 
					12 2 8 
					2 12 3 
					3 12 13 
					13 7 3 
					5 9 4 
					9 5 14 
					15 5 6 
					5 15 16 
					16 14 5 
					7 15 6 
					17 15 7 
					13 18 7 
					18 19 7 
					7 19 17 
					12 8 20 
					21 8 10 
					22 8 21 
					8 22 23 
					20 8 23 
					11 9 14 
					21 10 11 
					24 21 11 
					11 14 25 
					26 24 11 
					25 26 11 
					12 20 27 
					13 12 27 
					18 13 27 
					16 25 14 
					28 16 15 
					15 17 28 
					16 28 25 
					17 19 29 
					17 29 30 
					17 30 28 
					18 27 31 
					18 31 19 
					32 19 33 
					31 33 19 
					34 19 32 
					19 34 29 
					20 23 35 
					20 35 36 
					37 27 20 
					37 20 36 
					21 38 22 
					39 38 21 
					24 39 21 
					22 35 23 
					35 22 40 
					40 22 38 
					39 24 41 
					42 41 24 
					24 26 42 
					25 43 26 
					25 28 43 
					42 26 44 
					26 45 44 
					43 45 26 
					31 27 37 
					46 43 28 
					47 46 28 
					28 30 47 
					29 48 30 
					29 49 48 
					49 29 34 
					30 48 47 
					37 33 31 
					33 50 32 
					51 32 50 
					32 51 34 
					33 37 52 
					52 50 33 
					49 34 53 
					34 54 53 
					34 51 54 
					55 35 40 
					36 35 55 
					37 36 56 
					56 36 55 
					52 37 57 
					56 57 37 
					58 40 38 
					38 39 58 
					39 41 58 
					58 59 40 
					59 55 40 
					60 61 41 
					41 61 58 
					41 62 60 
					62 41 42 
					42 63 62 
					42 64 63 
					44 64 42 
					43 46 65 
					43 65 45 
					45 66 44 
					64 44 66 
					45 65 67 
					66 45 67 
					65 46 68 
					68 46 69 
					46 48 69 
					47 48 46 
					49 69 48 
					53 69 49 
					70 50 52 
					71 50 70 
					51 50 71 
					51 71 54 
					72 52 73 
					74 52 57 
					74 73 52 
					72 70 52 
					53 67 68 
					67 53 71 
					53 68 69 
					54 71 53 
					55 59 75 
					55 75 56 
					56 76 57 
					56 75 76 
					76 74 57 
					58 61 77 
					58 77 59 
					59 77 78 
					75 59 78 
					60 79 61 
					79 60 63 
					63 60 62 
					80 77 61 
					79 80 61 
					81 63 64 
					63 81 79 
					82 64 66 
					81 64 82 
					68 67 65 
					66 67 82 
					67 83 82 
					84 67 71 
					67 84 83 
					70 72 85 
					85 86 70 
					86 84 70 
					70 84 71 
					87 72 73 
					78 85 72 
					78 72 87 
					74 87 73 
					76 87 74 
					75 78 76 
					76 78 87 
					88 77 80 
					88 78 77 
					85 78 88 
					89 80 79 
					79 81 89 
					80 89 88 
					81 83 89 
					83 81 82 
					89 83 86 
					86 83 84 
					89 86 85 
					85 88 89 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
