<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.444162 -0.895946  0.000000
			 0.684901 -0.716352 -0.133230
			 0.943990 -0.329974  0.000000
			 0.742867 -0.401784 -0.535461
			 0.447295 -0.232276 -0.863699
			-0.769892 -0.638174  0.000000
			-0.443640 -0.896053  0.016525
			-0.771975 -0.565318 -0.290636
			-0.046853 -0.404187 -0.913476
			 0.684902 -0.716351  0.133230
			 0.742868 -0.401778  0.535464
			 0.447290 -0.232272  0.863702
			-0.443640 -0.896053 -0.016525
			-0.771975 -0.565318  0.290636
			-0.046853 -0.404187  0.913476
			 0.918152  0.277219 -0.283102
			 0.897923  0.440152  0.000000
			 0.918151  0.277222  0.283101
			 0.490149  0.466971 -0.735997
			 0.110146  0.340299 -0.933844
			-0.963450 -0.257075  0.075337
			-0.971114 -0.238617  0.000000
			-0.963450 -0.257075 -0.075337
			-0.763835 -0.283590 -0.579770
			-0.432952  0.142118 -0.890143
			-0.934607 -0.331908  0.127855
			 0.490144  0.466977  0.735996
			 0.110146  0.340299  0.933844
			-0.432952  0.142118  0.890143
			-0.763835 -0.283590  0.579770
			-0.934607 -0.331908 -0.127855
			 0.808832  0.438501 -0.391800
			 0.885865  0.463942  0.000000
			 0.808832  0.438501  0.391800
			 0.525385  0.318879 -0.788851
			 0.151697  0.180298 -0.971844
			-0.877602 -0.396947  0.268792
			-0.890366 -0.455244  0.000000
			-0.877602 -0.396947 -0.268792
			 0.069222 -0.575832 -0.814633
			-0.088624 -0.069828 -0.993614
			-0.515955 -0.714016  0.473256
			 0.525385  0.318879  0.788851
			 0.151697  0.180298  0.971844
			-0.088624 -0.069828  0.993614
			 0.069223 -0.575831  0.814633
			-0.515954 -0.714016 -0.473258
			 0.906634  0.178494 -0.382302
			 0.961769  0.273863  0.000000
			 0.906634  0.178494  0.382302
			 0.609393 -0.029497 -0.792319
			 0.299492 -0.175755 -0.937771
			-0.873252 -0.404814  0.271213
			-0.372606 -0.142237  0.917024
			-0.920762 -0.390125  0.000000
			-0.873252 -0.404814 -0.271213
			-0.372606 -0.142237 -0.917024
			 0.138832  0.027212 -0.989942
			 0.109900 -0.255533 -0.960534
			 0.144987 -0.751471 -0.643638
			 0.321210 -0.910722 -0.259634
			 0.657521 -0.229251 -0.717711
			 0.607969  0.404154  0.683399
			 0.609393 -0.029497  0.792319
			 0.299492 -0.175755  0.937771
			 0.109900 -0.255533  0.960534
			 0.138832  0.027212  0.989942
			 0.321212 -0.910721  0.259632
			 0.144989 -0.751470  0.643639
			 0.657521 -0.229250  0.717712
			 0.607968  0.404153 -0.683400
			 0.933428  0.148368 -0.326648
			 0.978507  0.206215  0.000000
			 0.933428  0.148368  0.326648
			 0.635759 -0.013288 -0.771774
			 0.276994 -0.216822 -0.936089
			-0.689842  0.608580  0.392109
			-0.182048 -0.080407  0.979997
			-0.893625  0.448814  0.000000
			-0.689842  0.608580 -0.392109
			-0.182048 -0.080407 -0.979997
			-0.247814  0.137306 -0.959028
			-0.013415 -0.193928 -0.980924
			 0.167091  0.769410 -0.616514
			-0.241631 -0.744469 -0.622399
			-0.514195 -0.518776 -0.682990
			-0.132261 -0.882594 -0.451148
			 0.905953 -0.035163 -0.421916
			 0.191054 -0.976147 -0.103123
			 0.738142  0.286261 -0.610902
			 0.904132  0.138396  0.404218
			 0.816572  0.577244  0.000000
			-0.744596  0.667516  0.000000
			-0.527006  0.330416  0.783001
			 0.635759 -0.013288  0.771774
			 0.276994 -0.216822  0.936089
			-0.013415 -0.193928  0.980924
			-0.247814  0.137306  0.959028
			 0.167091  0.769410  0.616514
			-0.514196 -0.518779  0.682987
			-0.241630 -0.744469  0.622399
			-0.132262 -0.882594  0.451149
			 0.905953 -0.035167  0.421915
			 0.738142  0.286261  0.610902
			 0.191055 -0.976147  0.103127
			 0.904132  0.138396 -0.404218
			-0.527008  0.330417 -0.782999
			 0.482095  0.796198 -0.365586
			 0.663763  0.747943  0.000000
			 0.482095  0.796198  0.365586
			 0.168085  0.815861 -0.553279
			 0.035270  0.544804 -0.837822
			-0.287828  0.386123 -0.876393
			-0.012631  0.919382 -0.393164
			-0.318667  0.926786  0.198793
			-0.728226  0.374447  0.574000
			-0.358784  0.933421  0.000000
			-0.492467  0.767232 -0.410890
			-0.318665  0.926787 -0.198794
			-0.012631  0.919382  0.393164
			-0.728228  0.374446 -0.573998
			-0.492467  0.767232  0.410890
			-0.469195  0.634660 -0.614054
			 0.139725  0.834491 -0.533012
			-0.042569  0.000569 -0.999093
			-0.858008 -0.050605 -0.511137
			-0.226151 -0.673195 -0.704035
			-0.489170 -0.622795  0.610606
			-0.922043 -0.159713  0.352603
			-0.282807  0.528934 -0.800156
			-0.463850  0.492149 -0.736636
			 0.963097  0.124210 -0.238781
			-0.003124  0.118607 -0.992936
			-0.554928 -0.810284 -0.188400
			 0.329815 -0.063004 -0.941941
			-0.973709 -0.091069  0.208801
			 0.886206 -0.065279  0.458669
			 0.998157 -0.060679  0.000000
			-0.711509  0.393680 -0.582041
			-0.838854  0.544356  0.000000
			-0.949460  0.225493 -0.218354
			-0.974249  0.071520  0.213830
			-0.711509  0.393680  0.582041
			-0.949460  0.225493  0.218354
			-0.974249  0.071523 -0.213829
			-0.785423  0.113810  0.608406
			 0.168085  0.815861  0.553279
			 0.035270  0.544804  0.837822
			-0.287828  0.386123  0.876393
			-0.469193  0.634660  0.614056
			 0.139726  0.834493  0.533009
			-0.282812  0.528933  0.800154
			-0.226150 -0.673195  0.704034
			-0.463855  0.492147  0.736634
			-0.858008 -0.050604  0.511138
			-0.042571  0.000569  0.999093
			-0.489167 -0.622794 -0.610609
			-0.922043 -0.159709 -0.352604
			 0.963097  0.124210  0.238781
			-0.003124  0.118607  0.992936
			 0.329819 -0.062998  0.941940
			-0.554924 -0.810283  0.188414
			-0.973711 -0.091074 -0.208789
			 0.886206 -0.065279 -0.458669
			-0.785421  0.113813 -0.608408
			-0.806557  0.238294  0.541000
			-0.401058  0.167950  0.900525
			-0.806557  0.238294 -0.541000
			-0.401050  0.167949 -0.900529
			-0.577635 -0.641084 -0.505321
			-0.765701 -0.024294 -0.642738
			-0.661378 -0.665758 -0.345464
			-0.927886  0.223874 -0.298173
			-0.636112 -0.098447 -0.765291
			-0.656943 -0.749281 -0.083689
			 0.754599 -0.638609 -0.150859
			 0.631197 -0.624830  0.459542
			 0.951007 -0.309168  0.000000
			 0.631197 -0.624830 -0.459542
			-0.723812  0.263292 -0.637788
			-0.650890  0.258429 -0.713833
			-0.788694  0.614786  0.000000
			-0.723812  0.263292  0.637788
			-0.650890  0.258429  0.713833
			-0.927886  0.223874  0.298173
			-0.636112 -0.098447  0.765291
			-0.656943 -0.749281  0.083689
			-0.765701 -0.024294  0.642738
			-0.577635 -0.641084  0.505321
			-0.661378 -0.665758  0.345464
			 0.754599 -0.638609  0.150859
			-0.395352 -0.116991 -0.911049
			-0.124179 -0.981334  0.146844
			 0.107565 -0.717024 -0.688699
			 0.665101 -0.746753  0.000000
			-0.124179 -0.981334 -0.146844
			 0.107565 -0.717024  0.688699
			-0.960628  0.277838  0.000000
			-0.395352 -0.116991  0.911049
			-0.093032  0.164230 -0.982025
			-0.004825 -0.087795 -0.996127
			 0.333879 -0.533801 -0.776905
			 0.275477 -0.961308  0.000000
			 0.333879 -0.533801  0.776905
			-0.004825 -0.087795  0.996127
			-0.693554 -0.306439 -0.651980
			-0.749634 -0.661853  0.000000
			-0.693554 -0.306439  0.651980
			-0.093032  0.164230  0.982025
			-0.097518 -0.682709 -0.724154
			-0.418708 -0.908121  0.000000
			-0.097518 -0.682709  0.724154
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.000477  0.007348  0.000000
			 0.014288  0.014566 -0.008983
			 0.015190  0.014569  0.000000
			 0.009619  0.013082 -0.018384
			 0.002400  0.009881 -0.022961
			-0.015475  0.000026  0.000000
			-0.015211 -0.000247 -0.005395
			-0.015704 -0.000123 -0.015981
			-0.011618  0.003802 -0.023095
			 0.014288  0.014566  0.008983
			 0.009619  0.013082  0.018384
			 0.002400  0.009881  0.022961
			-0.015211 -0.000247  0.005395
			-0.015704 -0.000123  0.015981
			-0.011618  0.003802  0.023095
			 0.014579  0.015903 -0.010789
			 0.014249  0.016540  0.000000
			 0.014579  0.015903  0.010789
			 0.007894  0.015663 -0.017755
			-0.000640  0.015071 -0.021586
			-0.016862  0.007630 -0.005046
			-0.016781  0.007501  0.000000
			-0.016862  0.007630  0.005046
			-0.017464  0.009400 -0.016318
			-0.011001  0.010075 -0.021747
			-0.018330  0.008788 -0.009635
			 0.007894  0.015663  0.017755
			-0.000640  0.015071  0.021586
			-0.011001  0.010075  0.021747
			-0.017464  0.009400  0.016318
			-0.018330  0.008788  0.009635
			 0.010380  0.021241 -0.010502
			 0.011842  0.021323  0.000000
			 0.010380  0.021241  0.010502
			 0.004636  0.020300 -0.017309
			-0.002241  0.019708 -0.019629
			-0.019388  0.015698 -0.006481
			-0.018914  0.015939  0.000000
			-0.019388  0.015698  0.006481
			-0.017722  0.014739 -0.019459
			-0.012250  0.017369 -0.020577
			-0.021428  0.015517 -0.009942
			 0.004636  0.020300  0.017309
			-0.002241  0.019708  0.019629
			-0.012250  0.017369  0.020577
			-0.017722  0.014739  0.019459
			-0.021428  0.015517  0.009942
			 0.007425  0.030310 -0.010130
			 0.008394  0.032102  0.000000
			 0.007425  0.030310  0.010130
			 0.001457  0.028397 -0.017974
			-0.003442  0.026453 -0.019716
			-0.023451  0.021580 -0.006696
			-0.025561  0.021211 -0.010395
			-0.022774  0.021997  0.000000
			-0.023451  0.021580  0.006696
			-0.025561  0.021211  0.010395
			-0.018640  0.021625 -0.021130
			-0.013575  0.022578 -0.020722
			-0.021817  0.015928 -0.021499
			-0.023587  0.011688 -0.017530
			-0.025017  0.018690 -0.025716
			-0.030394  0.014549 -0.003789
			 0.001457  0.028397  0.017974
			-0.003442  0.026453  0.019716
			-0.013575  0.022578  0.020722
			-0.018640  0.021625  0.021130
			-0.023587  0.011688  0.017530
			-0.021817  0.015928  0.021499
			-0.025017  0.018690  0.025716
			-0.030394  0.014549  0.003789
			 0.007022  0.035882 -0.011210
			 0.007480  0.036566  0.000000
			 0.007022  0.035882  0.011210
			 0.002018  0.033258 -0.019027
			-0.003857  0.030103 -0.021805
			-0.026589  0.029103 -0.008549
			-0.026715  0.026944 -0.011162
			-0.024621  0.028813  0.000000
			-0.026589  0.029103  0.008549
			-0.026715  0.026944  0.011162
			-0.021210  0.025875 -0.020416
			-0.012941  0.028097 -0.022866
			-0.027884  0.024546 -0.020468
			-0.031623  0.013488 -0.017633
			-0.034714  0.017451 -0.023741
			-0.027711  0.012405 -0.025399
			-0.024576  0.007973 -0.010789
			-0.029423  0.014399 -0.029720
			-0.029024  0.018111 -0.032692
			-0.026414  0.002638 -0.005221
			-0.030186  0.012915  0.000000
			-0.039370  0.012822  0.000000
			-0.034462  0.020288 -0.007892
			 0.002018  0.033258  0.019027
			-0.003857  0.030103  0.021805
			-0.012941  0.028097  0.022866
			-0.021210  0.025875  0.020416
			-0.027884  0.024546  0.020468
			-0.034714  0.017451  0.023741
			-0.031623  0.013488  0.017633
			-0.027711  0.012405  0.025399
			-0.024576  0.007973  0.010789
			-0.029024  0.018111  0.032692
			-0.029423  0.014399  0.029720
			-0.026414  0.002638  0.005221
			-0.034462  0.020288  0.007892
			 0.004999  0.040642 -0.011416
			 0.005731  0.041244  0.000000
			 0.004999  0.040642  0.011416
			 0.000109  0.038323 -0.019289
			-0.006847  0.035605 -0.022860
			-0.015603  0.031364 -0.023008
			-0.028253  0.026602 -0.014452
			-0.036099  0.030642 -0.016406
			-0.035043  0.027800 -0.010300
			-0.009445  0.035028  0.000000
			-0.024533  0.029386 -0.017549
			-0.036099  0.030642  0.016406
			-0.028253  0.026602  0.014452
			-0.035043  0.027800  0.010300
			-0.024533  0.029386  0.017549
			-0.032999  0.023209 -0.029174
			-0.032976  0.026926 -0.018869
			-0.032425  0.005509 -0.015879
			-0.036323  0.002236 -0.012968
			-0.040438  0.017183 -0.021653
			-0.040527  0.015647 -0.014540
			-0.037911  0.014924 -0.013000
			-0.042885  0.026452 -0.019979
			-0.036393  0.023848 -0.021835
			-0.023497  0.000997 -0.012299
			-0.034928 -0.002055 -0.017501
			-0.033908  0.014130 -0.032981
			-0.032967  0.017082 -0.036167
			-0.037103  0.021392 -0.033444
			-0.024986 -0.002927 -0.006855
			-0.029806  0.002622  0.000000
			-0.039353  0.008616 -0.005062
			-0.046306 -0.000990  0.000000
			-0.035157  0.008877 -0.008498
			-0.036437  0.013546 -0.010659
			-0.039353  0.008616  0.005062
			-0.035157  0.008877  0.008498
			-0.036437  0.013546  0.010659
			-0.035702  0.020458 -0.011630
			 0.000109  0.038323  0.019289
			-0.006847  0.035605  0.022860
			-0.015603  0.031364  0.023008
			-0.032999  0.023209  0.029174
			-0.032976  0.026926  0.018869
			-0.042885  0.026452  0.019979
			-0.040438  0.017183  0.021653
			-0.036393  0.023848  0.021835
			-0.036323  0.002236  0.012968
			-0.032425  0.005509  0.015879
			-0.040527  0.015647  0.014540
			-0.037911  0.014924  0.013000
			-0.023497  0.000997  0.012299
			-0.034928 -0.002055  0.017501
			-0.032967  0.017082  0.036167
			-0.033908  0.014130  0.032981
			-0.037103  0.021392  0.033444
			-0.024986 -0.002927  0.006855
			-0.035702  0.020458  0.011630
			-0.047503  0.021059 -0.016744
			-0.040173  0.020995 -0.014277
			-0.047503  0.021059  0.016744
			-0.040173  0.020995  0.014277
			-0.036704 -0.006553 -0.009958
			-0.037875 -0.001656 -0.010524
			-0.032885 -0.009098 -0.017254
			-0.036805  0.004505 -0.009067
			-0.045237  0.020136 -0.022146
			-0.046334  0.017032 -0.017202
			-0.024081 -0.011636 -0.013666
			-0.026932 -0.006318 -0.005445
			-0.030842 -0.003894  0.000000
			-0.026932 -0.006318  0.005445
			-0.041901  0.002710 -0.004274
			-0.043840 -0.002431 -0.004160
			-0.054573 -0.004416  0.000000
			-0.041901  0.002710  0.004274
			-0.043840 -0.002431  0.004160
			-0.036805  0.004505  0.009067
			-0.045237  0.020136  0.022146
			-0.046334  0.017032  0.017202
			-0.037875 -0.001656  0.010524
			-0.036704 -0.006553  0.009958
			-0.032885 -0.009098  0.017254
			-0.024081 -0.011636  0.013666
			-0.042743 -0.007087 -0.004343
			-0.032062 -0.008120 -0.007826
			-0.037547 -0.008540 -0.005095
			-0.035921 -0.013747  0.000000
			-0.032062 -0.008120  0.007826
			-0.037547 -0.008540  0.005095
			-0.055182 -0.010081  0.000000
			-0.042743 -0.007087  0.004343
			-0.045376 -0.011586 -0.004733
			-0.041499 -0.012885 -0.004749
			-0.041663 -0.017364 -0.003687
			-0.043266 -0.018687  0.000000
			-0.041663 -0.017364  0.003687
			-0.041499 -0.012885  0.004749
			-0.051860 -0.014453 -0.004792
			-0.052301 -0.016283  0.000000
			-0.051860 -0.014453  0.004792
			-0.045376 -0.011586  0.004733
			-0.049042 -0.017590 -0.005584
			-0.047498 -0.019215  0.000000
			-0.049042 -0.017590  0.005584
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 3 1 
					0 4 3 
					0 5 6 
					6 7 0 
					0 7 8 
					8 4 0 
					2 9 0 
					9 10 0 
					10 11 0 
					12 5 0 
					0 13 12 
					14 13 0 
					0 11 14 
					2 1 15 
					15 1 3 
					16 2 15 
					17 9 2 
					17 2 16 
					18 15 3 
					18 3 4 
					19 18 4 
					19 4 8 
					20 6 5 
					21 20 5 
					5 12 22 
					5 22 21 
					6 20 7 
					7 23 24 
					8 7 24 
					25 23 7 
					20 25 7 
					24 19 8 
					10 9 17 
					10 17 26 
					11 10 26 
					11 26 27 
					14 11 27 
					13 22 12 
					28 29 13 
					28 13 14 
					13 29 30 
					13 30 22 
					14 27 28 
					15 31 32 
					16 15 32 
					31 15 18 
					32 17 16 
					32 33 17 
					26 17 33 
					34 31 18 
					34 18 19 
					35 34 19 
					35 19 24 
					20 21 36 
					25 20 36 
					37 36 21 
					21 38 37 
					38 21 22 
					38 22 30 
					23 39 40 
					24 23 40 
					41 23 25 
					41 39 23 
					40 35 24 
					41 25 36 
					26 33 42 
					27 26 42 
					27 42 43 
					28 27 43 
					28 43 44 
					44 29 28 
					44 45 29 
					30 29 46 
					29 45 46 
					38 30 46 
					31 47 48 
					32 31 48 
					47 31 34 
					48 33 32 
					48 49 33 
					42 33 49 
					50 47 34 
					50 34 35 
					51 50 35 
					51 35 40 
					52 36 37 
					52 53 36 
					53 41 36 
					54 52 37 
					37 38 55 
					37 55 54 
					38 56 55 
					38 46 56 
					39 57 58 
					40 39 58 
					59 39 60 
					39 59 61 
					57 39 61 
					41 60 39 
					58 51 40 
					41 62 60 
					62 41 53 
					42 49 63 
					43 42 63 
					43 63 64 
					44 43 64 
					44 64 65 
					65 45 44 
					65 66 45 
					67 45 68 
					69 68 45 
					69 45 66 
					45 67 46 
					67 70 46 
					56 46 70 
					47 71 72 
					48 47 72 
					71 47 50 
					72 49 48 
					72 73 49 
					63 49 73 
					74 71 50 
					74 50 51 
					75 74 51 
					75 51 58 
					76 52 54 
					77 53 52 
					76 77 52 
					53 77 62 
					78 76 54 
					54 55 79 
					54 79 78 
					55 56 80 
					55 80 79 
					70 80 56 
					57 81 82 
					58 57 82 
					57 61 83 
					81 57 83 
					82 75 58 
					61 59 60 
					60 84 85 
					86 60 85 
					86 61 60 
					87 84 60 
					62 87 60 
					88 89 61 
					86 88 61 
					61 89 83 
					90 62 91 
					92 91 62 
					93 92 62 
					62 90 87 
					77 93 62 
					63 73 94 
					64 63 94 
					64 94 95 
					65 64 95 
					65 95 96 
					96 66 65 
					96 97 66 
					98 69 66 
					98 66 97 
					99 100 67 
					99 67 101 
					67 68 69 
					67 69 101 
					67 100 102 
					67 102 70 
					69 103 104 
					69 104 101 
					98 103 69 
					91 70 105 
					70 91 92 
					70 92 106 
					102 105 70 
					70 106 80 
					71 107 108 
					72 71 108 
					107 71 74 
					108 73 72 
					108 109 73 
					94 73 109 
					110 107 74 
					74 75 111 
					110 74 111 
					75 82 112 
					111 75 112 
					76 113 114 
					114 115 76 
					115 93 76 
					77 76 93 
					116 76 78 
					116 117 76 
					76 117 113 
					78 79 116 
					118 119 79 
					79 120 118 
					79 106 120 
					106 79 80 
					79 121 116 
					119 121 79 
					82 81 112 
					81 83 117 
					81 117 112 
					83 113 117 
					89 122 83 
					123 83 122 
					113 83 123 
					84 124 125 
					85 84 126 
					84 127 126 
					87 124 84 
					125 128 84 
					128 127 84 
					85 126 129 
					122 86 85 
					122 85 130 
					130 85 129 
					122 88 86 
					131 87 90 
					87 131 132 
					124 87 132 
					88 133 134 
					89 88 134 
					88 135 133 
					122 135 88 
					89 134 122 
					136 90 137 
					137 90 91 
					136 131 90 
					91 105 137 
					92 138 139 
					140 138 92 
					141 140 92 
					92 93 141 
					139 142 92 
					92 142 143 
					92 143 144 
					144 106 92 
					93 145 141 
					115 145 93 
					94 109 146 
					147 95 94 
					147 94 146 
					148 96 95 
					148 95 147 
					148 97 96 
					121 98 97 
					148 121 97 
					121 119 98 
					98 149 103 
					149 98 150 
					150 98 119 
					151 152 99 
					152 100 99 
					99 101 149 
					153 99 149 
					151 99 153 
					154 155 100 
					152 156 100 
					100 155 102 
					100 157 154 
					100 156 157 
					101 104 149 
					105 102 158 
					159 158 102 
					159 102 155 
					149 160 103 
					160 104 103 
					160 161 104 
					161 162 104 
					104 162 149 
					137 105 163 
					105 158 163 
					144 164 106 
					106 164 120 
					116 107 110 
					116 108 107 
					109 108 116 
					146 109 116 
					116 110 111 
					116 111 112 
					116 112 117 
					123 114 113 
					114 123 129 
					114 129 165 
					166 114 165 
					166 145 114 
					115 114 145 
					147 146 116 
					148 147 116 
					121 148 116 
					119 118 150 
					151 150 118 
					167 151 118 
					167 118 168 
					118 164 168 
					164 118 120 
					134 135 122 
					130 123 122 
					123 130 129 
					125 124 132 
					169 170 125 
					171 169 125 
					132 171 125 
					172 125 170 
					125 172 128 
					126 173 129 
					174 173 126 
					174 126 127 
					165 174 127 
					166 165 127 
					127 128 145 
					166 127 145 
					145 128 141 
					140 128 172 
					141 128 140 
					165 129 173 
					131 175 132 
					136 175 131 
					175 171 132 
					133 135 134 
					176 136 137 
					176 175 136 
					177 176 137 
					137 163 178 
					137 178 177 
					139 138 179 
					138 140 179 
					180 181 139 
					179 180 139 
					182 142 139 
					139 181 183 
					139 183 182 
					140 172 179 
					182 143 142 
					182 184 143 
					184 157 143 
					143 157 144 
					144 157 164 
					149 162 160 
					149 150 153 
					151 153 150 
					151 185 152 
					185 151 167 
					152 185 186 
					156 152 186 
					159 155 154 
					154 187 188 
					154 188 189 
					154 189 159 
					187 154 184 
					157 184 154 
					156 186 167 
					156 167 168 
					164 157 156 
					164 156 168 
					159 190 158 
					158 190 163 
					159 189 190 
					160 162 161 
					163 190 178 
					174 165 173 
					185 167 186 
					170 169 191 
					169 192 193 
					191 169 193 
					169 171 192 
					172 170 180 
					180 170 191 
					171 175 192 
					179 172 180 
					192 175 176 
					193 192 176 
					194 193 176 
					194 176 177 
					177 178 194 
					178 190 195 
					178 195 196 
					178 196 194 
					180 191 181 
					191 197 181 
					181 197 198 
					181 198 183 
					183 184 182 
					183 187 184 
					198 187 183 
					198 188 187 
					196 195 188 
					196 188 198 
					195 189 188 
					195 190 189 
					191 199 197 
					191 193 200 
					199 191 200 
					193 194 201 
					200 193 201 
					202 201 194 
					194 203 202 
					203 194 196 
					204 196 198 
					203 196 204 
					199 205 197 
					197 205 206 
					197 207 208 
					197 208 198 
					206 207 197 
					204 198 208 
					199 200 201 
					209 199 201 
					199 209 205 
					209 201 202 
					210 209 202 
					202 203 211 
					202 211 210 
					203 204 208 
					203 208 211 
					205 209 210 
					206 205 210 
					210 207 206 
					210 211 207 
					207 211 208 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
