<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="127" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="250">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.242876 -0.871010  0.427028
			-0.009451 -0.937845  0.346926
			 0.274983 -0.667932  0.691557
			 0.026148 -0.993244  0.113062
			 0.452795 -0.560172  0.693674
			 0.255732 -0.850203  0.460169
			 0.063103 -0.701529  0.709842
			-0.214477 -0.952289  0.217131
			-0.233652 -0.972024  0.023993
			 0.623011 -0.117542  0.773331
			 0.567811  0.064138  0.820657
			-0.882034 -0.414470  0.224124
			-0.189557 -0.965026  0.181089
			 0.067809 -0.327894  0.942278
			 0.533754 -0.023041  0.845326
			-0.159874 -0.859949  0.484694
			-0.196467 -0.619754  0.759807
			-0.126838 -0.899274  0.418591
			 0.092915 -0.312677  0.945304
			 0.281833  0.147398  0.948074
			-0.246511 -0.962467 -0.113530
			-0.256258 -0.903768 -0.342835
			-0.195351 -0.851825 -0.486037
			-0.964283 -0.192654 -0.181778
			-0.974469 -0.066997  0.214294
			 0.719815  0.548986  0.424830
			 0.664616  0.422144  0.616506
			 0.476081  0.742364  0.471426
			 0.634564  0.695869  0.336295
			-0.947425 -0.146021  0.284716
			-0.699346  0.703858  0.124496
			-0.856319  0.483790  0.180736
			 0.060662  0.263050  0.962873
			-0.823087  0.335676  0.458093
			-0.699832 -0.067096  0.711150
			 0.190848  0.662953  0.723927
			-0.300515 -0.904139  0.303682
			-0.315147 -0.713661  0.625596
			-0.027941 -0.999534  0.012263
			-0.047560  0.716156  0.696318
			-0.055559 -0.932604 -0.356598
			-0.353422 -0.803210 -0.479528
			-0.278543 -0.452199 -0.847307
			-0.206390 -0.609827 -0.765189
			 0.039688 -0.234547 -0.971294
			 0.097329 -0.480293 -0.871692
			 0.005177 -0.741640 -0.670779
			-0.921239  0.058857 -0.384518
			-0.794877  0.604502  0.052420
			-0.828663  0.551408  0.096269
			 0.514860  0.835859 -0.190417
			 0.368459  0.885664  0.282553
			 0.527849  0.848280 -0.042388
			-0.995424 -0.082295  0.048564
			-0.964021  0.116498  0.238940
			-0.857521  0.509564  0.070733
			-0.959846  0.222082  0.171394
			-0.561450  0.632813  0.533218
			-0.482117  0.768491  0.420695
			-0.948989  0.264508  0.171628
			 0.000595 -0.756011  0.654558
			-0.025433 -0.972294  0.232372
			-0.473843 -0.683939  0.554708
			-0.806246 -0.270609  0.526059
			 0.179752 -0.973633 -0.140458
			 0.106765 -0.994192 -0.013560
			 0.913531 -0.406726 -0.005964
			 0.533340 -0.826601 -0.179665
			 0.137565  0.989951 -0.032742
			-0.210656  0.977545 -0.005571
			-0.293835  0.954891  0.042952
			-0.267436 -0.711239 -0.650090
			 0.468321 -0.190428 -0.862794
			 0.682143 -0.508760 -0.525208
			-0.557282 -0.578117 -0.596001
			-0.443878 -0.636412 -0.630834
			-0.195833 -0.090334 -0.976468
			-0.712165 -0.038532 -0.700953
			-0.240375  0.701349 -0.671066
			-0.081402  0.148270 -0.985591
			 0.312523  0.722650 -0.616528
			 0.544848  0.276044 -0.791795
			 0.324033 -0.250392 -0.912308
			 0.008705 -0.408194 -0.912854
			-0.640681 -0.401707 -0.654339
			-0.873395  0.470629 -0.125255
			-0.970415  0.200451  0.134586
			-0.851153  0.509861  0.124817
			-0.986428  0.106560  0.124919
			 0.555349  0.341690 -0.758179
			 0.212608  0.631611 -0.745564
			-0.183710  0.824168 -0.535722
			-0.131797  0.985668 -0.105305
			-0.874383  0.462246  0.147589
			-0.222456  0.933558  0.281037
			-0.976555 -0.180957  0.116600
			 0.569441 -0.028887  0.821525
			 0.406266  0.033818  0.913129
			 0.661254 -0.238000  0.711406
			 0.267567 -0.050348  0.962223
			 0.715756 -0.477515  0.509580
			-0.050603  0.076415  0.995791
			-0.799321  0.275121  0.534224
			 0.805943  0.508855  0.302528
			 0.906048  0.340071 -0.251853
			 0.498615  0.866822 -0.001705
			 0.049349  0.998465  0.025152
			-0.177812  0.980887  0.079016
			-0.131110  0.911759  0.389238
			 0.424952  0.806507  0.411049
			 0.178310  0.068657 -0.981576
			-0.240091 -0.106357 -0.964907
			 0.573553  0.653509 -0.493926
			-0.321848  0.143839 -0.935801
			-0.674562  0.439257 -0.593312
			-0.481784  0.875043 -0.046725
			 0.237932  0.120401 -0.963790
			-0.785971 -0.002226 -0.618259
			-0.102702  0.048891 -0.993510
			-0.927294 -0.104041  0.359586
			-0.974836  0.067702  0.212395
			-0.175811  0.423574 -0.888637
			-0.764974  0.452965 -0.457862
			-0.905442  0.422663  0.039131
			 0.635562  0.604472  0.480285
			 0.179078  0.811547 -0.556167
			-0.064923  0.992823 -0.100441
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.046059 -0.161847  0.017249
			 0.046947 -0.162285  0.015753
			 0.047813 -0.161855  0.016602
			 0.045819 -0.162088  0.016636
			 0.047144 -0.161178  0.017455
			 0.044895 -0.161979  0.018110
			 0.049857 -0.162555  0.015397
			 0.050163 -0.163457  0.013730
			 0.042394 -0.161788  0.015568
			 0.047794 -0.160908  0.017148
			 0.048847 -0.161372  0.016446
			 0.042512 -0.161731  0.016539
			 0.044100 -0.162113  0.017617
			 0.044394 -0.160782  0.019556
			 0.045373 -0.159706  0.019448
			 0.044157 -0.161802  0.018563
			 0.044131 -0.161266  0.019206
			 0.053033 -0.164426  0.013174
			 0.051288 -0.162351  0.015443
			 0.050415 -0.162008  0.015572
			 0.052710 -0.164411  0.012590
			 0.050038 -0.163283  0.011876
			 0.045754 -0.161991  0.011779
			 0.041619 -0.161354  0.012269
			 0.041856 -0.161571  0.014423
			 0.047945 -0.159941  0.016571
			 0.046018 -0.159019  0.018747
			 0.050477 -0.161422  0.014963
			 0.048938 -0.160537  0.015687
			 0.043522 -0.161450  0.018628
			 0.043187 -0.160643  0.014152
			 0.043990 -0.159601  0.014515
			 0.044814 -0.159463  0.019677
			 0.044132 -0.159067  0.019264
			 0.043702 -0.160228  0.019347
			 0.045210 -0.158539  0.018965
			 0.055030 -0.164243  0.014094
			 0.052439 -0.162623  0.015273
			 0.056456 -0.164778  0.012307
			 0.051625 -0.162000  0.015298
			 0.055615 -0.164644  0.010750
			 0.052760 -0.163871  0.011252
			 0.051127 -0.162400  0.010350
			 0.049824 -0.162656  0.010871
			 0.048146 -0.161401  0.010489
			 0.046773 -0.160889  0.010241
			 0.044872 -0.160651  0.009956
			 0.042598 -0.160640  0.010128
			 0.042338 -0.160826  0.011155
			 0.043821 -0.159804  0.013630
			 0.045760 -0.157193  0.010652
			 0.045312 -0.157608  0.017554
			 0.050256 -0.160973  0.013091
			 0.044366 -0.158308  0.015163
			 0.044275 -0.158670  0.014589
			 0.043552 -0.160169  0.014154
			 0.044011 -0.159112  0.014156
			 0.044182 -0.158532  0.018824
			 0.044394 -0.157656  0.017714
			 0.044054 -0.157550  0.016825
			 0.055819 -0.164301  0.014903
			 0.056218 -0.164728  0.014041
			 0.052697 -0.162351  0.016289
			 0.052203 -0.162258  0.015670
			 0.056778 -0.164903  0.012701
			 0.056637 -0.164886  0.011799
			 0.057779 -0.164460  0.013025
			 0.057217 -0.164679  0.011714
			 0.051679 -0.161495  0.012939
			 0.052369 -0.161681  0.014617
			 0.052622 -0.161458  0.015469
			 0.053941 -0.163589  0.009531
			 0.056088 -0.163722  0.009634
			 0.057165 -0.164290  0.010722
			 0.052013 -0.162269  0.009529
			 0.051779 -0.162618  0.010315
			 0.050650 -0.162253  0.010426
			 0.051579 -0.162123  0.010076
			 0.051066 -0.161902  0.010470
			 0.049780 -0.162024  0.010590
			 0.049923 -0.161430  0.011066
			 0.047610 -0.159743  0.010330
			 0.045852 -0.159561  0.009550
			 0.043676 -0.159390  0.008825
			 0.042840 -0.160280  0.009627
			 0.042444 -0.160488  0.010692
			 0.043018 -0.159212  0.010167
			 0.042789 -0.160118  0.010801
			 0.044246 -0.158168  0.012956
			 0.046105 -0.158231  0.009726
			 0.045010 -0.157588  0.009424
			 0.043832 -0.157261  0.009775
			 0.043827 -0.156844  0.010955
			 0.043916 -0.156957  0.013647
			 0.044467 -0.157383  0.017193
			 0.044045 -0.157879  0.014331
			 0.056563 -0.163615  0.015321
			 0.055732 -0.163148  0.015770
			 0.056922 -0.163850  0.015025
			 0.053973 -0.162262  0.016405
			 0.057465 -0.164243  0.014132
			 0.052933 -0.161871  0.016658
			 0.052422 -0.161788  0.016346
			 0.056884 -0.163316  0.014681
			 0.057605 -0.163968  0.012100
			 0.056367 -0.162729  0.013886
			 0.052933 -0.161776  0.012669
			 0.051946 -0.161663  0.010867
			 0.052833 -0.161390  0.016172
			 0.054011 -0.161723  0.015983
			 0.053678 -0.162489  0.008813
			 0.053042 -0.162174  0.008845
			 0.055459 -0.162731  0.009569
			 0.052569 -0.161975  0.009008
			 0.051862 -0.161784  0.009324
			 0.052106 -0.161452  0.010266
			 0.044558 -0.158714  0.008803
			 0.042985 -0.159060  0.008961
			 0.043824 -0.158891  0.008726
			 0.043871 -0.157988  0.011730
			 0.043027 -0.158150  0.010151
			 0.043345 -0.158601  0.008889
			 0.043005 -0.158092  0.009287
			 0.043216 -0.157434  0.010429
			 0.056258 -0.162839  0.015206
			 0.053185 -0.161659  0.009277
			 0.052190 -0.161392  0.009766
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					4 5 0 
					5 3 0 
					4 0 2 
					6 2 1 
					7 6 1 
					7 1 8 
					1 3 8 
					9 4 2 
					10 2 6 
					10 9 2 
					11 3 12 
					8 3 11 
					3 5 12 
					5 4 13 
					13 4 14 
					4 9 14 
					15 12 5 
					16 15 5 
					5 13 16 
					7 17 6 
					18 19 6 
					19 10 6 
					17 18 6 
					20 17 7 
					21 7 8 
					21 20 7 
					22 21 8 
					8 23 22 
					23 8 24 
					11 24 8 
					9 10 25 
					26 9 25 
					14 9 26 
					27 28 10 
					27 10 19 
					28 25 10 
					11 12 29 
					24 11 30 
					30 11 31 
					11 29 31 
					12 15 29 
					32 13 14 
					33 34 13 
					16 13 34 
					13 32 33 
					26 35 14 
					32 14 35 
					16 29 15 
					29 16 34 
					36 37 17 
					17 37 18 
					17 20 38 
					36 17 38 
					37 39 18 
					19 18 39 
					27 19 39 
					38 20 40 
					41 40 20 
					21 41 20 
					41 21 42 
					43 42 21 
					22 43 21 
					44 43 22 
					45 44 22 
					46 22 23 
					45 22 46 
					47 46 23 
					48 47 23 
					30 23 24 
					48 23 49 
					23 30 49 
					25 28 50 
					51 25 50 
					26 25 51 
					35 26 51 
					52 28 27 
					39 52 27 
					50 28 52 
					33 29 34 
					29 33 53 
					31 29 54 
					29 53 54 
					49 30 55 
					31 55 30 
					31 56 55 
					31 54 56 
					32 35 33 
					57 33 35 
					57 58 33 
					59 33 58 
					53 33 59 
					58 57 35 
					58 35 51 
					60 36 61 
					61 36 38 
					62 36 60 
					62 37 36 
					62 63 37 
					37 63 39 
					61 38 64 
					40 65 38 
					66 64 38 
					65 67 38 
					66 38 67 
					68 39 69 
					39 68 52 
					39 63 70 
					69 39 70 
					71 72 40 
					67 65 40 
					67 40 73 
					72 73 40 
					71 40 41 
					71 41 74 
					42 75 41 
					75 74 41 
					42 43 76 
					75 42 77 
					76 78 42 
					77 42 78 
					44 79 43 
					79 76 43 
					79 44 80 
					81 80 44 
					44 45 81 
					45 46 82 
					45 82 81 
					83 46 84 
					83 82 46 
					47 84 46 
					48 85 47 
					84 47 86 
					87 47 85 
					47 87 86 
					85 48 87 
					87 48 49 
					49 55 56 
					88 87 49 
					88 49 56 
					89 50 81 
					81 50 52 
					90 50 89 
					50 90 91 
					50 91 92 
					50 92 51 
					92 93 51 
					94 51 93 
					94 58 51 
					80 81 52 
					80 52 68 
					53 59 95 
					53 95 54 
					54 95 56 
					56 95 88 
					58 94 59 
					94 93 59 
					59 93 95 
					96 97 60 
					98 96 60 
					99 60 97 
					98 60 100 
					60 61 100 
					101 62 60 
					101 60 99 
					61 66 100 
					64 66 61 
					62 102 63 
					101 102 62 
					102 70 63 
					103 100 66 
					66 67 104 
					104 105 66 
					66 105 103 
					67 73 104 
					68 78 80 
					69 106 68 
					106 107 68 
					68 107 78 
					70 106 69 
					102 108 70 
					106 70 105 
					105 70 109 
					108 109 70 
					71 110 72 
					71 111 110 
					71 74 111 
					110 112 72 
					73 72 104 
					112 104 72 
					74 113 111 
					74 114 113 
					74 75 77 
					77 114 74 
					79 78 76 
					77 78 115 
					114 77 115 
					79 80 78 
					78 107 115 
					89 81 82 
					82 116 89 
					116 82 83 
					84 117 83 
					83 117 118 
					83 118 116 
					117 84 86 
					86 87 88 
					86 88 119 
					117 86 120 
					119 120 86 
					88 93 119 
					95 93 88 
					90 89 116 
					121 90 116 
					90 121 91 
					121 122 91 
					123 92 91 
					122 123 91 
					123 93 92 
					93 123 119 
					124 97 96 
					98 103 96 
					103 124 96 
					97 124 109 
					97 109 99 
					103 98 100 
					109 101 99 
					108 101 109 
					102 101 108 
					124 103 105 
					105 104 112 
					125 105 112 
					126 106 105 
					109 124 105 
					125 126 105 
					106 115 107 
					106 126 115 
					112 110 125 
					110 111 125 
					113 125 111 
					114 125 113 
					114 126 125 
					126 114 115 
					121 116 118 
					121 118 117 
					117 122 121 
					120 122 117 
					119 123 120 
					122 120 123 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
