<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="134" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="264">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.553355 -0.546822 -0.628319
			-0.372355 -0.739848 -0.560337
			-0.619085 -0.785309 -0.004849
			-0.744152 -0.630315 -0.221228
			 0.107873 -0.307193 -0.945514
			-0.285562  0.052548 -0.956919
			-0.399404 -0.223792 -0.889041
			-0.005622 -0.896414 -0.443183
			 0.067442 -0.994369  0.081739
			-0.730153 -0.669521  0.136450
			-0.643166 -0.616627  0.453991
			-0.395855 -0.904240  0.160154
			-0.575704 -0.688667 -0.440797
			-0.667524 -0.664287  0.336355
			 0.451392 -0.639720 -0.622097
			 0.647578 -0.307366 -0.697257
			-0.030411  0.093940 -0.995113
			 0.217985  0.036715 -0.975261
			 0.461743  0.567746 -0.681512
			 0.834299  0.329787 -0.441798
			 0.042342  0.645905 -0.762242
			 0.062195  0.887245 -0.457087
			 0.525239  0.322790 -0.787357
			-0.208557 -0.410672 -0.887611
			 0.736842 -0.675773 -0.019853
			 0.227826 -0.973305  0.027813
			 0.839270 -0.540115  0.062471
			-0.725467 -0.503235  0.469524
			-0.159346 -0.822147  0.546520
			-0.402493 -0.129022  0.906285
			-0.700394 -0.248801  0.668989
			-0.213103 -0.212481  0.953645
			 0.163896 -0.974260  0.154777
			-0.638147 -0.768877  0.039961
			-0.438478 -0.778252 -0.449512
			-0.226076 -0.563805 -0.794364
			-0.133966 -0.562626 -0.815785
			-0.540370 -0.264633  0.798730
			-0.331758 -0.206979  0.920378
			-0.615078 -0.653223  0.441564
			-0.278430 -0.273937  0.920562
			-0.355427 -0.419702  0.835178
			 0.956930 -0.144821 -0.251619
			 0.585899  0.809937  0.026911
			 0.886417  0.459452  0.056293
			 0.452484  0.871433 -0.189374
			 0.552838  0.829409 -0.080323
			 0.719214  0.410603 -0.560479
			 0.712037  0.702134  0.003349
			 0.229437 -0.184196 -0.955736
			 0.986440 -0.024913  0.162220
			 0.704064 -0.710111  0.006052
			 0.932906  0.359293  0.024378
			 0.995609 -0.092282  0.015698
			-0.492589  0.179395  0.851571
			 0.570119 -0.565153  0.596294
			-0.292173 -0.021755  0.956118
			-0.211359 -0.047353  0.976261
			 0.203706 -0.011527  0.978964
			-0.671193 -0.631019  0.388991
			-0.721264 -0.691715  0.036166
			-0.669297 -0.671343 -0.318341
			 0.380507 -0.400099 -0.833748
			-0.215784 -0.812421 -0.541672
			 0.490772 -0.027422 -0.870857
			-0.114887  0.364095  0.924249
			 0.344088  0.492870  0.799176
			 0.607485  0.521616  0.599065
			-0.674582 -0.382538  0.631350
			-0.281293 -0.218858  0.934331
			 0.059629 -0.106135  0.992562
			 0.391207  0.234319  0.889973
			 0.481341  0.861946  0.159250
			 0.384361  0.923168  0.005169
			 0.811126  0.583975 -0.032371
			 0.808064  0.446781 -0.383952
			 0.826822  0.273378  0.491559
			 0.840011 -0.108856  0.531537
			 0.005903  0.832922  0.553359
			 0.147872  0.795848  0.587163
			-0.036678  0.424459  0.904704
			 0.184240  0.242451  0.952509
			-0.968057  0.037499  0.247909
			-0.866983  0.410152 -0.283048
			-0.374942 -0.647635 -0.663316
			-0.749442  0.639241 -0.172358
			-0.718703 -0.270180 -0.640679
			 0.106410 -0.577318 -0.809556
			 0.377805 -0.151053 -0.913480
			 0.923844  0.272411 -0.268896
			 0.709098  0.168180 -0.684760
			 0.874473  0.436080  0.212440
			-0.772520  0.600539  0.206314
			-0.674222  0.318032  0.666544
			 0.055240  0.181771  0.981788
			 0.573232  0.279873  0.770115
			 0.404994  0.843975  0.351691
			 0.606638  0.617968  0.500105
			-0.343295  0.916965 -0.203280
			-0.253097  0.963360 -0.088770
			-0.178952 -0.362393 -0.914684
			-0.742041  0.160621 -0.650827
			-0.654642  0.667558 -0.354698
			-0.779577  0.626270 -0.006827
			-0.661526  0.748238 -0.050236
			-0.844013  0.510409 -0.164694
			-0.905412  0.413500 -0.096156
			-0.726812  0.595984 -0.341392
			-0.432807  0.901450  0.008075
			 0.076121  0.348786 -0.934106
			 0.092726 -0.158327 -0.983023
			 0.764472  0.614142 -0.195990
			 0.462135  0.574751 -0.675346
			 0.710928  0.584762  0.390685
			-0.759162  0.589463 -0.276057
			-0.430616  0.650834  0.625288
			 0.297811  0.437558  0.848441
			-0.454443  0.885849 -0.093564
			-0.699735  0.692941 -0.173794
			-0.439673  0.237627 -0.866153
			-0.353509  0.793786 -0.494909
			-0.041028  0.908865 -0.415069
			-0.503209  0.823254 -0.262743
			-0.761541  0.638682 -0.110187
			-0.751000  0.654439 -0.087794
			-0.942661  0.136050 -0.304765
			-0.975848  0.158985 -0.149817
			-0.846665  0.530352 -0.043413
			-0.610527  0.771793 -0.177744
			 0.181003  0.704751  0.685977
			-0.342548  0.924524  0.167079
			-0.542378  0.825071  0.158375
			-0.934385  0.328167 -0.138678
			-0.919115  0.361065 -0.157671
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.037679 -0.158068 -0.018509
			 0.039684 -0.160332 -0.017770
			 0.039308 -0.160188 -0.016532
			 0.036769 -0.156972 -0.017893
			 0.042435 -0.157434 -0.019592
			 0.038347 -0.155560 -0.019817
			 0.036987 -0.155634 -0.019238
			 0.041080 -0.160670 -0.017855
			 0.040640 -0.160881 -0.016552
			 0.037207 -0.157574 -0.014793
			 0.039789 -0.159618 -0.011345
			 0.039965 -0.160156 -0.013401
			 0.034751 -0.155115 -0.017620
			 0.032629 -0.153364 -0.014996
			 0.042432 -0.159915 -0.018294
			 0.042820 -0.158811 -0.018926
			 0.039747 -0.155652 -0.020091
			 0.041336 -0.156326 -0.019967
			 0.041466 -0.155298 -0.019620
			 0.042988 -0.156711 -0.018965
			 0.037859 -0.154603 -0.018921
			 0.039169 -0.154444 -0.019321
			 0.036708 -0.153470 -0.019299
			 0.035619 -0.154367 -0.019184
			 0.042908 -0.159876 -0.017364
			 0.040600 -0.160400 -0.014138
			 0.042650 -0.159126 -0.014524
			 0.036673 -0.156196 -0.013119
			 0.041040 -0.160062 -0.011006
			 0.038609 -0.156567 -0.011096
			 0.037642 -0.156801 -0.011760
			 0.040580 -0.159213 -0.010612
			 0.041131 -0.160395 -0.012108
			 0.028562 -0.149365 -0.015494
			 0.028725 -0.148635 -0.020908
			 0.032962 -0.151655 -0.020145
			 0.031093 -0.149409 -0.021378
			 0.036610 -0.155188 -0.012330
			 0.033362 -0.152311 -0.013107
			 0.027798 -0.148372 -0.014730
			 0.031568 -0.150193 -0.012815
			 0.026863 -0.146003 -0.013318
			 0.043442 -0.158121 -0.018268
			 0.041709 -0.155200 -0.017464
			 0.043142 -0.156552 -0.017112
			 0.038108 -0.154083 -0.017505
			 0.041331 -0.155209 -0.015312
			 0.035202 -0.151685 -0.019615
			 0.034253 -0.150103 -0.017290
			 0.034891 -0.152186 -0.019927
			 0.043410 -0.157911 -0.016460
			 0.042618 -0.159570 -0.012679
			 0.042813 -0.157130 -0.014874
			 0.043401 -0.157732 -0.012761
			 0.037687 -0.155357 -0.011577
			 0.042306 -0.159261 -0.010722
			 0.038605 -0.155943 -0.010996
			 0.040375 -0.157114 -0.011004
			 0.042417 -0.157497 -0.010232
			 0.023192 -0.145345 -0.016178
			 0.023069 -0.145485 -0.017194
			 0.023729 -0.145173 -0.022051
			 0.029394 -0.146380 -0.023327
			 0.026859 -0.146945 -0.022644
			 0.032069 -0.148930 -0.021395
			 0.036911 -0.154389 -0.012407
			 0.036118 -0.152912 -0.012919
			 0.032366 -0.149006 -0.012949
			 0.023758 -0.143968 -0.013874
			 0.025194 -0.143587 -0.012764
			 0.027855 -0.145136 -0.012906
			 0.029730 -0.145868 -0.013123
			 0.037863 -0.153703 -0.013908
			 0.040938 -0.154589 -0.013224
			 0.042867 -0.156009 -0.013045
			 0.032584 -0.148162 -0.020631
			 0.042888 -0.156910 -0.010758
			 0.043002 -0.157776 -0.010688
			 0.039759 -0.154390 -0.011459
			 0.037214 -0.153794 -0.012774
			 0.040673 -0.155338 -0.010596
			 0.041911 -0.156207 -0.010313
			 0.021866 -0.143457 -0.016809
			 0.021872 -0.143914 -0.019432
			 0.026037 -0.145655 -0.024230
			 0.023647 -0.143964 -0.022700
			 0.024325 -0.143920 -0.024068
			 0.027405 -0.145067 -0.024903
			 0.028188 -0.143019 -0.025385
			 0.030616 -0.144963 -0.022277
			 0.029227 -0.142549 -0.024662
			 0.029884 -0.144086 -0.016269
			 0.022950 -0.143044 -0.014633
			 0.024217 -0.142161 -0.013064
			 0.026190 -0.142688 -0.012640
			 0.028873 -0.144443 -0.013266
			 0.040782 -0.154714 -0.011260
			 0.042576 -0.156063 -0.010860
			 0.023447 -0.142947 -0.015449
			 0.023720 -0.143550 -0.019362
			 0.026594 -0.143906 -0.025499
			 0.025147 -0.143227 -0.024959
			 0.026523 -0.141628 -0.024931
			 0.027220 -0.140480 -0.023270
			 0.025784 -0.142559 -0.019551
			 0.026643 -0.141576 -0.019505
			 0.027133 -0.141149 -0.020641
			 0.025065 -0.142853 -0.024399
			 0.024840 -0.143066 -0.019230
			 0.026877 -0.142341 -0.025747
			 0.027085 -0.142963 -0.025664
			 0.029032 -0.140408 -0.022895
			 0.028512 -0.141302 -0.024648
			 0.028407 -0.141152 -0.014380
			 0.025757 -0.141649 -0.016179
			 0.024907 -0.140982 -0.013500
			 0.027384 -0.141639 -0.013215
			 0.024612 -0.143285 -0.018206
			 0.025941 -0.142145 -0.017869
			 0.026060 -0.142631 -0.025662
			 0.027188 -0.140404 -0.024349
			 0.027616 -0.140130 -0.023711
			 0.027736 -0.139504 -0.022474
			 0.025991 -0.142011 -0.019083
			 0.025648 -0.142631 -0.018709
			 0.027091 -0.140373 -0.020115
			 0.026795 -0.140084 -0.019129
			 0.026528 -0.141575 -0.018695
			 0.027420 -0.139146 -0.020831
			 0.026287 -0.140230 -0.013958
			 0.026624 -0.139476 -0.015273
			 0.025782 -0.139954 -0.014289
			 0.026466 -0.139873 -0.015941
			 0.026724 -0.141108 -0.017877
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 2 3 
					1 0 4 
					5 0 6 
					4 0 5 
					6 0 3 
					2 1 7 
					1 4 7 
					7 8 2 
					9 3 2 
					9 2 10 
					2 11 10 
					11 2 8 
					12 3 13 
					6 3 12 
					3 9 13 
					4 14 7 
					4 15 14 
					4 16 17 
					4 5 16 
					17 18 4 
					18 19 4 
					15 4 19 
					6 20 5 
					21 5 20 
					16 5 21 
					6 22 20 
					23 6 12 
					22 6 23 
					8 7 24 
					7 14 24 
					11 8 25 
					8 26 25 
					8 24 26 
					13 9 27 
					10 27 9 
					10 11 28 
					29 30 10 
					10 31 29 
					10 28 31 
					10 30 27 
					28 11 32 
					25 32 11 
					33 12 13 
					33 34 12 
					12 35 23 
					12 36 35 
					36 12 34 
					13 37 38 
					33 13 39 
					40 13 38 
					13 40 41 
					39 13 41 
					37 13 27 
					24 14 42 
					14 15 42 
					15 19 42 
					18 17 16 
					21 18 16 
					43 19 18 
					21 43 18 
					19 43 44 
					44 42 19 
					22 45 20 
					45 21 20 
					46 21 45 
					21 46 43 
					22 47 48 
					45 22 48 
					22 49 47 
					23 49 22 
					49 23 35 
					24 42 50 
					50 26 24 
					51 25 26 
					32 25 51 
					52 26 50 
					52 53 26 
					53 51 26 
					27 30 54 
					54 37 27 
					32 55 28 
					55 31 28 
					29 54 30 
					56 54 29 
					56 29 57 
					29 31 57 
					31 58 57 
					55 58 31 
					55 32 51 
					59 33 39 
					33 60 61 
					33 61 34 
					59 60 33 
					62 34 63 
					36 34 62 
					34 61 63 
					36 49 35 
					36 64 49 
					36 62 64 
					37 65 38 
					37 54 65 
					38 65 66 
					66 40 38 
					41 59 39 
					40 66 67 
					40 67 41 
					59 41 68 
					68 41 69 
					70 69 41 
					71 70 41 
					67 71 41 
					42 44 50 
					52 43 46 
					43 52 44 
					52 50 44 
					45 48 72 
					73 46 45 
					45 72 73 
					46 74 52 
					74 46 73 
					75 48 47 
					47 64 75 
					49 64 47 
					48 67 66 
					66 72 48 
					75 67 48 
					55 51 53 
					52 74 53 
					53 76 77 
					74 76 53 
					55 53 77 
					54 78 79 
					65 54 79 
					54 56 80 
					54 80 78 
					55 77 58 
					80 56 57 
					80 57 81 
					57 58 81 
					58 76 81 
					76 58 77 
					60 59 82 
					82 59 68 
					83 61 60 
					82 83 60 
					61 84 63 
					83 85 61 
					61 85 86 
					84 61 86 
					63 87 62 
					88 62 87 
					75 64 62 
					62 89 75 
					89 62 90 
					90 62 88 
					87 63 84 
					65 79 66 
					72 66 79 
					89 67 75 
					71 67 91 
					89 91 67 
					92 68 93 
					68 92 82 
					68 69 93 
					69 70 94 
					94 93 69 
					95 94 70 
					70 71 95 
					71 91 95 
					79 73 72 
					74 73 96 
					73 79 78 
					73 78 96 
					97 76 74 
					74 96 97 
					76 97 81 
					80 96 78 
					97 80 81 
					80 97 96 
					82 98 83 
					82 92 98 
					83 99 85 
					99 83 98 
					84 86 100 
					84 100 87 
					86 85 101 
					102 85 103 
					85 104 105 
					85 105 106 
					101 85 107 
					107 85 102 
					85 99 108 
					85 106 103 
					104 85 108 
					101 100 86 
					88 87 100 
					109 88 110 
					110 88 100 
					90 88 109 
					91 89 111 
					111 89 90 
					90 112 111 
					112 90 109 
					91 111 113 
					95 91 113 
					93 114 92 
					98 92 114 
					93 94 115 
					93 115 114 
					94 95 116 
					94 116 115 
					95 113 116 
					99 98 117 
					117 98 118 
					98 114 118 
					99 117 108 
					100 119 110 
					101 119 100 
					107 119 101 
					120 102 103 
					119 107 102 
					102 109 119 
					102 120 109 
					121 103 122 
					103 121 120 
					122 103 106 
					123 105 104 
					104 108 117 
					117 124 104 
					123 104 124 
					105 125 106 
					105 126 125 
					126 105 127 
					123 127 105 
					106 125 128 
					106 128 122 
					110 119 109 
					120 112 109 
					111 128 113 
					128 111 122 
					121 111 112 
					111 121 122 
					112 120 121 
					116 113 129 
					130 129 113 
					128 130 113 
					114 115 131 
					114 130 132 
					114 131 130 
					133 114 132 
					118 114 133 
					129 131 115 
					115 116 129 
					124 117 118 
					118 133 127 
					123 124 118 
					123 118 127 
					125 126 128 
					126 132 128 
					126 133 132 
					127 133 126 
					132 130 128 
					131 129 130 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
