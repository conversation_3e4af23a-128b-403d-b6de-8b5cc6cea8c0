<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="83" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="162">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.121126 -0.174726 -0.977138
			-0.324815  0.084002 -0.942040
			-0.258180  0.781421 -0.568088
			 0.257943  0.806183 -0.532479
			 0.272477 -0.766924 -0.581019
			 0.302514 -0.788904 -0.534898
			 0.289927 -0.025192 -0.956717
			 0.187781 -0.695542 -0.693513
			-0.853329  0.479610 -0.204459
			-0.740014 -0.398208 -0.542043
			-0.628527  0.773796  0.078695
			-0.039496  0.919321 -0.391521
			 0.816589  0.469746 -0.335442
			 0.542471  0.495205 -0.678600
			 0.758921  0.651023 -0.014417
			 0.275945  0.902133 -0.331678
			-0.704388 -0.683895 -0.190065
			-0.325644 -0.892909 -0.310919
			 0.608309  0.103392 -0.786937
			 0.458327 -0.474901 -0.751270
			 0.265087 -0.568289 -0.778959
			 0.696003 -0.431218 -0.574135
			-0.916764  0.231952  0.325179
			-0.945360  0.178554  0.272788
			-0.459553  0.671144  0.581702
			-0.255015  0.961583  0.101617
			-0.750053  0.366918  0.550265
			 0.174452  0.931814 -0.318259
			 0.983421  0.165292 -0.074578
			 0.838952 -0.026542 -0.543557
			 0.726768  0.353360  0.589021
			 0.691305  0.576722  0.435303
			 0.467102  0.882631  0.052713
			 0.990814  0.088974 -0.101840
			-0.796091 -0.026515  0.604596
			-0.554803 -0.831911  0.010862
			-0.714971 -0.283249  0.639208
			-0.115021 -0.750945 -0.650271
			 0.603702 -0.304887 -0.736605
			-0.063231 -0.551945 -0.831480
			-0.809548 -0.076952  0.581988
			-0.720434  0.155845  0.675786
			-0.277891  0.703034  0.654614
			-0.412986  0.398628  0.818864
			-0.116343  0.904435  0.410440
			 0.068339  0.996046  0.056767
			-0.638631 -0.145249  0.755681
			 0.244316  0.327248  0.912808
			 0.256675 -0.156895  0.953678
			 0.748137  0.100312  0.655918
			 0.223038  0.139402  0.964791
			 0.150006  0.824335  0.545866
			 0.057236  0.561976  0.825171
			 0.895071 -0.160173  0.416164
			 0.983231 -0.168160  0.070558
			-0.512599  0.028200  0.858165
			-0.452072 -0.877814 -0.158344
			-0.487862  0.460183  0.741769
			-0.385791 -0.894229  0.226980
			-0.599170 -0.379411  0.705012
			 0.810044 -0.502707 -0.301851
			 0.286329 -0.519749 -0.804908
			-0.144201 -0.941768 -0.303776
			-0.272153 -0.875004 -0.400376
			-0.340058  0.096125  0.935479
			-0.507988 -0.373055  0.776388
			-0.165698  0.274456  0.947216
			 0.081101  0.189026  0.978617
			 0.380884 -0.486706  0.786158
			-0.092989 -0.868000  0.487780
			 0.015638 -0.608559  0.793354
			 0.134202  0.133576  0.981910
			 0.755615 -0.476475  0.449464
			 0.460053 -0.705847  0.538637
			 0.731715 -0.628895  0.262837
			-0.615556  0.338610  0.711642
			-0.563189  0.508228  0.651554
			-0.225069  0.019410  0.974150
			 0.380698 -0.915478 -0.130264
			 0.239338 -0.907714  0.344635
			-0.702892  0.049703  0.709558
			-0.268015  0.438983  0.857591
			-0.711139  0.522322  0.470597
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.002791 -0.010802  0.013712
			-0.008368 -0.012117  0.013576
			-0.006227 -0.005999  0.019078
			-0.001091 -0.006372  0.017515
			-0.007909 -0.012536  0.014026
			-0.007617 -0.012699  0.014688
			-0.000987 -0.010584  0.014097
			-0.000494 -0.012897  0.016195
			-0.009177 -0.009207  0.017151
			-0.009708 -0.014488  0.015533
			-0.006400 -0.005650  0.020172
			-0.003519 -0.005579  0.019102
			 0.003013 -0.010693  0.016508
			 0.001978 -0.010222  0.015438
			-0.000791 -0.005790  0.020325
			-0.001689 -0.005491  0.019841
			-0.010097 -0.016565  0.018456
			-0.009056 -0.017487  0.020225
			 0.002216 -0.010920  0.015356
			 0.002385 -0.011413  0.015601
			 0.003258 -0.016033  0.020187
			 0.003239 -0.012300  0.017176
			-0.009096 -0.011887  0.020008
			-0.010015 -0.014486  0.019839
			-0.004644 -0.005772  0.022469
			-0.005218 -0.005167  0.020918
			-0.006913 -0.007970  0.022782
			-0.002003 -0.005135  0.020423
			 0.003476 -0.011808  0.017776
			 0.002814 -0.011144  0.015953
			 0.002424 -0.016092  0.027901
			-0.000745 -0.006716  0.023729
			-0.001315 -0.005069  0.021285
			 0.003732 -0.015708  0.020432
			-0.009092 -0.016033  0.022175
			-0.008198 -0.018093  0.021001
			-0.007076 -0.016816  0.023808
			-0.006940 -0.018561  0.021244
			 0.004109 -0.019388  0.022363
			-0.003027 -0.020575  0.022666
			-0.007653 -0.011263  0.022316
			-0.008356 -0.012954  0.021333
			-0.003552 -0.005604  0.022888
			-0.004808 -0.009255  0.025027
			-0.002989 -0.005079  0.022427
			-0.002607 -0.004869  0.021445
			-0.006451 -0.011171  0.024242
			 0.001621 -0.014616  0.027841
			 0.002323 -0.018405  0.028242
			 0.003153 -0.017429  0.027982
			 0.002073 -0.016547  0.028148
			-0.002104 -0.005485  0.023104
			-0.001692 -0.007977  0.025261
			 0.003779 -0.020365  0.026713
			 0.004536 -0.021513  0.024183
			-0.005184 -0.013940  0.023544
			-0.004869 -0.020067  0.022683
			-0.004911 -0.016507  0.025546
			-0.002708 -0.021179  0.023933
			-0.002433 -0.019899  0.028090
			 0.004300 -0.022027  0.023604
			 0.003804 -0.022214  0.023276
			 0.002064 -0.022265  0.023175
			-0.002635 -0.021163  0.023152
			-0.000978 -0.011626  0.027022
			-0.003518 -0.012404  0.025346
			-0.000290 -0.014894  0.027606
			 0.001685 -0.017078  0.028214
			 0.003351 -0.020710  0.027076
			 0.000956 -0.021507  0.025801
			-0.000378 -0.020255  0.028184
			 0.000936 -0.017747  0.028623
			 0.004065 -0.021356  0.025394
			 0.003565 -0.021688  0.025807
			 0.004235 -0.022046  0.024398
			-0.002814 -0.015117  0.023811
			-0.003015 -0.016826  0.027882
			-0.002162 -0.018023  0.028592
			 0.003819 -0.022497  0.023781
			 0.003651 -0.022426  0.024456
			-0.001664 -0.013749  0.026419
			-0.000637 -0.016111  0.028194
			-0.002091 -0.015488  0.026291
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 2 3 
					4 1 0 
					5 4 0 
					3 6 0 
					7 0 6 
					0 7 5 
					1 8 2 
					9 8 1 
					9 1 4 
					2 8 10 
					11 3 2 
					2 10 11 
					12 13 3 
					14 12 3 
					15 14 3 
					11 15 3 
					3 13 6 
					9 4 5 
					9 5 16 
					5 17 16 
					7 17 5 
					13 18 6 
					6 19 7 
					18 19 6 
					7 20 17 
					7 19 21 
					20 7 21 
					22 8 9 
					10 8 22 
					9 23 22 
					9 16 23 
					10 24 25 
					11 10 25 
					10 22 26 
					24 10 26 
					27 11 25 
					15 11 27 
					13 12 18 
					28 12 14 
					29 12 28 
					18 12 29 
					30 14 31 
					31 14 32 
					14 27 32 
					14 15 27 
					30 33 14 
					28 14 33 
					23 16 34 
					34 16 17 
					35 36 17 
					20 37 17 
					17 36 34 
					37 35 17 
					18 29 19 
					29 21 19 
					20 33 38 
					20 39 37 
					21 33 20 
					39 20 38 
					28 21 29 
					21 28 33 
					40 26 22 
					22 23 41 
					22 41 40 
					23 34 41 
					25 24 42 
					26 43 24 
					24 43 42 
					25 42 44 
					44 45 25 
					45 27 25 
					26 40 46 
					26 46 43 
					32 27 45 
					47 30 31 
					48 49 30 
					50 30 47 
					50 48 30 
					33 30 49 
					32 51 31 
					52 31 51 
					47 31 52 
					51 32 45 
					53 33 49 
					33 53 54 
					38 33 54 
					41 34 55 
					36 55 34 
					36 35 56 
					37 56 35 
					57 55 36 
					36 56 58 
					36 58 59 
					36 59 57 
					37 39 56 
					54 60 38 
					38 60 61 
					38 61 39 
					39 62 63 
					39 61 62 
					56 39 63 
					41 55 40 
					46 40 55 
					44 42 51 
					51 42 52 
					52 42 43 
					52 43 64 
					43 46 64 
					44 51 45 
					55 65 46 
					46 65 64 
					47 66 67 
					50 47 67 
					52 64 47 
					64 66 47 
					68 49 48 
					48 69 68 
					50 67 48 
					69 48 70 
					70 48 71 
					71 48 67 
					49 68 53 
					53 72 54 
					73 53 68 
					73 72 53 
					54 74 60 
					54 72 74 
					55 57 75 
					65 55 75 
					56 63 58 
					59 76 57 
					57 76 75 
					58 70 59 
					62 58 63 
					58 69 70 
					58 62 69 
					59 77 76 
					70 77 59 
					60 78 61 
					60 74 78 
					62 61 78 
					62 79 69 
					62 78 79 
					64 65 80 
					64 80 66 
					65 75 80 
					71 67 66 
					66 80 81 
					81 71 66 
					69 73 68 
					79 73 69 
					77 70 71 
					71 81 77 
					79 72 73 
					72 79 74 
					74 79 78 
					82 75 76 
					80 75 82 
					81 82 76 
					81 76 77 
					80 82 81 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
