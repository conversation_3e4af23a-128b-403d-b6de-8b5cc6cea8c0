<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.647858 -0.761761  0.000337
			 0.454535 -0.870644  0.188087
			-0.026340 -0.999653  0.000015
			 0.951545 -0.095730  0.292230
			 0.972366 -0.233461  0.000223
			 0.454637 -0.870642 -0.187850
			 0.951641 -0.095658 -0.291939
			 0.463545 -0.740659  0.486365
			 0.202906 -0.640726  0.740472
			-0.370267 -0.928788 -0.015999
			-0.478783 -0.877933 -0.000014
			-0.415591 -0.893828  0.168390
			-0.189650 -0.668053  0.719541
			 0.463540 -0.740664 -0.486362
			 0.202905 -0.640730 -0.740469
			-0.370264 -0.928789  0.015973
			-0.415586 -0.893831 -0.168388
			-0.189644 -0.668058 -0.719537
			 0.977797  0.209555  0.000173
			 0.907698  0.242243  0.342640
			 0.655983  0.143100  0.741086
			 0.907805  0.242285 -0.342327
			 0.655984  0.143104 -0.741085
			 0.235346  0.152308  0.959904
			-0.871160 -0.489527 -0.037997
			-0.980467 -0.196686 -0.000020
			-0.871159 -0.489528  0.037993
			-0.252847 -0.035003  0.966873
			-0.738281 -0.408625  0.536626
			-0.921779 -0.328925 -0.205261
			 0.235346  0.152308 -0.959904
			-0.738281 -0.408625 -0.536626
			-0.252847 -0.035003 -0.966873
			-0.921779 -0.328925  0.205261
			 0.993599 -0.112965  0.000372
			 0.902752 -0.197091  0.382352
			 0.614369  0.201344  0.762896
			 0.902871 -0.197097 -0.382068
			 0.614371  0.201343 -0.762895
			 0.219345  0.156205  0.963062
			-0.948043  0.153044 -0.278912
			-0.973782  0.227484 -0.000079
			-0.948053  0.153036  0.278881
			-0.145260  0.031898  0.988879
			-0.121195 -0.530709  0.838844
			-0.767383 -0.455286 -0.451484
			 0.219345  0.156205 -0.963062
			-0.121195 -0.530709 -0.838844
			-0.145260  0.031898 -0.988879
			-0.767383 -0.455286  0.451484
			 0.980297 -0.197531  0.000217
			 0.912276 -0.244014  0.328954
			 0.528795 -0.298403  0.794564
			 0.912335 -0.244008 -0.328792
			 0.528795 -0.298403 -0.794564
			 0.173250 -0.318544  0.931941
			-0.944167  0.184058 -0.273261
			-0.396207  0.186875 -0.898943
			-0.990428  0.138031 -0.000158
			-0.944196  0.184040  0.273171
			-0.396195  0.186865  0.898950
			-0.033265 -0.299134  0.953631
			 0.191221  0.088253  0.977571
			-0.163996 -0.971277  0.172414
			-0.298725 -0.612167  0.732131
			 0.070088 -0.107402  0.991742
			 0.668328  0.030304 -0.743249
			 0.173250 -0.318544 -0.931941
			 0.191221  0.088253 -0.977571
			-0.033265 -0.299134 -0.953631
			-0.298725 -0.612167 -0.732131
			-0.163998 -0.971277 -0.172412
			 0.070088 -0.107402 -0.991742
			 0.668496  0.030359  0.743096
			 0.903267  0.429078  0.000182
			 0.776187  0.531196  0.339654
			 0.561591 -0.290714  0.774662
			 0.776229  0.531222 -0.339518
			 0.561593 -0.290709 -0.774662
			 0.132361 -0.352955  0.926231
			-0.440520  0.825150 -0.353652
			-0.299773  0.022783 -0.953739
			-0.653744  0.756716 -0.000234
			-0.440567  0.825185  0.353512
			-0.299761  0.022775  0.953742
			-0.158557 -0.129917  0.978765
			-0.228932  0.324688  0.917697
			 0.049887  0.750487  0.659000
			-0.219868 -0.543788  0.809909
			-0.351533 -0.542349  0.763074
			 0.493785 -0.255432  0.831222
			 0.790196 -0.414666  0.451269
			 0.145845  0.182953  0.972244
			-0.526839 -0.499247  0.687890
			 0.986767 -0.162142 -0.000208
			 0.663910 -0.585625 -0.465045
			-0.427380  0.904072  0.000060
			-0.383013  0.551896 -0.740751
			 0.132361 -0.352955 -0.926231
			-0.228932  0.324688 -0.917697
			-0.158557 -0.129917 -0.978765
			 0.049887  0.750487 -0.659000
			-0.351531 -0.542352 -0.763073
			-0.219870 -0.543790 -0.809907
			 0.493785 -0.255432 -0.831222
			 0.790196 -0.414658 -0.451275
			-0.526839 -0.499247 -0.687890
			 0.145845  0.182953 -0.972244
			 0.663906 -0.585670  0.464994
			-0.382925  0.551922  0.740777
			 0.089052  0.996027 -0.000005
			 0.533835  0.660659  0.527778
			 0.301889  0.470462  0.829173
			 0.533829  0.660672 -0.527768
			 0.301893  0.470471 -0.829167
			-0.189636  0.446476  0.874470
			-0.068723  0.992112 -0.104835
			 0.110316  0.968657  0.222562
			-0.558693  0.634736 -0.533828
			-0.085803  0.947696  0.307425
			 0.110315  0.968658 -0.222560
			-0.068725  0.992112  0.104838
			-0.558693  0.634732  0.533834
			-0.085789  0.947699 -0.307421
			 0.332329  0.584437  0.740264
			 0.369415  0.653058  0.661096
			 0.508546  0.276652  0.815380
			 0.199810 -0.697565  0.688098
			 0.545692  0.172787  0.819978
			-0.780712  0.359966  0.510796
			-0.099002  0.147243  0.984133
			-0.782772 -0.542342 -0.305177
			-0.878007  0.364015 -0.310800
			 0.878470 -0.361800  0.312077
			-0.063192  0.242711  0.968038
			 0.103044 -0.010700  0.994619
			-0.873455 -0.386616  0.295982
			-0.945836  0.252386 -0.204195
			-0.050932 -0.998702 -0.000120
			-0.140788 -0.909722 -0.390620
			-0.808659  0.588277  0.000208
			-0.469703  0.811794  0.346944
			-0.714635  0.679565  0.165797
			-0.867962  0.445324 -0.219839
			-0.469564  0.811952 -0.346763
			-0.714535  0.679712 -0.165626
			-0.867950  0.445314  0.219906
			-0.758635  0.379483 -0.529590
			-0.189635  0.446479 -0.874468
			 0.332329  0.584437 -0.740264
			 0.369413  0.653061 -0.661094
			-0.098999  0.147250 -0.984132
			-0.780711  0.359966 -0.510797
			 0.199810 -0.697567 -0.688096
			-0.782770 -0.542346  0.305176
			-0.878013  0.364010  0.310789
			 0.508545  0.276656 -0.815379
			 0.545692  0.172787 -0.819978
			 0.878470 -0.361800 -0.312077
			-0.063195  0.242716 -0.968037
			-0.873455 -0.386616 -0.295982
			 0.103044 -0.010700 -0.994619
			-0.945836  0.252386  0.204195
			-0.140671 -0.909754  0.390587
			-0.758636  0.379483  0.529588
			-0.807143  0.482787 -0.339759
			-0.636658  0.158187 -0.754747
			-0.807144  0.482786  0.339759
			-0.636659  0.158185  0.754747
			-0.546322 -0.271124  0.792480
			-0.651549 -0.707632  0.273390
			-0.854960  0.278892  0.437337
			-0.852873 -0.325455  0.408272
			-0.732001 -0.539864  0.415598
			-0.767528  0.586963  0.257635
			 0.325304 -0.911135  0.253001
			 0.142613 -0.934794 -0.325302
			 0.720725 -0.693221 -0.000082
			 0.142577 -0.934844  0.325176
			-0.700160  0.493030  0.516427
			-0.809727  0.586807 -0.000651
			-0.786721  0.217409  0.577757
			-0.700221  0.493028 -0.516347
			-0.786472  0.217738 -0.577972
			-0.767529  0.586961 -0.257636
			-0.852878 -0.325449 -0.408267
			-0.854962  0.278888 -0.437334
			-0.732003 -0.539862 -0.415596
			-0.546322 -0.271124 -0.792480
			-0.651549 -0.707632 -0.273390
			 0.325298 -0.911137 -0.253002
			-0.662794  0.163003  0.730845
			-0.120573 -0.947007  0.297725
			-0.129593 -0.956650 -0.260819
			 0.644094 -0.764946  0.000033
			-0.129595 -0.956650  0.260817
			-0.120604 -0.946990 -0.297768
			-0.655999  0.754761 -0.001255
			-0.661939  0.162647 -0.731699
			-0.078831  0.216801  0.973028
			-0.060424 -0.001943  0.998171
			 0.537529 -0.735719  0.412043
			 0.078609 -0.996905 -0.000440
			 0.537526 -0.735717 -0.412052
			-0.060451 -0.001924 -0.998169
			-0.821469  0.150193  0.550120
			-0.996679 -0.081428 -0.000952
			-0.078187  0.215966 -0.973265
			-0.820551  0.149694 -0.551622
			-0.352361 -0.637553  0.685104
			-0.674790 -0.738009 -0.000877
			-0.351615 -0.637373 -0.685655
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.040259  0.309814  0.000023
			-0.040959  0.310136  0.007643
			-0.053747  0.310696  0.000003
			-0.039511  0.313673  0.009168
			-0.039371  0.311158  0.000025
			-0.040959  0.310136 -0.007643
			-0.039511  0.313673 -0.009168
			-0.045003  0.310911  0.015572
			-0.051521  0.311553  0.019429
			-0.065841  0.310741  0.004596
			-0.065958  0.311007  0.000003
			-0.066189  0.310993  0.013528
			-0.061871  0.311929  0.019533
			-0.045003  0.310911 -0.015572
			-0.051521  0.311553 -0.019429
			-0.065841  0.310741 -0.004596
			-0.066189  0.310993 -0.013528
			-0.061871  0.311929 -0.019533
			-0.039826  0.315362  0.000025
			-0.041099  0.315885  0.009816
			-0.044566  0.314809  0.016543
			-0.041099  0.315885 -0.009816
			-0.044566  0.314809 -0.016543
			-0.052367  0.316075  0.020094
			-0.069445  0.314091  0.004730
			-0.069416  0.313973  0.000003
			-0.069445  0.314091 -0.004730
			-0.062891  0.316836  0.020237
			-0.069039  0.311753  0.015192
			-0.070307  0.315459  0.008989
			-0.052367  0.316075 -0.020094
			-0.069039  0.311753 -0.015192
			-0.062891  0.316836 -0.020237
			-0.070307  0.315459 -0.008989
			-0.039180  0.324045  0.000028
			-0.040600  0.323213  0.009473
			-0.046275  0.317535  0.016131
			-0.040600  0.323213 -0.009473
			-0.046275  0.317535 -0.016131
			-0.052292  0.319875  0.018280
			-0.068917  0.320568  0.006064
			-0.068431  0.320545  0.000005
			-0.068917  0.320568 -0.006064
			-0.062630  0.320142  0.019153
			-0.067833  0.319249  0.018110
			-0.070705  0.321257  0.009275
			-0.052292  0.319875 -0.018280
			-0.067833  0.319249 -0.018110
			-0.062630  0.320142 -0.019153
			-0.070705  0.321257 -0.009275
			-0.038478  0.327436  0.000029
			-0.039099  0.327151  0.010479
			-0.046288  0.324293  0.016751
			-0.039099  0.327151 -0.010479
			-0.046288  0.324293 -0.016751
			-0.051078  0.324927  0.018364
			-0.067025  0.327829  0.006267
			-0.068934  0.328419  0.009698
			-0.066310  0.327844  0.000007
			-0.067025  0.327829 -0.006267
			-0.068935  0.328419 -0.009698
			-0.060935  0.326340  0.019291
			-0.065536  0.327713  0.019666
			-0.073803  0.319520  0.016315
			-0.070906  0.321687  0.020003
			-0.069319  0.326487  0.023918
			-0.078782  0.323674  0.003560
			-0.051078  0.324927 -0.018364
			-0.065536  0.327713 -0.019666
			-0.060935  0.326340 -0.019291
			-0.070906  0.321687 -0.020003
			-0.073803  0.319520 -0.016315
			-0.069319  0.326487 -0.023918
			-0.078782  0.323674 -0.003560
			-0.038411  0.331305  0.000029
			-0.039238  0.331185  0.010672
			-0.044207  0.327365  0.017731
			-0.039238  0.331185 -0.010672
			-0.044207  0.327365 -0.017731
			-0.050224  0.327567  0.020305
			-0.067194  0.334180  0.007989
			-0.068016  0.332766  0.010413
			-0.065619  0.333202  0.000009
			-0.067194  0.334180 -0.007989
			-0.068016  0.332766 -0.010413
			-0.058575  0.329829  0.021284
			-0.063717  0.329843  0.019004
			-0.072393  0.333381  0.019049
			-0.081735  0.327146  0.022079
			-0.080163  0.323335  0.016409
			-0.077628  0.317783  0.023617
			-0.075863  0.317397  0.010056
			-0.076596  0.325705  0.022714
			-0.078554  0.319753  0.020649
			-0.079622  0.318977  0.000003
			-0.083405  0.308785  0.004883
			-0.087086  0.325464  0.000005
			-0.080701  0.329057  0.007369
			-0.050224  0.327567 -0.020305
			-0.063717  0.329843 -0.019004
			-0.058575  0.329829 -0.021284
			-0.072393  0.333381 -0.019049
			-0.080163  0.323335 -0.016409
			-0.081735  0.327146 -0.022079
			-0.077628  0.317783 -0.023617
			-0.075863  0.317397 -0.010056
			-0.078554  0.319753 -0.020649
			-0.076596  0.325705 -0.022714
			-0.083405  0.308785 -0.004883
			-0.080701  0.329057 -0.007369
			-0.053309  0.333142  0.000009
			-0.044149  0.331560  0.017976
			-0.050936  0.332489  0.021286
			-0.044149  0.331560 -0.017976
			-0.050936  0.332489 -0.021286
			-0.059748  0.333105  0.021416
			-0.079198  0.336864  0.015276
			-0.069433  0.333146  0.013466
			-0.079079  0.334520  0.009608
			-0.065367  0.333549  0.016344
			-0.069433  0.333146 -0.013466
			-0.079198  0.336864 -0.015276
			-0.079079  0.334520 -0.009608
			-0.065367  0.333549 -0.016344
			-0.078611  0.330611  0.020274
			-0.077532  0.333212  0.017562
			-0.091946  0.337253  0.033211
			-0.086795  0.328872  0.020137
			-0.081383  0.332195  0.020312
			-0.091594  0.315295  0.012070
			-0.087274  0.316287  0.014776
			-0.092949  0.328889  0.024182
			-0.085228  0.326446  0.012105
			-0.076461  0.315667  0.011456
			-0.091598  0.311818  0.016275
			-0.080323  0.326300  0.025123
			-0.081978  0.324546  0.022911
			-0.082703  0.330708  0.023234
			-0.085799  0.313393  0.000003
			-0.083179  0.307887  0.006399
			-0.097928  0.315496  0.000004
			-0.088267  0.322507  0.004733
			-0.084541  0.321286  0.007925
			-0.084334  0.324988  0.009933
			-0.088267  0.322507 -0.004733
			-0.084541  0.321286 -0.007925
			-0.084334  0.324988 -0.009933
			-0.081735  0.329590  0.010838
			-0.059748  0.333105 -0.021416
			-0.078611  0.330611 -0.020274
			-0.077532  0.333212 -0.017562
			-0.087274  0.316287 -0.014776
			-0.091594  0.315295 -0.012070
			-0.086795  0.328872 -0.020137
			-0.092949  0.328889 -0.024182
			-0.085228  0.326446 -0.012105
			-0.091946  0.337253 -0.033211
			-0.081383  0.332195 -0.020312
			-0.076461  0.315667 -0.011456
			-0.091598  0.311818 -0.016275
			-0.081978  0.324546 -0.022911
			-0.080323  0.326300 -0.025123
			-0.082703  0.330708 -0.023234
			-0.083179  0.307887 -0.006399
			-0.081735  0.329590 -0.010838
			-0.097493  0.335016  0.027837
			-0.085479  0.331460  0.013293
			-0.097493  0.335016 -0.027837
			-0.085479  0.331460 -0.013293
			-0.095786  0.333608  0.036798
			-0.097617  0.331800  0.028594
			-0.094047  0.313086  0.009798
			-0.094415  0.309260  0.009271
			-0.091814  0.306195  0.016043
			-0.091368  0.317049  0.008450
			-0.084407  0.306063  0.012717
			-0.085377  0.310749  0.005089
			-0.088548  0.309169  0.000001
			-0.085377  0.310749 -0.005089
			-0.096312  0.317496  0.003997
			-0.105178  0.305570 -0.000015
			-0.096193  0.313660  0.003889
			-0.096312  0.317496 -0.003997
			-0.096193  0.313660 -0.003889
			-0.091368  0.317049 -0.008450
			-0.094415  0.309260 -0.009271
			-0.094047  0.313086 -0.009798
			-0.091815  0.306195 -0.016043
			-0.095786  0.333608 -0.036798
			-0.097617  0.331800 -0.028594
			-0.084407  0.306063 -0.012717
			-0.096556  0.310027  0.004057
			-0.095709  0.308150  0.004756
			-0.090814  0.306609  0.007293
			-0.095765  0.303956 -0.000000
			-0.090814  0.306609 -0.007293
			-0.095709  0.308150 -0.004756
			-0.111991  0.299646 -0.000019
			-0.096556  0.310027 -0.004057
			-0.104439  0.298787  0.004413
			-0.098655  0.302059  0.004430
			-0.105431  0.291354  0.003440
			-0.107200  0.290962 -0.000003
			-0.105431  0.291354 -0.003440
			-0.098655  0.302059 -0.004430
			-0.113489  0.296803  0.004462
			-0.114388  0.295669 -0.000021
			-0.104439  0.298787 -0.004413
			-0.113489  0.296803 -0.004462
			-0.111924  0.293661  0.005196
			-0.111036  0.292007 -0.000020
			-0.111924  0.293661 -0.005196
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
