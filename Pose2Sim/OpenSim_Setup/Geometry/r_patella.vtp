<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="72" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="80">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.195918  0.980604  0.005724
			-0.010377  0.946435 -0.322728
			-0.203832  0.677010 -0.707184
			-0.232363  0.048436 -0.971422
			-0.065546 -0.466146 -0.882276
			 0.037547 -0.475147 -0.879105
			 0.044928 -0.563107 -0.825162
			 0.079727 -0.831177 -0.550263
			 0.096243 -0.992191 -0.079331
			 0.493988  0.859371 -0.132127
			 0.658798  0.634931 -0.403543
			 0.784488  0.109290 -0.610437
			 0.670676 -0.251401 -0.697847
			 0.622110 -0.414081 -0.664467
			 0.611688 -0.586582 -0.530809
			 0.465990 -0.795060 -0.388243
			 0.605698  0.795549  0.015230
			 0.795269  0.601184 -0.078257
			 0.977407  0.138796 -0.159411
			 0.960925 -0.146581 -0.234811
			 0.895912 -0.385014 -0.221600
			 0.796049 -0.586348 -0.150005
			 0.645786 -0.754308 -0.118236
			 0.611537  0.790927 -0.021379
			 0.798241  0.601859  0.024030
			 0.984971  0.126795  0.117281
			 0.955089 -0.226060  0.191580
			 0.910107 -0.374978  0.176341
			 0.867625 -0.490376  0.082210
			 0.699280 -0.714552  0.020601
			 0.532244  0.843953  0.066787
			 0.674742  0.693024  0.253852
			 0.829350  0.143958  0.539865
			 0.679878 -0.412745  0.606142
			 0.679560 -0.441512  0.585888
			 0.777279 -0.422496  0.466192
			 0.599004 -0.759902  0.252474
			 0.007726  0.971134  0.238408
			-0.200570  0.806443  0.556257
			-0.218650  0.183401  0.958413
			-0.220210 -0.311871  0.924253
			-0.103206 -0.308856  0.945493
			 0.047198 -0.501684  0.863763
			 0.070406 -0.866958  0.493383
			-0.612060  0.785956  0.087498
			-0.972404  0.232664  0.017260
			-0.999090  0.006526  0.042141
			-0.944783  0.138370  0.297051
			-0.826212  0.035253  0.562256
			-0.711445 -0.459911  0.531346
			-0.449506 -0.826595  0.338652
			-0.767334  0.638774 -0.056272
			-0.980285 -0.079971 -0.180682
			-0.971011 -0.097056 -0.218443
			-0.961576  0.262501 -0.080400
			-0.986320  0.128822  0.102844
			-0.898008 -0.418193  0.136734
			-0.610441 -0.786029  0.097577
			-0.746393  0.661341  0.074329
			-0.979380  0.003616  0.201996
			-0.973229 -0.061005  0.221594
			-0.989326  0.144539  0.018502
			-0.980294  0.033798 -0.194629
			-0.870128 -0.441364 -0.219261
			-0.558223 -0.807355 -0.191221
			-0.570435  0.815365 -0.098917
			-0.956218  0.290866 -0.032305
			-0.998714 -0.004973 -0.050462
			-0.895821 -0.264233 -0.357331
			-0.698052 -0.295002 -0.652455
			-0.590317 -0.495926 -0.636854
			-0.307997 -0.828035 -0.468503
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.000000  0.052920  0.000000
			 0.000000  0.050274 -0.012600
			 0.000000  0.044982 -0.021764
			 0.000000  0.035280 -0.024709
			 0.000000  0.023814 -0.021436
			 0.000000  0.018345 -0.015546
			 0.000000  0.008643 -0.013418
			 0.000000  0.004233 -0.008182
			 0.000000  0.000000  0.000000
			 0.005482  0.049127 -0.007546
			 0.010147  0.044365 -0.013966
			 0.012023  0.033957 -0.016548
			 0.011013  0.023814 -0.015158
			 0.008512  0.016493 -0.011716
			 0.006925  0.008732 -0.009531
			 0.003991  0.003969 -0.005495
			 0.005758  0.047980 -0.001871
			 0.012139  0.043747 -0.003944
			 0.015407  0.032634 -0.005006
			 0.015251  0.023814 -0.004955
			 0.012761  0.014641 -0.004146
			 0.009649  0.008820 -0.003135
			 0.005135  0.003705 -0.001669
			 0.005758  0.047980  0.001871
			 0.012139  0.043747  0.003944
			 0.015407  0.032634  0.005006
			 0.015251  0.023814  0.004955
			 0.012761  0.014641  0.004146
			 0.009649  0.008820  0.003135
			 0.005135  0.003705  0.001669
			 0.003895  0.049744  0.005362
			 0.008993  0.046569  0.012378
			 0.012600  0.034927  0.017343
			 0.011590  0.023460  0.015953
			 0.009089  0.016229  0.012511
			 0.007551  0.008556  0.010392
			 0.004617  0.003440  0.006355
			 0.000000  0.051509  0.007200
			 0.000000  0.049392  0.017836
			 0.000000  0.037220  0.026673
			 0.000000  0.023109  0.023400
			 0.000000  0.017816  0.017509
			 0.000000  0.008291  0.015546
			 0.000000  0.003174  0.010309
			-0.003367  0.051596  0.004633
			-0.006781  0.045599  0.009333
			-0.008752  0.035986  0.012047
			-0.007935  0.025225  0.010922
			-0.008464  0.015171  0.011650
			-0.007551  0.007761  0.010392
			-0.005049  0.003087  0.006950
			-0.004046  0.051685  0.001315
			-0.004981  0.041807  0.001618
			-0.002957  0.034751  0.000961
			-0.003424  0.027342  0.001112
			-0.010738  0.012524  0.003489
			-0.009649  0.007232  0.003135
			-0.006536  0.002999  0.002124
			-0.004046  0.051685 -0.001315
			-0.004981  0.041807 -0.001618
			-0.002957  0.034751 -0.000961
			-0.003424  0.027342 -0.001112
			-0.010738  0.012524 -0.003489
			-0.009649  0.007232 -0.003135
			-0.006536  0.002999 -0.002124
			-0.004954  0.050979 -0.006818
			-0.007935  0.043395 -0.010922
			-0.008176  0.035015 -0.011253
			-0.007358  0.025577 -0.010128
			-0.007887  0.015435 -0.010856
			-0.006925  0.007937 -0.009531
			-0.004424  0.003616 -0.006089
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					9 1 0 
					9 10 2 1 
					10 11 3 2 
					11 12 4 3 
					12 13 5 4 
					13 14 6 5 
					14 15 7 6 
					15 8 7 
					16 9 0 
					16 17 10 9 
					17 18 11 10 
					18 19 12 11 
					19 20 13 12 
					20 21 14 13 
					21 22 15 14 
					22 8 15 
					23 16 0 
					23 24 17 16 
					24 25 18 17 
					25 26 19 18 
					26 27 20 19 
					27 28 21 20 
					28 29 22 21 
					29 8 22 
					30 23 0 
					30 31 24 23 
					31 32 25 24 
					32 33 26 25 
					33 34 27 26 
					34 35 28 27 
					35 36 29 28 
					36 8 29 
					37 30 0 
					37 38 31 30 
					38 39 32 31 
					39 40 33 32 
					40 41 34 33 
					41 42 35 34 
					42 43 36 35 
					43 8 36 
					44 37 0 
					44 45 38 37 
					45 46 39 38 
					46 47 40 39 
					47 48 41 40 
					48 49 42 41 
					49 50 43 42 
					50 8 43 
					51 44 0 
					51 52 45 44 
					52 53 46 45 
					53 54 47 46 
					54 55 48 47 
					55 56 49 48 
					56 57 50 49 
					57 8 50 
					58 51 0 
					58 59 52 51 
					59 60 53 52 
					60 61 54 53 
					61 62 55 54 
					62 63 56 55 
					63 64 57 56 
					64 8 57 
					65 58 0 
					65 66 59 58 
					66 67 60 59 
					67 68 61 60 
					68 69 62 61 
					69 70 63 62 
					70 71 64 63 
					71 8 64 
					1 65 0 
					1 2 66 65 
					2 3 67 66 
					3 4 68 67 
					4 5 69 68 
					5 6 70 69 
					6 7 71 70 
					7 8 71 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 7 11 15 19 23 27 30 33 37 41 45 49 53 57 60 63 67 71 75 79 83 87 90 93 97 101 105 109 113 117 120 123 127 131 135 139 143 147 150 153 157 161 165 169 173 177 180 183 187 191 195 199 203 207 210 213 217 221 225 229 233 237 240 243 247 251 255 259 263 267 270 273 277 281 285 289 293 297 300 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
