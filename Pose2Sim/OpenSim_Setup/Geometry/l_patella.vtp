<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="72" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="80">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			0.195918 0.980604 -0.005724
			-0.015504 0.926118 0.376916
			-0.217930 0.572379 0.790499
			-0.235883 -0.033038 0.971220
			-0.237461 -0.321972 0.916486
			-0.107548 -0.303499 0.946743
			0.041875 -0.512405 0.857722
			0.071831 -0.817763 0.571056
			0.096243 -0.992191 0.079331
			0.540593 0.825996 0.159656
			0.706252 0.557145 0.436804
			0.787993 0.023821 0.615224
			0.613833 -0.435072 0.658728
			0.601760 -0.456294 0.655500
			0.658122 -0.498370 0.564360
			0.473826 -0.788574 0.391970
			0.634075 0.773228 -0.008181
			0.825262 0.557791 0.088389
			0.982593 0.090881 0.162025
			0.942307 -0.238222 0.235176
			0.891651 -0.389807 0.230237
			0.829943 -0.533788 0.162067
			0.651876 -0.748841 0.119558
			0.607906 0.793657 0.023642
			0.804289 0.593686 -0.025623
			0.983048 0.137438 -0.121356
			0.970496 -0.146519 -0.191494
			0.908613 -0.382491 -0.167703
			0.812652 -0.578743 -0.068216
			0.668850 -0.743231 -0.015734
			0.523839 0.850029 -0.055171
			0.679995 0.689041 -0.250659
			0.825072 0.162015 -0.541301
			0.727071 -0.252519 -0.638437
			0.688754 -0.420192 -0.590810
			0.697959 -0.580543 -0.419314
			0.548700 -0.804803 -0.226320
			0.029104 0.982689 -0.182961
			-0.167789 0.854903 -0.490906
			-0.211768 0.205262 -0.955522
			-0.058045 -0.470286 -0.880603
			0.033971 -0.491020 -0.870486
			0.021531 -0.554643 -0.831810
			0.053412 -0.878173 -0.475353
			-0.543771 0.838860 -0.025027
			-0.915652 0.398447 0.053118
			-0.997245 0.069299 -0.026460
			-0.917420 -0.249618 -0.309890
			-0.768429 -0.283512 -0.573706
			-0.724894 -0.428176 -0.539624
			-0.457826 -0.819318 -0.345128
			-0.733890 0.674979 0.076217
			-0.977309 0.031500 0.209461
			-0.971846 -0.048827 0.230502
			-0.988484 0.144192 0.045919
			-0.989564 0.037906 -0.139020
			-0.905354 -0.401684 -0.137783
			-0.615053 -0.782276 -0.098762
			-0.765214 0.639750 -0.071881
			-0.977561 -0.080637 -0.194607
			-0.971885 -0.096570 -0.214739
			-0.962529 0.264263 -0.060852
			-0.979805 0.131304 0.150803
			-0.879018 -0.421092 0.223626
			-0.580203 -0.789849 0.198755
			-0.619844 0.774225 0.127943
			-0.989656 0.128831 0.063121
			-0.995560 -0.071496 0.061225
			-0.932807 0.135496 0.333934
			-0.772873 0.034650 0.633614
			-0.607149 -0.459480 0.648265
			-0.339851 -0.798987 0.496105
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			0.000000 0.052920 0.000000
			0.000000 0.050274 0.012600
			0.000000 0.044982 0.021764
			0.000000 0.035280 0.024709
			0.000000 0.023814 0.021436
			0.000000 0.018345 0.015546
			0.000000 0.008643 0.013418
			0.000000 0.004233 0.008182
			0.000000 0.000000 0.000000
			0.005482 0.049127 0.007546
			0.010147 0.044365 0.013966
			0.012023 0.033957 0.016548
			0.011013 0.023814 0.015158
			0.008512 0.016493 0.011716
			0.006925 0.008732 0.009531
			0.003991 0.003969 0.005495
			0.005758 0.047980 0.001871
			0.012139 0.043747 0.003944
			0.015407 0.032634 0.005006
			0.015251 0.023814 0.004955
			0.012761 0.014641 0.004146
			0.009649 0.008820 0.003135
			0.005135 0.003705 0.001669
			0.005758 0.047980 -0.001871
			0.012139 0.043747 -0.003944
			0.015407 0.032634 -0.005006
			0.015251 0.023814 -0.004955
			0.012761 0.014641 -0.004146
			0.009649 0.008820 -0.003135
			0.005135 0.003705 -0.001669
			0.003895 0.049744 -0.005362
			0.008993 0.046569 -0.012378
			0.012600 0.034927 -0.017343
			0.011590 0.023460 -0.015953
			0.009089 0.016229 -0.012511
			0.007551 0.008556 -0.010392
			0.004617 0.003440 -0.006355
			0.000000 0.051509 -0.007200
			0.000000 0.049392 -0.017836
			0.000000 0.037220 -0.026673
			0.000000 0.023109 -0.023400
			0.000000 0.017816 -0.017509
			0.000000 0.008291 -0.015546
			0.000000 0.003174 -0.010309
			-0.003367 0.051596 -0.004633
			-0.006781 0.045599 -0.009333
			-0.008752 0.035986 -0.012047
			-0.007935 0.025225 -0.010922
			-0.008464 0.015171 -0.011650
			-0.007551 0.007761 -0.010392
			-0.005049 0.003087 -0.006950
			-0.004046 0.051685 -0.001315
			-0.004981 0.041807 -0.001618
			-0.002957 0.034751 -0.000961
			-0.003424 0.027342 -0.001112
			-0.010738 0.012524 -0.003489
			-0.009649 0.007232 -0.003135
			-0.006536 0.002999 -0.002124
			-0.004046 0.051685 0.001315
			-0.004981 0.041807 0.001618
			-0.002957 0.034751 0.000961
			-0.003424 0.027342 0.001112
			-0.010738 0.012524 0.003489
			-0.009649 0.007232 0.003135
			-0.006536 0.002999 0.002124
			-0.004954 0.050979 0.006818
			-0.007935 0.043395 0.010922
			-0.008176 0.035015 0.011253
			-0.007358 0.025577 0.010128
			-0.007887 0.015435 0.010856
			-0.006925 0.007937 0.009531
			-0.004424 0.003616 0.006089
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 9 
					1 2 10 9 
					2 3 11 10 
					3 4 12 11 
					4 5 13 12 
					5 6 14 13 
					6 7 15 14 
					7 8 15 
					0 9 16 
					9 10 17 16 
					10 11 18 17 
					11 12 19 18 
					12 13 20 19 
					13 14 21 20 
					14 15 22 21 
					15 8 22 
					0 16 23 
					16 17 24 23 
					17 18 25 24 
					18 19 26 25 
					19 20 27 26 
					20 21 28 27 
					21 22 29 28 
					22 8 29 
					0 23 30 
					23 24 31 30 
					24 25 32 31 
					25 26 33 32 
					26 27 34 33 
					27 28 35 34 
					28 29 36 35 
					29 8 36 
					0 30 37 
					30 31 38 37 
					31 32 39 38 
					32 33 40 39 
					33 34 41 40 
					34 35 42 41 
					35 36 43 42 
					36 8 43 
					0 37 44 
					37 38 45 44 
					38 39 46 45 
					39 40 47 46 
					40 41 48 47 
					41 42 49 48 
					42 43 50 49 
					43 8 50 
					0 44 51 
					44 45 52 51 
					45 46 53 52 
					46 47 54 53 
					47 48 55 54 
					48 49 56 55 
					49 50 57 56 
					50 8 57 
					0 51 58 
					51 52 59 58 
					52 53 60 59 
					53 54 61 60 
					54 55 62 61 
					55 56 63 62 
					56 57 64 63 
					57 8 64 
					0 58 65 
					58 59 66 65 
					59 60 67 66 
					60 61 68 67 
					61 62 69 68 
					62 63 70 69 
					63 64 71 70 
					64 8 71 
					0 65 1 
					65 66 2 1 
					66 67 3 2 
					67 68 4 3 
					68 69 5 4 
					69 70 6 5 
					70 71 7 6 
					71 8 7 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 7 11 15 19 23 27 30 33 37 41 45 49 53 57 60 63 67 71 75 79 83 87 90 93 97 101 105 109 113 117 120 123 127 131 135 139 143 147 150 153 157 161 165 169 173 177 180 183 187 191 195 199 203 207 210 213 217 221 225 229 233 237 240 243 247 251 255 259 263 267 270 273 277 281 285 289 293 297 300 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
