<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="142" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="216">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.593483  0.804847  0.000000
			 0.995701 -0.092627  0.000000
			-0.463933  0.708806  0.531375
			-0.423310  0.905140 -0.039112
			-0.479335  0.708644 -0.517747
			 0.823176 -0.565594  0.049838
			 0.922366 -0.006848  0.386255
			 0.728862 -0.684661  0.000000
			 0.922366 -0.006847 -0.386257
			 0.819148 -0.570620 -0.058222
			 0.248602  0.963049  0.103605
			-0.754487  0.516162  0.405372
			-0.915369  0.263243 -0.304637
			-0.567162  0.481531 -0.668173
			-0.165073  0.418527 -0.893077
			-0.114821  0.456524 -0.882271
			-0.243639  0.968947 -0.042220
			-0.089424  0.511443  0.854652
			-0.129793  0.478442  0.868474
			-0.425664  0.359238  0.830517
			-0.890205  0.159009  0.426908
			 0.172205  0.910879 -0.375028
			-0.802636  0.470660 -0.366409
			 0.812478 -0.408375  0.416064
			-0.244657 -0.807983 -0.536010
			-0.839820  0.024833 -0.542296
			 0.146743 -0.989175 -0.000004
			-0.094752 -0.832131  0.546425
			 0.604164 -0.501204 -0.619500
			-0.879924  0.001776  0.475112
			-0.082514  0.837356  0.540395
			 0.275065  0.912633  0.302390
			-0.543797  0.772559 -0.327777
			-0.232420 -0.664776 -0.709968
			 0.181682 -0.548792 -0.815977
			-0.282186 -0.319250 -0.904683
			-0.542389  0.320081 -0.776764
			-0.997981 -0.048643  0.040839
			-0.502227  0.276072  0.819483
			-0.282183 -0.319248  0.904684
			 0.245467 -0.557601  0.792986
			 0.032599 -0.712742  0.700668
			-0.660502  0.713472  0.233871
			 0.136958  0.819682 -0.556205
			-0.060218  0.445990 -0.893010
			-0.473755  0.415482  0.776486
			-0.540285 -0.701557 -0.464662
			 0.589649 -0.797084 -0.130277
			-0.625619 -0.151403 -0.765296
			 0.236505 -0.971630 -0.000006
			-0.671820  0.492236 -0.553499
			-0.884700  0.430054 -0.179886
			-0.304044  0.240538 -0.921791
			-0.540633 -0.701418  0.464466
			 0.664829 -0.729534 -0.160572
			-0.692333 -0.192660  0.695383
			-0.758679  0.317640  0.568781
			-0.884700  0.430054  0.179886
			-0.656352  0.656017  0.372616
			-0.523846  0.805229  0.277834
			-0.467405  0.877943  0.103673
			-0.074137  0.919208 -0.386731
			 0.261407  0.910765  0.319646
			-0.578192  0.766275 -0.280209
			 0.292150  0.929550  0.224913
			 0.433371 -0.901216  0.000006
			-0.355015 -0.934861  0.000011
			-0.928721 -0.366067  0.058932
			-0.928721 -0.366067 -0.058932
			-0.519755  0.553460  0.650798
			 0.414100  0.880953  0.229004
			 0.143378  0.954770 -0.260495
			-0.538895  0.840124  0.061513
			-0.195266  0.885211  0.422225
			-0.747448  0.628993 -0.213747
			-0.738070  0.552520 -0.387264
			-0.803905 -0.574029  0.155654
			 0.590415 -0.704797 -0.393283
			 0.366008 -0.632728 -0.682417
			 0.168625 -0.406793 -0.897822
			 0.242888 -0.429739 -0.869672
			 0.075570  0.776354 -0.625751
			-0.233543  0.512135 -0.826544
			 0.718254  0.460122 -0.521919
			 0.595380  0.569075 -0.567164
			-0.280540 -0.462880 -0.840856
			-0.188438 -0.958308 -0.214796
			 0.195108  0.720901 -0.665007
			 0.048990  0.416698 -0.907724
			-0.360394 -0.924526 -0.123966
			 0.234626 -0.776753 -0.584470
			-0.493870  0.418619 -0.762135
			-0.800535 -0.593236 -0.084934
			 0.350924 -0.652288  0.671843
			 0.584563 -0.721535  0.371044
			 0.170520 -0.404070  0.898694
			 0.242566 -0.427882  0.870677
			 0.038531  0.760703  0.647956
			 0.159890  0.471712  0.867135
			 0.624484  0.604711  0.494312
			 0.771979  0.619863  0.140777
			-0.280786 -0.462077  0.841215
			-0.063429 -0.981418  0.181097
			 0.104975  0.528466  0.842439
			 0.054606  0.872427  0.485684
			-0.068927 -0.849373  0.523273
			-0.429563 -0.900865 -0.062589
			-0.359516  0.219556  0.906942
			 0.244470  0.851046  0.464709
			 0.155700  0.947447  0.279468
			 0.245891  0.968612  0.036435
			-0.377192  0.803969  0.459739
			-0.949359 -0.309455  0.054368
			-0.301454  0.595936 -0.744302
			-0.290877  0.522614  0.801415
			-0.073378  0.980179  0.184024
			 0.009547  0.830609 -0.556775
			 0.161548  0.944529 -0.285951
			-0.410707  0.818727 -0.401256
			 0.874941 -0.458164 -0.156727
			 0.688916 -0.721365 -0.070903
			 0.821372  0.542656 -0.175708
			 0.614006  0.599793 -0.513075
			 0.664115  0.588335 -0.461316
			 0.590707  0.413467 -0.692900
			 0.288747  0.519328 -0.804315
			 0.502611  0.649435 -0.570627
			 0.430468  0.192053 -0.881937
			 0.211699  0.335721 -0.917864
			 0.889100 -0.452907  0.066160
			 0.825947  0.525042  0.205289
			 0.620044  0.583011  0.525018
			 0.700901  0.578481  0.417249
			 0.509660  0.432684  0.743660
			 0.125111  0.417309  0.900111
			 0.740083  0.356528  0.570233
			 0.490120  0.239680  0.838055
			 0.331179  0.290594  0.897706
			 0.558858  0.740667 -0.372948
			-0.079492  0.995529  0.051027
			 0.759010  0.650487  0.027758
			 0.590079  0.740336  0.322038
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.008257  0.082988  0.000132
			-0.001465  0.090917  0.000132
			-0.000844  0.091108 -0.003659
			-0.009209  0.082845  0.000132
			-0.000844  0.091108  0.003922
			-0.004559  0.075287 -0.001858
			-0.001619  0.090236 -0.001300
			-0.004560  0.075285  0.000132
			-0.001619  0.090236  0.001564
			-0.004559  0.075287  0.002121
			 0.004154  0.094426 -0.010672
			-0.001802  0.091013 -0.004342
			-0.003730  0.085314 -0.010342
			-0.010671  0.078273 -0.004614
			-0.016349  0.077171 -0.003285
			-0.021970  0.077213 -0.003088
			-0.022050  0.079256  0.000132
			-0.021970  0.077213  0.003351
			-0.016349  0.077171  0.003548
			-0.010671  0.078273  0.004877
			-0.003730  0.085314  0.010605
			 0.004154  0.094426  0.010936
			-0.001802  0.091013  0.004605
			 0.001003  0.080574 -0.008613
			-0.010632  0.073687 -0.005847
			-0.005296  0.077762 -0.010739
			-0.010636  0.073681  0.000132
			-0.010632  0.073687  0.006110
			 0.001003  0.080574  0.008877
			-0.005296  0.077762  0.011002
			 0.008762  0.089156 -0.010742
			 0.007459  0.090621 -0.012022
			 0.002454  0.090147 -0.020035
			-0.013215  0.073592 -0.005035
			-0.019454  0.070647 -0.004896
			-0.023291  0.068767 -0.004768
			-0.027810  0.073922 -0.003779
			-0.027178  0.078226  0.000132
			-0.027810  0.073922  0.004042
			-0.023291  0.068767  0.005031
			-0.019454  0.070647  0.005160
			-0.013215  0.073592  0.005298
			 0.002454  0.090147  0.020298
			 0.007459  0.090621  0.012285
			 0.008762  0.089156  0.011005
			 0.012562  0.083481 -0.009569
			 0.009407  0.078043 -0.008602
			 0.010982  0.089269 -0.018755
			-0.002897  0.078767 -0.020208
			-0.013218  0.073587  0.000132
			-0.000302  0.084821 -0.021535
			-0.003970  0.078480 -0.015233
			 0.012562  0.083481  0.009833
			 0.009407  0.078043  0.008865
			 0.010982  0.089269  0.019019
			-0.002897  0.078767  0.020471
			-0.000302  0.084821  0.021798
			-0.003970  0.078480  0.015496
			 0.013865  0.093599 -0.008660
			 0.013850  0.095015 -0.009619
			 0.011849  0.090184 -0.011509
			 0.013230  0.090595 -0.014574
			 0.009315  0.093333 -0.016521
			 0.005220  0.086116 -0.020203
			 0.011412  0.095558 -0.021231
			-0.019457  0.070642  0.000132
			-0.023294  0.068763  0.000132
			-0.027808  0.070721 -0.003686
			-0.027808  0.070721  0.003949
			 0.004969  0.091251  0.022614
			 0.011412  0.095558  0.021469
			 0.009315  0.093333  0.016784
			 0.011849  0.090184  0.011772
			 0.013230  0.090595  0.014837
			 0.013850  0.095015  0.009882
			 0.013865  0.093599  0.008923
			 0.010734  0.076922 -0.003590
			 0.029571  0.075442 -0.007480
			 0.026506  0.076856 -0.013135
			 0.022832  0.077266 -0.014671
			 0.019439  0.082595 -0.016108
			 0.013501  0.087789 -0.016000
			 0.009546  0.093474 -0.023071
			 0.011345  0.083859 -0.019853
			 0.011366  0.091295 -0.025096
			 0.009826  0.082953 -0.026960
			 0.013321  0.081237 -0.024095
			 0.016619  0.087654 -0.017115
			 0.017061  0.086663 -0.020569
			 0.016415  0.080462 -0.021946
			 0.018641  0.084924 -0.019867
			 0.007056  0.086444 -0.023477
			 0.010734  0.076922  0.003853
			 0.026506  0.076856  0.013398
			 0.029571  0.075442  0.007743
			 0.022832  0.077266  0.014934
			 0.019439  0.082595  0.016371
			 0.013501  0.087789  0.016263
			 0.009546  0.093474  0.023309
			 0.011366  0.091295  0.025333
			 0.011095  0.088952  0.022227
			 0.009826  0.082953  0.027198
			 0.013321  0.081237  0.024333
			 0.017061  0.086663  0.020833
			 0.016619  0.087654  0.017378
			 0.018641  0.084924  0.020130
			 0.016415  0.080462  0.022183
			 0.007056  0.086444  0.023714
			 0.016113  0.097634 -0.010213
			 0.016363  0.092674 -0.004517
			 0.017518  0.096664 -0.012545
			 0.015375  0.092399 -0.002093
			 0.010710  0.076224  0.000132
			 0.016582  0.095843 -0.013756
			 0.016582  0.095843  0.014019
			 0.017518  0.096664  0.012808
			 0.016113  0.097634  0.010476
			 0.016363  0.092674  0.004780
			 0.015375  0.092399  0.002356
			 0.030866  0.075457 -0.001894
			 0.030848  0.075605  0.000132
			 0.029815  0.089461 -0.006591
			 0.026534  0.090693 -0.010265
			 0.024199  0.092136 -0.011679
			 0.020428  0.089416 -0.015118
			 0.018623  0.086259 -0.020387
			 0.014331  0.083199 -0.020451
			 0.012126  0.084106 -0.026428
			 0.016179  0.081723 -0.021215
			 0.030866  0.075457  0.002157
			 0.029815  0.089461  0.006854
			 0.026534  0.090693  0.010528
			 0.024199  0.092136  0.011943
			 0.020428  0.089416  0.015381
			 0.018623  0.086259  0.020650
			 0.014331  0.083199  0.020689
			 0.012126  0.084106  0.026666
			 0.016179  0.081723  0.021453
			 0.019669  0.095545 -0.011763
			 0.018087  0.091327  0.000132
			 0.030717  0.089337  0.000132
			 0.019669  0.095545  0.012026
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					4 1 0 
					0 3 4 
					5 6 1 
					7 5 1 
					1 6 2 
					1 8 9 
					1 9 7 
					4 8 1 
					10 2 6 
					10 11 2 
					2 12 13 3 
					12 2 11 
					3 14 15 16 
					13 14 3 
					16 17 18 3 
					3 18 19 
					3 19 20 4 
					8 4 21 
					4 22 21 
					22 4 20 
					23 6 5 
					23 5 24 25 
					24 5 7 26 
					10 6 23 
					26 7 9 27 
					28 8 21 
					9 8 28 
					29 27 9 28 
					30 31 10 23 
					10 32 12 11 
					10 31 32 
					13 12 24 33 
					12 25 24 
					12 32 25 
					14 13 33 34 
					14 34 35 
					15 14 35 
					16 15 36 37 
					35 36 15 
					37 38 17 16 
					39 18 17 
					17 38 39 
					39 40 18 
					40 41 19 18 
					41 27 20 19 
					22 20 42 21 
					27 29 20 
					29 42 20 
					28 21 43 44 
					42 43 21 
					30 23 45 
					45 23 46 
					47 46 23 
					47 23 25 48 
					49 33 24 26 
					25 32 50 
					25 50 51 
					25 51 48 
					26 27 41 49 
					52 28 44 
					53 28 52 
					28 53 54 
					55 29 28 54 
					56 42 29 
					57 56 29 
					55 57 29 
					58 59 30 45 
					59 60 30 
					60 31 30 
					61 62 31 60 
					62 32 31 
					32 63 50 
					32 62 64 63 
					65 34 33 49 
					66 35 34 65 
					66 37 35 
					67 35 37 
					67 36 35 
					36 67 37 
					39 37 66 
					37 68 38 
					37 39 68 
					39 38 68 
					65 40 39 66 
					49 41 40 65 
					56 69 42 
					69 70 71 42 
					43 42 71 
					72 43 71 73 
					44 43 72 
					52 44 74 75 
					44 72 74 
					58 45 46 
					58 46 76 
					77 76 46 78 
					78 46 79 
					79 46 80 
					46 47 80 
					81 47 62 61 
					47 64 62 
					47 82 64 
					47 83 84 82 
					47 48 85 
					47 85 86 
					83 47 87 88 
					47 81 87 
					89 90 47 
					90 80 47 
					47 86 89 
					51 50 48 
					50 91 48 
					91 85 48 
					63 91 50 
					53 52 75 
					92 53 75 
					93 53 92 94 
					95 53 93 
					96 53 95 
					96 54 53 
					73 71 54 97 
					71 70 54 
					70 98 54 
					98 99 100 54 
					101 55 54 
					102 101 54 
					103 104 54 100 
					104 97 54 
					54 105 106 
					54 96 105 
					106 102 54 
					55 56 57 
					55 107 56 
					55 101 107 
					56 107 69 
					108 58 109 
					110 59 58 108 
					109 58 111 
					111 58 76 112 
					110 113 60 59 
					113 61 60 
					81 61 113 
					82 63 64 
					91 63 82 
					70 69 98 
					98 69 107 
					74 72 114 115 
					72 73 114 
					114 73 97 
					116 75 74 115 
					117 75 116 
					118 75 117 
					112 92 75 118 
					76 77 119 120 112 
					77 121 119 
					78 122 121 77 
					79 122 78 
					80 123 122 79 
					80 124 123 
					80 90 125 124 
					87 81 113 
					82 84 91 
					126 84 83 
					126 83 88 
					84 127 91 
					126 127 84 
					91 127 85 
					86 85 127 
					89 86 127 128 
					125 88 87 124 
					124 87 113 
					128 126 88 125 
					90 89 128 125 
					112 120 129 94 92 
					94 130 131 93 
					93 131 95 
					129 130 94 
					95 131 132 96 
					132 133 96 
					133 134 105 96 
					114 97 104 
					107 99 98 
					100 99 135 
					107 136 99 
					99 136 135 
					103 100 135 
					101 136 107 
					136 101 102 
					137 136 102 106 
					133 104 103 134 
					134 103 135 137 
					114 104 133 
					134 137 106 105 
					123 138 108 109 
					138 110 108 
					123 109 122 
					122 109 121 
					109 139 140 121 
					109 111 139 
					124 113 110 138 
					139 111 112 
					112 118 139 
					141 115 114 133 
					116 115 141 
					117 116 141 132 
					131 117 132 
					130 117 131 
					130 140 139 117 
					139 118 117 
					121 140 120 119 
					129 120 140 130 
					123 124 138 
					127 126 128 
					141 133 132 
					137 135 136 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 40 43 47 50 54 57 61 64 67 70 73 77 81 84 88 91 94 98 102 106 109 113 116 119 123 126 129 133 136 140 143 146 149 153 157 161 164 167 171 174 177 180 183 187 191 194 197 200 204 207 210 213 217 220 223 226 230 233 236 240 243 246 250 254 258 261 264 267 270 273 276 279 282 286 290 293 297 300 304 307 311 314 317 320 324 327 330 333 337 340 343 347 350 353 357 360 363 366 369 372 375 378 381 384 387 391 394 397 400 404 407 410 414 417 420 424 427 430 433 436 439 442 445 448 451 455 458 462 466 469 472 475 478 481 484 488 491 494 498 501 504 508 513 516 520 523 527 530 534 537 540 543 546 549 552 555 558 562 566 569 573 577 582 586 589 592 596 599 603 606 609 612 615 618 621 624 627 631 635 639 642 646 650 653 656 659 663 666 670 673 676 680 683 687 690 693 697 700 704 708 711 714 717 720 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
