<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="133" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="262">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.704437 -0.709555  0.017340
			-0.838636 -0.544563  0.011882
			-0.861270 -0.467667 -0.198751
			-0.888284 -0.431250 -0.158038
			-0.557894 -0.560453  0.612084
			-0.257344 -0.965491 -0.040024
			-0.216501 -0.896702  0.386073
			-0.707996 -0.680641 -0.188333
			-0.715433 -0.379127  0.586872
			-0.878280 -0.478127  0.004286
			-0.851826 -0.306441 -0.424836
			-0.795180 -0.294779 -0.529900
			-0.833920 -0.545697  0.082416
			-0.855452 -0.504393 -0.117430
			-0.735166 -0.389983 -0.554477
			-0.061404 -0.435079  0.898296
			 0.518887 -0.853766  0.042882
			-0.054230 -0.995117 -0.082467
			-0.071447 -0.997041  0.028380
			-0.679770 -0.528770 -0.508247
			-0.299964 -0.832423 -0.465933
			-0.437074 -0.160675  0.884958
			-0.827809 -0.331754  0.452406
			-0.504698  0.098098 -0.857704
			-0.768238 -0.141092 -0.624422
			-0.392457  0.036881 -0.919031
			-0.319948 -0.342348 -0.883420
			-0.761834 -0.410296  0.501264
			-0.852615 -0.495931  0.164623
			-0.861291 -0.476857 -0.175455
			-0.262123 -0.205654 -0.942867
			-0.742904 -0.350158 -0.570510
			 0.503558 -0.452169  0.736188
			 0.193764 -0.079913  0.977788
			-0.094842  0.187347  0.977704
			 0.911031 -0.378768 -0.162963
			 0.876911 -0.392898  0.276872
			 0.834822 -0.548689 -0.044858
			 0.502666 -0.863139  0.048152
			 0.242319 -0.941840 -0.232852
			 0.547436 -0.836748 -0.012858
			 0.156276 -0.718205 -0.678056
			-0.434477  0.069292  0.898013
			 0.192862  0.692221 -0.695438
			-0.332160  0.639095 -0.693706
			-0.372982  0.403999 -0.835266
			 0.247226 -0.314326 -0.916558
			 0.282671  0.208361 -0.936314
			-0.079315  0.381317 -0.921036
			-0.295548  0.578883 -0.759964
			-0.217808 -0.271461  0.937480
			-0.544730 -0.547194  0.635491
			-0.801066 -0.529884  0.278415
			-0.806926 -0.560618  0.185953
			-0.929605 -0.350643 -0.113508
			 0.057098 -0.167536 -0.984211
			 0.374898  0.101489 -0.921494
			-0.190164 -0.215874 -0.957724
			-0.767774 -0.116403 -0.630059
			-0.970944  0.181056 -0.156483
			 0.867537  0.054711  0.494355
			 0.587628  0.521002  0.619072
			-0.198054  0.713421  0.672165
			 0.301018  0.908001  0.291415
			 0.990916  0.120390 -0.059923
			 0.999321 -0.031667  0.018827
			 0.875374 -0.466465  0.127013
			 0.462297 -0.735243 -0.495682
			 0.837089 -0.424032 -0.345657
			 0.158422  0.653381  0.740267
			 0.625755  0.775679 -0.082174
			 0.439125  0.896614  0.057023
			 0.928158  0.320551 -0.189128
			 0.283007  0.804235 -0.522602
			 0.835111  0.076579 -0.544725
			 0.703435  0.549518 -0.450787
			-0.001482 -0.316624  0.948550
			 0.664269  0.142464  0.733792
			 0.295428 -0.127959  0.946757
			-0.304851 -0.517174  0.799748
			-0.686493 -0.486136  0.540739
			-0.863015 -0.412281  0.291942
			-0.968032  0.217849  0.124320
			 0.697914  0.065230 -0.713205
			 0.338277  0.016577 -0.940901
			-0.096968  0.298435 -0.949491
			-0.513490  0.153124 -0.844323
			-0.913887  0.397341 -0.083252
			-0.808135  0.501625 -0.308692
			-0.538056  0.838124  0.089689
			 0.791990  0.610358 -0.014662
			 0.748965  0.660344  0.054752
			 0.663950  0.708804  0.238259
			 0.976716  0.214129  0.013230
			 0.703589  0.710211 -0.023732
			 0.982110  0.140799 -0.125042
			 0.876923  0.021674  0.480143
			 0.388413 -0.259098  0.884310
			-0.636197 -0.275439  0.720685
			-0.041581 -0.361536  0.931430
			-0.610020 -0.476817  0.632867
			-0.769576 -0.382682  0.511182
			-0.794222  0.214750  0.568414
			-0.701886  0.663523  0.259023
			 0.869652  0.197261 -0.452542
			 0.315762  0.573226 -0.756113
			 0.658875  0.647294 -0.383267
			-0.470190  0.833505 -0.290156
			-0.089683  0.857511 -0.506588
			-0.288245  0.955255 -0.066359
			-0.524407  0.831977  0.181140
			-0.316535  0.944427 -0.088677
			 0.095467  0.995310  0.015641
			-0.096392  0.992249  0.078421
			-0.349739  0.924374  0.152364
			 0.831591  0.529032  0.169065
			 0.764087  0.061258  0.642199
			 0.471318 -0.059148  0.879978
			-0.340091  0.109781  0.933963
			 0.188175 -0.034863  0.981517
			-0.503479  0.499701  0.704846
			 0.171933  0.926919 -0.333558
			 0.369289  0.910601 -0.185555
			 0.431710  0.900507 -0.052098
			-0.061517  0.997680 -0.029155
			-0.252248  0.854494  0.454105
			 0.136868  0.984654  0.108276
			 0.068602  0.960657  0.269133
			 0.565145  0.468107  0.679329
			 0.414738  0.822944  0.388272
			 0.299243  0.403088  0.864855
			-0.023265  0.388974  0.920955
			 0.157672  0.768935  0.619579
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.017779 -0.145721 -0.000389
			 0.017121 -0.144953 -0.000453
			 0.015985 -0.141798 -0.003754
			 0.016845 -0.144163 -0.003943
			 0.017952 -0.145679  0.000405
			 0.019483 -0.146880 -0.001213
			 0.019801 -0.146668  0.000574
			 0.017818 -0.145253 -0.005041
			 0.016430 -0.143360  0.000574
			 0.015066 -0.140363 -0.001725
			 0.016305 -0.141223 -0.006030
			 0.017397 -0.143157 -0.007393
			 0.013588 -0.138396 -0.001516
			 0.007036 -0.128181 -0.003058
			 0.008507 -0.128673 -0.005097
			 0.022023 -0.146271  0.001032
			 0.022612 -0.146487 -0.000156
			 0.019665 -0.146304 -0.004054
			 0.020116 -0.146729 -0.006646
			 0.018669 -0.145332 -0.007707
			 0.020199 -0.146407 -0.008056
			 0.016887 -0.140507  0.001714
			 0.015812 -0.141665  0.000440
			 0.016468 -0.137826 -0.007459
			 0.016825 -0.140499 -0.007457
			 0.018297 -0.139077 -0.008741
			 0.020041 -0.145167 -0.008810
			 0.012632 -0.136178 -0.000492
			 0.005501 -0.125594 -0.002385
			 0.002736 -0.119519 -0.002547
			 0.003030 -0.113122 -0.006877
			-0.001185 -0.110648 -0.005109
			 0.023021 -0.144813  0.001692
			 0.022777 -0.141392  0.002730
			 0.019482 -0.138684  0.002508
			 0.024266 -0.144008 -0.001537
			 0.024145 -0.144254  0.000873
			 0.023058 -0.144786 -0.003760
			 0.022233 -0.145661 -0.003825
			 0.021314 -0.146539 -0.007380
			 0.022516 -0.146157 -0.007047
			 0.021263 -0.146267 -0.008138
			 0.016543 -0.138781  0.001519
			 0.018235 -0.136921 -0.007155
			 0.017792 -0.137527 -0.007635
			 0.017860 -0.138542 -0.008407
			 0.023295 -0.143758 -0.009506
			 0.023448 -0.141802 -0.009646
			 0.020065 -0.138540 -0.009158
			 0.018585 -0.137556 -0.008170
			 0.010180 -0.124153  0.003109
			 0.004254 -0.118890  0.002662
			 0.002816 -0.119339  0.000100
			-0.000168 -0.114811  0.001487
			-0.002363 -0.111065 -0.002191
			 0.004524 -0.111220 -0.007529
			 0.007966 -0.114833 -0.007327
			 0.002352 -0.107814 -0.008618
			-0.001491 -0.106380 -0.007048
			-0.002758 -0.105662 -0.004103
			 0.024388 -0.141929  0.001531
			 0.023290 -0.139503  0.002022
			 0.018386 -0.138184  0.002090
			 0.020722 -0.137778  0.001521
			 0.024696 -0.142017 -0.000940
			 0.024078 -0.141931 -0.003614
			 0.024397 -0.144107 -0.006185
			 0.023193 -0.145162 -0.008501
			 0.023924 -0.144252 -0.008764
			 0.018564 -0.137650  0.000946
			 0.019345 -0.136877 -0.005588
			 0.020975 -0.137641 -0.005969
			 0.009497 -0.115209 -0.004434
			 0.020105 -0.137231 -0.008235
			 0.024376 -0.141826 -0.008887
			 0.023717 -0.140529 -0.008807
			 0.007572 -0.117586  0.004487
			 0.011379 -0.123796  0.003041
			 0.008549 -0.117179  0.004410
			 0.004552 -0.113036  0.006609
			 0.000265 -0.114713  0.002979
			-0.001015 -0.113155  0.001906
			-0.003141 -0.106331  0.001052
			 0.006757 -0.110634 -0.007280
			 0.005816 -0.106220 -0.008185
			 0.001474 -0.101935 -0.008671
			-0.000151 -0.103393 -0.008390
			-0.002150 -0.104198 -0.005810
			-0.000828 -0.101933 -0.007135
			 0.003199 -0.102253  0.000879
			 0.023683 -0.139703 -0.000751
			 0.023211 -0.139812 -0.003473
			 0.019345 -0.137149 -0.000941
			 0.024749 -0.141630 -0.006145
			 0.023129 -0.139051 -0.006229
			 0.008568 -0.106940 -0.000044
			 0.008948 -0.111220  0.004396
			 0.007926 -0.115355  0.005006
			-0.000020 -0.107567  0.008051
			 0.002937 -0.108787  0.009369
			 0.000129 -0.112720  0.003852
			-0.001288 -0.110263  0.005076
			-0.000822 -0.104739  0.006790
			-0.000554 -0.103263  0.005844
			 0.007313 -0.104176 -0.006887
			 0.003944 -0.101579 -0.008375
			 0.006040 -0.101879 -0.006855
			 0.000789 -0.100738 -0.007549
			 0.001903 -0.100435 -0.007488
			 0.003908 -0.100962  0.006541
			 0.001236 -0.102209  0.006900
			 0.004527 -0.101148  0.004219
			 0.005749 -0.100690  0.003734
			 0.003606 -0.100265 -0.004773
			 0.001615 -0.100461 -0.006454
			 0.007806 -0.102128  0.005023
			 0.006972 -0.105449  0.007903
			 0.005353 -0.106912  0.009019
			 0.002129 -0.106406  0.009569
			 0.003918 -0.107279  0.009424
			 0.002391 -0.102215  0.008006
			 0.002916 -0.100299 -0.007228
			 0.004454 -0.100553 -0.005892
			 0.004835 -0.100487 -0.002770
			 0.002701 -0.100260 -0.006688
			 0.003406 -0.101164  0.007454
			 0.005746 -0.100806  0.004993
			 0.004284 -0.100921  0.006927
			 0.006000 -0.102272  0.008040
			 0.005482 -0.101428  0.007212
			 0.004974 -0.102457  0.008734
			 0.003948 -0.102803  0.009025
			 0.004618 -0.101520  0.007947
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 2 3 
					0 4 1 
					5 6 0 
					6 4 0 
					3 7 0 
					5 0 7 
					1 4 8 
					8 9 1 
					9 2 1 
					2 10 11 
					3 2 7 
					2 9 12 
					2 12 13 
					2 13 14 
					14 10 2 
					7 2 11 
					8 4 15 
					4 6 15 
					16 6 5 
					7 17 5 
					5 17 16 
					16 15 6 
					7 18 17 
					11 19 7 
					20 18 7 
					7 19 20 
					21 8 15 
					22 8 21 
					8 22 9 
					12 9 22 
					23 10 14 
					23 24 10 
					10 24 11 
					11 24 25 
					25 26 11 
					26 19 11 
					13 12 27 
					22 27 12 
					13 27 28 
					13 28 14 
					28 29 14 
					30 23 14 
					31 14 29 
					30 14 31 
					32 33 15 
					34 15 33 
					34 21 15 
					32 15 16 
					16 35 36 
					35 16 37 
					16 38 37 
					38 16 17 
					36 32 16 
					38 17 18 
					20 39 18 
					38 18 40 
					18 39 40 
					26 20 19 
					41 39 20 
					26 41 20 
					22 21 42 
					21 34 42 
					27 22 42 
					23 25 24 
					30 43 23 
					43 44 23 
					44 45 23 
					23 45 25 
					46 26 25 
					47 25 48 
					47 46 25 
					25 49 48 
					49 25 45 
					26 46 41 
					42 50 27 
					28 27 50 
					51 28 50 
					52 28 51 
					29 28 52 
					29 52 53 
					53 54 29 
					29 54 31 
					30 55 56 
					30 31 57 
					30 57 55 
					56 43 30 
					31 58 57 
					59 31 54 
					58 31 59 
					60 33 32 
					36 60 32 
					34 33 61 
					33 60 61 
					34 62 42 
					63 62 34 
					61 63 34 
					64 35 65 
					37 65 35 
					35 64 36 
					60 36 64 
					65 37 66 
					38 66 37 
					66 38 40 
					40 39 67 
					41 67 39 
					68 66 40 
					68 40 67 
					67 41 46 
					50 42 69 
					62 69 42 
					70 71 43 
					72 70 43 
					56 72 43 
					43 71 73 
					43 73 49 
					43 49 44 
					44 49 45 
					47 74 46 
					68 46 74 
					68 67 46 
					47 75 74 
					47 73 75 
					48 73 47 
					49 73 48 
					76 51 50 
					69 77 50 
					77 78 50 
					78 76 50 
					76 79 51 
					79 80 51 
					53 52 51 
					53 51 80 
					81 53 80 
					54 53 81 
					82 54 81 
					54 82 59 
					83 56 55 
					84 83 55 
					84 55 57 
					83 72 56 
					85 84 57 
					57 58 86 
					85 57 86 
					59 87 58 
					86 58 88 
					58 87 88 
					59 82 89 
					87 59 89 
					64 90 60 
					61 60 90 
					63 61 90 
					69 62 63 
					90 91 63 
					70 63 91 
					92 63 70 
					69 63 92 
					91 64 65 
					91 90 64 
					65 93 91 
					65 66 93 
					66 68 93 
					93 68 74 
					69 92 77 
					72 92 70 
					71 70 91 
					71 94 73 
					71 91 94 
					72 83 95 
					72 95 96 
					72 96 77 
					77 92 72 
					75 73 94 
					74 75 93 
					75 94 93 
					97 79 76 
					78 97 76 
					78 77 96 
					96 97 78 
					98 79 99 
					79 100 80 
					79 101 100 
					79 97 99 
					79 98 101 
					80 100 81 
					100 101 81 
					101 82 81 
					82 101 102 
					82 103 89 
					103 82 102 
					83 84 104 
					95 83 104 
					105 106 84 
					104 84 106 
					105 84 85 
					107 85 86 
					85 107 108 
					85 108 105 
					86 88 107 
					88 87 89 
					107 88 89 
					109 89 110 
					111 112 89 
					103 110 89 
					113 114 89 
					114 107 89 
					89 109 111 
					89 112 113 
					94 91 93 
					104 115 95 
					95 115 96 
					96 115 116 
					96 116 117 
					117 97 96 
					97 117 99 
					102 101 98 
					102 98 118 
					98 99 118 
					119 99 117 
					118 99 119 
					103 102 120 
					102 118 120 
					120 110 103 
					104 106 115 
					121 105 108 
					121 122 105 
					106 105 122 
					123 106 122 
					112 106 123 
					106 112 115 
					108 107 124 
					107 114 124 
					121 108 124 
					109 110 125 
					111 109 126 
					125 127 109 
					127 126 109 
					125 110 120 
					112 111 126 
					113 112 123 
					112 126 115 
					114 113 124 
					123 122 113 
					122 124 113 
					128 116 115 
					128 115 129 
					126 129 115 
					116 128 117 
					130 117 128 
					117 131 119 
					131 117 130 
					131 120 118 
					118 119 131 
					120 131 125 
					122 121 124 
					131 132 125 
					127 125 132 
					126 127 129 
					127 132 129 
					129 132 128 
					130 128 132 
					130 132 131 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
