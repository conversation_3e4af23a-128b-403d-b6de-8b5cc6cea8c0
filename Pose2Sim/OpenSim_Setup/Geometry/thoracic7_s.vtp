<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.722898 -0.690955  0.000335
			 0.543506 -0.818301  0.187040
			 0.082281 -0.996609  0.000015
			 0.956446  0.000180  0.291909
			 0.990844 -0.135011  0.000222
			 0.543606 -0.818289 -0.186804
			 0.956535  0.000261 -0.291619
			 0.539214 -0.688936  0.484372
			 0.270974 -0.616450  0.739298
			-0.269531 -0.962858 -0.016062
			-0.383509 -0.923537 -0.000014
			-0.318755 -0.932733  0.168538
			-0.116744 -0.683519  0.720536
			 0.539210 -0.688941 -0.484368
			 0.270974 -0.616455 -0.739295
			-0.269528 -0.962859  0.016038
			-0.318750 -0.932735 -0.168535
			-0.116736 -0.683523 -0.720534
			 0.952062  0.305905  0.000173
			 0.878688  0.331630  0.343408
			 0.637901  0.207838  0.741542
			 0.878790  0.331683 -0.343095
			 0.637901  0.207842 -0.741541
			 0.217847  0.174836  0.960195
			-0.816579 -0.575985 -0.037956
			-0.955795 -0.294034 -0.000020
			-0.816579 -0.575984  0.037954
			-0.247900 -0.059695  0.966945
			-0.692554 -0.481051  0.537548
			-0.883909 -0.419961 -0.205760
			 0.217847  0.174836 -0.960195
			-0.692555 -0.481051 -0.537548
			-0.247898 -0.059695 -0.966945
			-0.883911 -0.419959  0.205760
			 0.999896 -0.014393  0.000371
			 0.918068 -0.106641  0.381810
			 0.590231  0.261397  0.763740
			 0.918186 -0.106635 -0.381527
			 0.590233  0.261396 -0.763739
			 0.201432  0.177065  0.963366
			-0.960062 -0.075627 -0.269371
			-0.995828 -0.091254 -0.000082
			-0.960071 -0.075635  0.269337
			-0.147768  0.017325  0.988870
			-0.076970 -0.498051  0.863725
			-0.711140 -0.552645 -0.434585
			 0.201432  0.177065 -0.963366
			-0.076970 -0.498051 -0.863725
			-0.147768  0.017325 -0.988870
			-0.711140 -0.552645  0.434585
			 0.995044 -0.099438  0.000217
			 0.932344 -0.151935  0.328101
			 0.557522 -0.243918  0.793519
			 0.932403 -0.151924 -0.327940
			 0.557522 -0.243918 -0.793519
			 0.206793 -0.299339  0.931468
			-0.960216 -0.072397 -0.269711
			-0.408372  0.026740 -0.912424
			-0.997393 -0.072167 -0.000159
			-0.960242 -0.072425  0.269611
			-0.408370  0.026731  0.912425
			-0.000633 -0.300711  0.953715
			 0.139949  0.011612  0.990091
			-0.023853 -0.980300  0.196067
			-0.143099 -0.628913  0.764193
			 0.027483 -0.075627  0.996757
			 0.674619  0.115268 -0.729111
			 0.206793 -0.299339 -0.931468
			 0.139949  0.011612 -0.990091
			-0.000633 -0.300711 -0.953715
			-0.143099 -0.628913 -0.764193
			-0.023854 -0.980301 -0.196064
			 0.027483 -0.075627 -0.996757
			 0.674777  0.115336  0.728954
			 0.855440  0.517902  0.000183
			 0.717485  0.607448  0.340914
			 0.589428 -0.233024  0.773482
			 0.717524  0.607479 -0.340777
			 0.589430 -0.233018 -0.773482
			 0.169857 -0.337626  0.925828
			-0.521773  0.786570 -0.330243
			-0.242088 -0.001328 -0.970253
			-0.685510  0.728063 -0.000228
			-0.521822  0.786594  0.330109
			-0.242085 -0.001339  0.970254
			-0.091126 -0.214589  0.972444
			-0.180385  0.236063  0.954848
			-0.127346  0.700175  0.702523
			-0.160808 -0.564042  0.809937
			-0.291764 -0.576286  0.763393
			 0.472326 -0.151664  0.868278
			 0.827838 -0.333527  0.451048
			 0.070919  0.213764  0.974308
			-0.491090 -0.621830  0.610047
			 0.998043 -0.062526 -0.000207
			 0.720285 -0.515884 -0.463739
			-0.520319  0.853972  0.000059
			-0.446789  0.528480 -0.721865
			 0.169857 -0.337626 -0.925828
			-0.180385  0.236063 -0.954848
			-0.091126 -0.214589 -0.972444
			-0.127346  0.700175 -0.702523
			-0.291760 -0.576289 -0.763392
			-0.160810 -0.564044 -0.809935
			 0.472326 -0.151664 -0.868278
			 0.827842 -0.333520 -0.451046
			-0.491090 -0.621830 -0.610047
			 0.070919  0.213764 -0.974308
			 0.720287 -0.515923  0.463692
			-0.446703  0.528518  0.721890
			 0.032158  0.999483  0.000000
			 0.460812  0.712414  0.529262
			 0.249597  0.498694  0.830063
			 0.460805  0.712426 -0.529253
			 0.249600  0.498704 -0.830057
			-0.118918  0.460410  0.879705
			-0.219604  0.970060 -0.103718
			-0.076090  0.934040  0.348969
			-0.676104  0.541267 -0.499914
			-0.145037  0.912703  0.382018
			-0.076090  0.934040 -0.348969
			-0.219604  0.970060  0.103716
			-0.676104  0.541264  0.499916
			-0.145027  0.912708 -0.382010
			 0.268088  0.614853  0.741677
			 0.267363  0.655095  0.706659
			 0.477322  0.324436  0.816643
			 0.272865 -0.673637  0.686847
			 0.524545  0.226463  0.820712
			-0.813689  0.279791  0.509537
			-0.113952  0.136284  0.984094
			-0.722366 -0.619756 -0.306740
			-0.910708  0.273195 -0.309800
			 0.910198 -0.271228  0.313009
			-0.088813  0.234797  0.967979
			 0.103663  0.000240  0.994613
			-0.829727 -0.472175  0.297664
			-0.966427  0.158398 -0.202311
			 0.053264 -0.998580 -0.000120
			-0.045933 -0.919225 -0.391045
			-0.864820  0.502082  0.000208
			-0.553133  0.757869  0.345945
			-0.781321  0.601891  0.165120
			-0.908795  0.355295 -0.218761
			-0.553012  0.758040 -0.345765
			-0.781238  0.602046 -0.164950
			-0.908783  0.355286  0.218827
			-0.793819  0.300905 -0.528496
			-0.118913  0.460417 -0.879702
			 0.268088  0.614853 -0.741677
			 0.267363  0.655095 -0.706659
			-0.113947  0.136290 -0.984094
			-0.813689  0.279789 -0.509538
			 0.272865 -0.673639 -0.686844
			-0.722364 -0.619760  0.306740
			-0.910713  0.273189  0.309789
			 0.477322  0.324436 -0.816643
			 0.524545  0.226463 -0.820712
			 0.910203 -0.271221 -0.313001
			-0.088815  0.234800 -0.967978
			-0.829727 -0.472175 -0.297664
			 0.103663  0.000240 -0.994613
			-0.966427  0.158398  0.202311
			-0.045812 -0.919245  0.391012
			-0.793820  0.300907  0.528493
			-0.852582  0.397664 -0.339067
			-0.650360  0.092790 -0.753937
			-0.852582  0.397664  0.339067
			-0.650360  0.092790  0.753937
			-0.514524 -0.326454  0.792902
			-0.574646 -0.771332  0.273549
			-0.878950  0.191954  0.436578
			-0.815367 -0.409755  0.408997
			-0.672157 -0.612266  0.416336
			-0.823827  0.505383  0.256706
			 0.419563 -0.871915  0.252450
			 0.240564 -0.914575 -0.325086
			 0.788510 -0.615022 -0.000082
			 0.240533 -0.914628  0.324960
			-0.747869  0.418835  0.515043
			-0.865479  0.500945 -0.000649
			-0.805064  0.137906  0.576935
			-0.747929  0.418827 -0.514962
			-0.804851  0.138255 -0.577149
			-0.823828  0.505381 -0.256707
			-0.815370 -0.409750 -0.408994
			-0.878953  0.191950 -0.436575
			-0.672155 -0.612270 -0.416333
			-0.514524 -0.326454 -0.792902
			-0.574646 -0.771332 -0.273549
			 0.419559 -0.871917 -0.252451
			-0.676614  0.096195  0.730027
			-0.019484 -0.954364  0.298009
			-0.026455 -0.965204 -0.260157
			 0.720212 -0.693754  0.000033
			-0.026457 -0.965205  0.260153
			-0.019516 -0.954350 -0.298052
			-0.730873  0.682513 -0.001253
			-0.675729  0.095928 -0.730882
			-0.101807  0.207515  0.972920
			-0.059812 -0.007914  0.998178
			 0.611154 -0.676359  0.411132
			 0.184451 -0.982842 -0.000439
			 0.611151 -0.676356 -0.411141
			-0.059841 -0.007898 -0.998177
			-0.833011  0.065641  0.549349
			-0.983193 -0.182565 -0.000956
			-0.101078  0.206751 -0.973158
			-0.832048  0.065244 -0.550854
			-0.282786 -0.670755  0.685653
			-0.594628 -0.804000 -0.000880
			-0.282061 -0.670496 -0.686204
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.038279  0.285732  0.000023
			-0.039007  0.285977  0.007643
			-0.051775  0.285146  0.000003
			-0.037915  0.289653  0.009168
			-0.037528  0.287166  0.000025
			-0.039007  0.285977 -0.007643
			-0.037915  0.289653 -0.009168
			-0.043103  0.286308  0.015572
			-0.049646  0.286241  0.019429
			-0.063802  0.283878  0.004596
			-0.063945  0.284130  0.000003
			-0.064174  0.284091  0.013528
			-0.059973  0.285491  0.019533
			-0.043103  0.286308 -0.015572
			-0.049646  0.286241 -0.019429
			-0.063802  0.283878 -0.004596
			-0.064174  0.284091 -0.013528
			-0.059973  0.285491 -0.019533
			-0.038395  0.291299  0.000025
			-0.039712  0.291682  0.009816
			-0.043053  0.290235  0.016543
			-0.039712  0.291682 -0.009816
			-0.043053  0.290235 -0.016543
			-0.050933  0.290649  0.020094
			-0.067715  0.286820  0.004730
			-0.067675  0.286706  0.000003
			-0.067715  0.286820 -0.004730
			-0.061470  0.290264  0.020237
			-0.067081  0.284538  0.015192
			-0.068708  0.288088  0.008989
			-0.050933  0.290649 -0.020094
			-0.067081  0.284538 -0.015192
			-0.061470  0.290264 -0.020237
			-0.068708  0.288088 -0.008989
			-0.038608  0.300011  0.000028
			-0.039938  0.299028  0.009473
			-0.045020  0.292763  0.016131
			-0.039938  0.299028 -0.009473
			-0.045020  0.292763 -0.016131
			-0.051232  0.294438  0.018280
			-0.067828  0.293323  0.006064
			-0.067343  0.293353  0.000005
			-0.067828  0.293323 -0.006064
			-0.061537  0.293581  0.019153
			-0.066621  0.292128  0.018110
			-0.069674  0.293815  0.009275
			-0.051232  0.294438 -0.018280
			-0.066621  0.292128 -0.018110
			-0.061537  0.293581 -0.019153
			-0.069674  0.293815 -0.009275
			-0.038244  0.303462  0.000029
			-0.038834  0.303111  0.010479
			-0.045699  0.299486  0.016751
			-0.038834  0.303111 -0.010479
			-0.045699  0.299486 -0.016751
			-0.050523  0.299597  0.018364
			-0.069411  0.302242  0.006267
			-0.071367  0.302622  0.009698
			-0.068701  0.302335  0.000007
			-0.069411  0.302242 -0.006267
			-0.071367  0.302622 -0.009698
			-0.060462  0.299934  0.019291
			-0.065171  0.300801  0.019666
			-0.072582  0.291750  0.016315
			-0.069916  0.294221  0.020003
			-0.071559  0.300658  0.023918
			-0.077941  0.295343  0.003560
			-0.050523  0.299597 -0.018364
			-0.065171  0.300801 -0.019666
			-0.060462  0.299934 -0.019291
			-0.069916  0.294221 -0.020003
			-0.072582  0.291750 -0.016315
			-0.071559  0.300658 -0.023918
			-0.077941  0.295343 -0.003560
			-0.038559  0.307319  0.000029
			-0.039369  0.307110  0.010672
			-0.043933  0.302769  0.017731
			-0.039369  0.307110 -0.010672
			-0.043933  0.302769 -0.017731
			-0.049935  0.302317  0.020305
			-0.070204  0.308545  0.007989
			-0.070883  0.307048  0.010413
			-0.068542  0.307742  0.000009
			-0.070204  0.308545 -0.007989
			-0.070883  0.307048 -0.010413
			-0.058460  0.303661  0.021284
			-0.066321  0.304606  0.019004
			-0.072547  0.305696  0.019049
			-0.081220  0.298478  0.022079
			-0.079281  0.294856  0.016409
			-0.076214  0.289606  0.023617
			-0.074422  0.289414  0.010056
			-0.075968  0.297602  0.022714
			-0.077329  0.291466  0.020649
			-0.078314  0.290578  0.000003
			-0.081071  0.280025  0.004883
			-0.086374  0.296223  0.000005
			-0.080380  0.300492  0.007369
			-0.049935  0.302317 -0.020305
			-0.066321  0.304606 -0.019004
			-0.058460  0.303661 -0.021284
			-0.072547  0.305696 -0.019049
			-0.079281  0.294856 -0.016409
			-0.081220  0.298478 -0.022079
			-0.076214  0.289606 -0.023617
			-0.074422  0.289414 -0.010056
			-0.077329  0.291466 -0.020649
			-0.075968  0.297602 -0.022714
			-0.081071  0.280025 -0.004883
			-0.080380  0.300492 -0.007369
			-0.053551  0.307530  0.000009
			-0.044288  0.306950  0.017976
			-0.051127  0.307138  0.021286
			-0.044288  0.306950 -0.017976
			-0.051127  0.307138 -0.021286
			-0.059948  0.306795  0.021416
			-0.079654  0.308424  0.015276
			-0.072329  0.307272  0.013466
			-0.079305  0.306104  0.009608
			-0.068326  0.308115  0.016344
			-0.072329  0.307272 -0.013466
			-0.079654  0.308424 -0.015276
			-0.079305  0.306104 -0.009608
			-0.068326  0.308115 -0.016344
			-0.078455  0.302266  0.020274
			-0.077639  0.304971  0.017562
			-0.092366  0.307427  0.033211
			-0.086420  0.299647  0.020137
			-0.081367  0.303540  0.020312
			-0.089853  0.285615  0.012070
			-0.085656  0.287071  0.014776
			-0.092540  0.298996  0.024182
			-0.084623  0.297403  0.012105
			-0.074845  0.287627  0.011456
			-0.089515  0.282154  0.016275
			-0.079733  0.297790  0.025123
			-0.081205  0.295864  0.022911
			-0.082532  0.301917  0.023234
			-0.083905  0.284351  0.000003
			-0.080758  0.279156  0.006399
			-0.096171  0.285127  0.000004
			-0.087257  0.293153  0.004733
			-0.083432  0.292342  0.007925
			-0.083591  0.296048  0.009933
			-0.087257  0.293153 -0.004733
			-0.083432  0.292342 -0.007925
			-0.083591  0.296048 -0.009933
			-0.081460  0.300910  0.010838
			-0.059948  0.306795 -0.021416
			-0.078455  0.302266 -0.020274
			-0.077639  0.304971 -0.017562
			-0.085656  0.287071 -0.014776
			-0.089853  0.285615 -0.012070
			-0.086420  0.299647 -0.020137
			-0.092540  0.298996 -0.024182
			-0.084623  0.297403 -0.012105
			-0.092366  0.307427 -0.033211
			-0.081367  0.303540 -0.020312
			-0.074845  0.287627 -0.011456
			-0.089515  0.282154 -0.016275
			-0.081205  0.295864 -0.022911
			-0.079733  0.297790 -0.025123
			-0.082532  0.301917 -0.023234
			-0.080758  0.279156 -0.006399
			-0.081460  0.300910 -0.010838
			-0.097661  0.304599  0.027837
			-0.085366  0.302364  0.013293
			-0.097661  0.304599 -0.027837
			-0.085366  0.302364 -0.013293
			-0.095825  0.303383  0.036798
			-0.097467  0.301386  0.028594
			-0.092074  0.283150  0.009798
			-0.092064  0.279302  0.009271
			-0.089177  0.276535  0.016043
			-0.089802  0.287385  0.008450
			-0.081799  0.277207  0.012717
			-0.083225  0.281766  0.005089
			-0.086222  0.279849  0.000001
			-0.083225  0.281766 -0.005089
			-0.094760  0.287293  0.003997
			-0.102400  0.274463 -0.000015
			-0.094264  0.283488  0.003889
			-0.094760  0.287293 -0.003997
			-0.094264  0.283488 -0.003889
			-0.089802  0.287385 -0.008450
			-0.092064  0.279302 -0.009271
			-0.092074  0.283150 -0.009798
			-0.089177  0.276535 -0.016043
			-0.095825  0.303383 -0.036798
			-0.097467  0.301386 -0.028594
			-0.081799  0.277207 -0.012717
			-0.094268  0.279834  0.004057
			-0.093240  0.278057  0.004756
			-0.088223  0.277056  0.007293
			-0.092883  0.273878 -0.000000
			-0.088223  0.277056 -0.007293
			-0.093240  0.278057 -0.004756
			-0.108590  0.267828 -0.000019
			-0.094268  0.279834 -0.004057
			-0.100998  0.267792  0.004413
			-0.095570  0.271677  0.004430
			-0.101252  0.260288  0.003440
			-0.102972  0.259706 -0.000003
			-0.101252  0.260288 -0.003440
			-0.095570  0.271677 -0.004430
			-0.109799  0.264837  0.004462
			-0.110581  0.263610 -0.000021
			-0.100998  0.267792 -0.004413
			-0.109799  0.264837 -0.004462
			-0.107933  0.261879  0.005196
			-0.106888  0.260329 -0.000020
			-0.107933  0.261879 -0.005196
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
