<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.589163 -0.808014  0.000328
			 0.378364 -0.907175  0.184051
			-0.133787 -0.991010  0.000015
			 0.945089 -0.176898  0.274797
			 0.951109 -0.308855  0.000214
			 0.378463 -0.907180 -0.183819
			 0.945181 -0.176833 -0.274522
			 0.406694 -0.778803  0.477563
			 0.148105 -0.658306  0.738037
			-0.479595 -0.877350 -0.015698
			-0.585989 -0.810319 -0.000014
			-0.519849 -0.838153  0.165095
			-0.264739 -0.647021  0.715037
			 0.406689 -0.778807 -0.477560
			 0.148103 -0.658310 -0.738033
			-0.479592 -0.877352  0.015672
			-0.519843 -0.838157 -0.165093
			-0.264734 -0.647027 -0.715033
			 0.994145  0.108056  0.000161
			 0.935380  0.145692  0.322238
			 0.691955  0.074594  0.718076
			 0.935477  0.145721 -0.321945
			 0.691956  0.074598 -0.718075
			 0.265831  0.127403  0.955564
			-0.923281 -0.382533 -0.034942
			-0.994912 -0.100744 -0.000019
			-0.923281 -0.382534  0.034938
			-0.263511 -0.011733  0.964585
			-0.785848 -0.334206  0.520336
			-0.953813 -0.229492 -0.193841
			 0.265831  0.127403 -0.955564
			-0.785848 -0.334206 -0.520336
			-0.263511 -0.011733 -0.964585
			-0.953813 -0.229492  0.193841
			 0.981195 -0.193017  0.000346
			 0.892769 -0.266684  0.363103
			 0.657009  0.137343  0.741267
			 0.892873 -0.266695 -0.362837
			 0.657010  0.137342 -0.741266
			 0.250304  0.133738  0.958886
			-0.976383  0.147676 -0.157699
			-0.995399  0.095814 -0.000076
			-0.976389  0.147670  0.157666
			-0.148137  0.043621  0.988004
			-0.182751 -0.475000  0.860800
			-0.812849 -0.381524 -0.440131
			 0.250304  0.133738 -0.958886
			-0.182751 -0.475000 -0.860800
			-0.148137  0.043621 -0.988004
			-0.812849 -0.381524  0.440131
			 0.962439 -0.271499  0.000202
			 0.895949 -0.314551  0.313580
			 0.522784 -0.340559  0.781484
			 0.896003 -0.314550 -0.313429
			 0.522784 -0.340559 -0.781484
			 0.152148 -0.333199  0.930500
			-0.967843  0.246686  0.049253
			-0.701270  0.167276 -0.692993
			-0.882320  0.470650 -0.000156
			-0.967844  0.246665 -0.049347
			-0.701270  0.167270  0.692994
			-0.067316 -0.294640  0.953234
			 0.143367 -0.031847  0.989157
			-0.246709 -0.948996  0.196322
			-0.285450 -0.585057  0.759096
			 0.013202 -0.057826  0.998239
			 0.684421 -0.032629 -0.728357
			 0.152148 -0.333199 -0.930500
			 0.143367 -0.031847 -0.989157
			-0.067316 -0.294640 -0.953234
			-0.285450 -0.585057 -0.759096
			-0.246711 -0.948995 -0.196320
			 0.013202 -0.057826 -0.998239
			 0.684595 -0.032597  0.728195
			 0.940067  0.340989  0.000169
			 0.834133  0.450486  0.318252
			 0.555186 -0.338159  0.759880
			 0.834172  0.450504 -0.318124
			 0.555188 -0.338154 -0.759880
			 0.104299 -0.363994  0.925543
			-0.205550  0.945268 -0.253412
			-0.436157  0.226581 -0.870877
			-0.300790  0.953690 -0.000192
			-0.205580  0.945295  0.253288
			-0.436156  0.226575  0.870880
			-0.143436 -0.192761  0.970706
			-0.159234  0.394300  0.905081
			 0.073655  0.746053  0.661801
			-0.288834 -0.510854  0.809693
			-0.428156 -0.498894  0.753517
			 0.436416 -0.213954  0.873937
			 0.760409 -0.464973  0.453407
			 0.146212  0.267687  0.952347
			-0.632609 -0.492021  0.598098
			 0.971628 -0.236515 -0.000212
			 0.630728 -0.628039 -0.455795
			-0.345596  0.938383  0.000049
			-0.341243  0.577122 -0.741946
			 0.104299 -0.363994 -0.925543
			-0.159234  0.394300 -0.905081
			-0.143436 -0.192761 -0.970706
			 0.073655  0.746053 -0.661801
			-0.428154 -0.498897 -0.753516
			-0.288836 -0.510856 -0.809691
			 0.436416 -0.213954 -0.873937
			 0.760410 -0.464965 -0.453414
			-0.632609 -0.492021 -0.598098
			 0.146212  0.267687 -0.952347
			 0.630711 -0.628092  0.455745
			-0.341153  0.577139  0.741974
			 0.213733  0.976892 -0.000004
			 0.618441  0.599839  0.507666
			 0.369682  0.436885  0.820041
			 0.618435  0.599853 -0.507656
			 0.369687  0.436894 -0.820034
			-0.034057  0.581117  0.813107
			 0.017275  0.994046 -0.107580
			 0.178407  0.918456  0.353001
			-0.516647  0.674130 -0.527850
			 0.007379  0.960755  0.277302
			 0.178407  0.918456 -0.353001
			 0.017276  0.994047  0.107578
			-0.516648  0.674127  0.527852
			 0.007393  0.960756 -0.277297
			 0.441841  0.526349  0.726453
			 0.483938  0.547761  0.682468
			 0.553874  0.232496  0.799480
			 0.136042 -0.715119  0.685637
			 0.585811  0.119191  0.801635
			-0.763384  0.416745  0.493526
			-0.088463  0.153522  0.984178
			-0.849779 -0.446727 -0.279842
			-0.856864  0.422460 -0.295485
			 0.856089 -0.411812  0.312288
			-0.025801  0.246310  0.968847
			 0.113991 -0.023803  0.993197
			-0.917237 -0.286252  0.277012
			-0.924820  0.324080 -0.199199
			-0.151639 -0.988436 -0.000119
			-0.244786 -0.888373 -0.388425
			-0.767754  0.640745  0.000192
			-0.412547  0.843472  0.344036
			-0.663685  0.729825  0.163945
			-0.837866  0.503419 -0.211071
			-0.412383  0.843623 -0.343861
			-0.663567  0.729969 -0.163781
			-0.837856  0.503408  0.211135
			-0.741451  0.433746 -0.511972
			-0.034052  0.581121 -0.813104
			 0.441841  0.526349 -0.726453
			 0.483938  0.547761 -0.682468
			-0.088459  0.153528 -0.984177
			-0.763385  0.416743 -0.493528
			 0.136042 -0.715121 -0.685634
			-0.849777 -0.446730  0.279841
			-0.856870  0.422456  0.295474
			 0.553874  0.232496 -0.799480
			 0.585811  0.119191 -0.801635
			 0.856089 -0.411812 -0.312288
			-0.025803  0.246314 -0.968847
			-0.917237 -0.286252 -0.277012
			 0.113991 -0.023803 -0.993197
			-0.924820  0.324080  0.199199
			-0.244671 -0.888418  0.388394
			-0.741452  0.433748  0.511968
			-0.772207  0.545578 -0.325638
			-0.642215  0.222214 -0.733608
			-0.772207  0.545578  0.325638
			-0.642215  0.222214  0.733608
			-0.592663 -0.224475  0.773539
			-0.720935 -0.637538  0.271659
			-0.842471  0.343015  0.415431
			-0.893316 -0.232822  0.384422
			-0.789055 -0.463934  0.402688
			-0.732333  0.635341  0.245012
			 0.255387 -0.932321  0.256036
			 0.060672 -0.944491 -0.322887
			 0.677272 -0.735733 -0.000079
			 0.060632 -0.944536  0.322762
			-0.672037  0.547465  0.498646
			-0.774205  0.632935 -0.000681
			-0.783351  0.280229  0.554827
			-0.672090  0.547467 -0.498573
			-0.783065  0.280539 -0.555075
			-0.732334  0.635339 -0.245013
			-0.893315 -0.232826 -0.384423
			-0.842471  0.343016 -0.415431
			-0.789054 -0.463938 -0.402685
			-0.592663 -0.224475 -0.773539
			-0.720935 -0.637538 -0.271659
			 0.255382 -0.932323 -0.256037
			-0.677713  0.208680  0.705094
			-0.140417 -0.934720  0.326468
			-0.233234 -0.936173 -0.263026
			 0.577900 -0.816107  0.000027
			-0.233237 -0.936173  0.263022
			-0.140435 -0.934702 -0.326511
			-0.588688  0.808358 -0.001604
			-0.676847  0.208093 -0.706099
			-0.150728  0.201380  0.967847
			-0.192416  0.020158  0.981106
			 0.426566 -0.764645  0.483073
			-0.031907 -0.999490 -0.000870
			 0.426565 -0.764621 -0.483112
			-0.192430  0.020169 -0.981104
			-0.785712  0.202130  0.584637
			-0.998992  0.044862 -0.001134
			-0.149834  0.199829 -0.968307
			-0.784760  0.201242 -0.586220
			-0.457781 -0.621786  0.635468
			-0.714838 -0.699289 -0.000914
			-0.456794 -0.621811 -0.636153
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.039673  0.333644  0.000023
			-0.040289  0.334033  0.007643
			-0.052015  0.335842  0.000003
			-0.038642  0.337410  0.009168
			-0.038736  0.334895  0.000025
			-0.040289  0.334033 -0.007643
			-0.038642  0.337410 -0.009168
			-0.043944  0.335199  0.015572
			-0.049889  0.336477  0.019429
			-0.063147  0.337070  0.004596
			-0.063232  0.337346  0.000003
			-0.063446  0.337354  0.013528
			-0.059387  0.337863  0.019533
			-0.043944  0.335199 -0.015572
			-0.049889  0.336477 -0.019429
			-0.063147  0.337070 -0.004596
			-0.063446  0.337354 -0.013528
			-0.059387  0.337863 -0.019533
			-0.038782  0.339122  0.000025
			-0.039907  0.339767  0.009816
			-0.043196  0.339035  0.016543
			-0.039907  0.339767 -0.009816
			-0.043196  0.339035 -0.016543
			-0.050267  0.341059  0.020094
			-0.066169  0.340755  0.004730
			-0.066153  0.340635  0.000003
			-0.066169  0.340755 -0.004730
			-0.059891  0.342846  0.020237
			-0.066003  0.338389  0.015192
			-0.066842  0.342201  0.008989
			-0.050267  0.341059 -0.020094
			-0.066003  0.338389 -0.015192
			-0.059891  0.342846 -0.020237
			-0.066842  0.342201 -0.008989
			-0.037417  0.347699  0.000028
			-0.038799  0.347009  0.009473
			-0.044527  0.341915  0.016131
			-0.038799  0.347009 -0.009473
			-0.044527  0.341915 -0.016131
			-0.049861  0.344833  0.018280
			-0.065108  0.347148  0.006064
			-0.064663  0.347077  0.000005
			-0.065108  0.347148 -0.006064
			-0.059357  0.346109  0.019153
			-0.064227  0.345730  0.018110
			-0.066694  0.348009  0.009275
			-0.049861  0.344833 -0.018280
			-0.064227  0.345730 -0.018110
			-0.059357  0.346109 -0.019153
			-0.066694  0.348009 -0.009275
			-0.036470  0.351005  0.000029
			-0.037067  0.350782  0.010479
			-0.043940  0.348641  0.016751
			-0.037067  0.350782 -0.010479
			-0.043940  0.348641 -0.016751
			-0.048294  0.349740  0.018364
			-0.064947  0.356210  0.006267
			-0.064065  0.355206  0.009698
			-0.064287  0.356155  0.000007
			-0.064947  0.356210 -0.006267
			-0.064065  0.355206 -0.009698
			-0.057246  0.352111  0.019291
			-0.061361  0.353927  0.019666
			-0.069700  0.346583  0.016315
			-0.066841  0.348457  0.020003
			-0.067179  0.355099  0.023918
			-0.073917  0.351204  0.003560
			-0.048294  0.349740 -0.018364
			-0.061361  0.353927 -0.019666
			-0.057246  0.352111 -0.019291
			-0.066841  0.348457 -0.020003
			-0.069700  0.346583 -0.016315
			-0.067179  0.355099 -0.023918
			-0.073917  0.351204 -0.003560
			-0.036065  0.354847  0.000029
			-0.036837  0.354809  0.010672
			-0.041752  0.351494  0.017731
			-0.036837  0.354809 -0.010672
			-0.041752  0.351494 -0.017731
			-0.047274  0.352284  0.020305
			-0.061952  0.360768  0.007989
			-0.062834  0.359441  0.010413
			-0.060588  0.359641  0.000009
			-0.061952  0.360768 -0.007989
			-0.062834  0.359441 -0.010413
			-0.054764  0.355351  0.021284
			-0.061723  0.357890  0.019004
			-0.064585  0.358459  0.019049
			-0.076328  0.354948  0.022079
			-0.075218  0.351002  0.016409
			-0.073377  0.345229  0.023617
			-0.071786  0.344673  0.010056
			-0.071723  0.353011  0.022714
			-0.074055  0.347280  0.020649
			-0.075107  0.346612  0.000003
			-0.079495  0.336841  0.004883
			-0.081405  0.353797  0.000005
			-0.075207  0.356748  0.007369
			-0.047274  0.352284 -0.020305
			-0.061723  0.357890 -0.019004
			-0.054764  0.355351 -0.021284
			-0.064585  0.358459 -0.019049
			-0.075218  0.351002 -0.016409
			-0.076328  0.354948 -0.022079
			-0.073377  0.345229 -0.023617
			-0.071786  0.344673 -0.010056
			-0.074055  0.347280 -0.020649
			-0.071723  0.353011 -0.022714
			-0.079495  0.336841 -0.004883
			-0.075207  0.356748 -0.007369
			-0.049621  0.358133  0.000009
			-0.041325  0.355663  0.017976
			-0.047493  0.357251  0.021286
			-0.041325  0.355663 -0.017976
			-0.047493  0.357251 -0.021286
			-0.055553  0.358726  0.021416
			-0.073130  0.364368  0.015276
			-0.064105  0.359958  0.013466
			-0.073228  0.362024  0.009608
			-0.060325  0.359962  0.016344
			-0.064105  0.359958 -0.013466
			-0.073130  0.364368 -0.015276
			-0.073228  0.362024 -0.009608
			-0.060325  0.359962 -0.016344
			-0.073144  0.358090  0.020274
			-0.071920  0.360572  0.017562
			-0.084834  0.366003  0.033211
			-0.080835  0.357160  0.020137
			-0.075556  0.359936  0.020312
			-0.086458  0.344120  0.012070
			-0.082392  0.344684  0.014776
			-0.086500  0.357779  0.024182
			-0.079607  0.354593  0.012105
			-0.072490  0.343009  0.011456
			-0.086770  0.340660  0.016275
			-0.075103  0.353968  0.025123
			-0.076782  0.352384  0.022911
			-0.076903  0.358586  0.023234
			-0.081291  0.341660  0.000003
			-0.079367  0.335925  0.006399
			-0.092273  0.344940  0.000004
			-0.082755  0.350971  0.004733
			-0.079432  0.349391  0.007925
			-0.078913  0.353054  0.009933
			-0.082755  0.350971 -0.004733
			-0.079432  0.349391 -0.007925
			-0.078913  0.353054 -0.009933
			-0.076111  0.357379  0.010838
			-0.055553  0.358726 -0.021416
			-0.073144  0.358090 -0.020274
			-0.071920  0.360572 -0.017562
			-0.082392  0.344684 -0.014776
			-0.086458  0.344120 -0.012070
			-0.080835  0.357160 -0.020137
			-0.086500  0.357779 -0.024182
			-0.079607  0.354593 -0.012105
			-0.084834  0.366003 -0.033211
			-0.075556  0.359936 -0.020312
			-0.072490  0.343009 -0.011456
			-0.086770  0.340660 -0.016275
			-0.076782  0.352384 -0.022911
			-0.075103  0.353968 -0.025123
			-0.076903  0.358586 -0.023234
			-0.079367  0.335925 -0.006399
			-0.076111  0.357379 -0.010838
			-0.090141  0.364320  0.027837
			-0.079393  0.359606  0.013293
			-0.090141  0.364320 -0.027837
			-0.079393  0.359606 -0.013293
			-0.088694  0.362752  0.036798
			-0.090540  0.361132  0.028594
			-0.088913  0.342162  0.009798
			-0.089592  0.338391  0.009271
			-0.087469  0.335087  0.016043
			-0.086094  0.345843  0.008450
			-0.080659  0.334231  0.012717
			-0.081137  0.338989  0.005089
			-0.084197  0.337726  0.000001
			-0.081137  0.338989 -0.005089
			-0.090607  0.346772  0.003997
			-0.099830  0.335772 -0.000015
			-0.090838  0.342943  0.003889
			-0.090607  0.346772 -0.003997
			-0.090838  0.342943 -0.003889
			-0.086094  0.345843 -0.008450
			-0.089592  0.338391 -0.009271
			-0.088913  0.342162 -0.009798
			-0.087469  0.335087 -0.016043
			-0.088694  0.362752 -0.036798
			-0.090540  0.361132 -0.028594
			-0.080659  0.334231 -0.012717
			-0.091495  0.339364  0.004057
			-0.090881  0.337413  0.004756
			-0.086511  0.335401  0.007293
			-0.091305  0.333246 -0.000000
			-0.086511  0.335401 -0.007293
			-0.090881  0.337413 -0.004756
			-0.106629  0.330545 -0.000019
			-0.091495  0.339364 -0.004057
			-0.100749  0.328902  0.002876
			-0.094135  0.331641  0.004430
			-0.102322  0.321603  0.002242
			-0.102988  0.321435 -0.000003
			-0.102322  0.321603 -0.002242
			-0.094135  0.331641 -0.004430
			-0.109259  0.327813  0.002908
			-0.110187  0.326772 -0.000014
			-0.100749  0.328902 -0.002876
			-0.109259  0.327813 -0.002908
			-0.108096  0.324533  0.003387
			-0.106427  0.322850 -0.000020
			-0.108096  0.324533 -0.003387
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
