<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="145" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="286">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.404464  0.862097 -0.305283
			 0.099217  0.964243 -0.245748
			 0.026104  0.982554  0.184135
			 0.666257  0.727737 -0.162791
			 0.655919  0.487565 -0.576239
			 0.928679  0.264005 -0.260492
			-0.238191  0.907388 -0.346283
			 0.132200  0.532056 -0.836325
			 0.454900  0.841861  0.290406
			 0.101367  0.987396  0.121550
			-0.013366  0.996727 -0.079732
			-0.225600  0.833053  0.505101
			-0.654609  0.691836  0.304713
			-0.571044  0.758817 -0.313219
			 0.916760  0.306946  0.255606
			 0.700428  0.170078 -0.693163
			 0.935156  0.312151 -0.167469
			 0.957572  0.242035  0.156447
			-0.484081  0.246801 -0.839497
			 0.075449 -0.061214 -0.995269
			 0.496504  0.774764  0.391439
			-0.093233  0.987250  0.129014
			-0.883328  0.013807  0.468552
			-0.474995  0.706983  0.523980
			-0.918853 -0.113197 -0.378015
			 0.740111  0.338646  0.580995
			 0.477769 -0.114858 -0.870945
			 0.927486  0.233118 -0.292278
			 0.910836  0.345743  0.225477
			 0.680517  0.309110  0.664340
			-0.470906 -0.354024 -0.808031
			-0.135823 -0.455428 -0.879851
			 0.072202  0.532564  0.843305
			-0.668659  0.153829  0.727484
			-0.496395 -0.811859  0.307370
			-0.647343 -0.723827 -0.238794
			-0.048333  0.091319  0.994648
			 0.219096 -0.261758 -0.939936
			 0.890760 -0.059736 -0.450532
			 0.933568 -0.081727  0.348958
			 0.491573 -0.213073  0.844367
			 0.054849 -0.166415  0.984529
			-0.463952 -0.636038 -0.616607
			-0.260753 -0.360373 -0.895622
			-0.669781 -0.253240  0.698043
			-0.755140 -0.590407  0.284925
			-0.636474 -0.744012 -0.203339
			-0.758059 -0.150440  0.634598
			 0.103936 -0.094557 -0.990079
			 0.851904 -0.131175 -0.507004
			 0.867304 -0.128138  0.481004
			 0.189296 -0.309039  0.932020
			 0.159184 -0.533822  0.830478
			-0.205958 -0.282676  0.936843
			-0.489348 -0.300323 -0.818746
			-0.218429 -0.057737 -0.974143
			-0.967219 -0.212528  0.138994
			-0.891997 -0.252722 -0.374796
			-0.980359 -0.051980  0.190248
			 0.176601 -0.055330 -0.982726
			 0.870423 -0.130823 -0.474604
			 0.830374 -0.149859  0.536676
			 0.116195 -0.143350  0.982827
			 0.022469 -0.201507  0.979229
			-0.903608 -0.055118  0.424800
			-0.373668 -0.083955  0.923755
			-0.362991 -0.040991 -0.930890
			-0.105383 -0.009237 -0.994389
			-0.890978 -0.028272 -0.453165
			-0.937071 -0.026891 -0.348101
			 0.336464 -0.025675 -0.941346
			 0.919229 -0.086800 -0.384037
			 0.863153 -0.092481  0.496401
			 0.208930 -0.057405  0.976244
			-0.080159 -0.045284  0.995753
			-0.477978 -0.018952  0.878167
			-0.953124 -0.051301  0.298198
			-0.973930  0.004512  0.226805
			-0.326538 -0.010400 -0.945127
			-0.095135  0.020931 -0.995244
			-0.502256  0.008921 -0.864673
			-0.970570 -0.051265 -0.235299
			 0.423345  0.057263 -0.904157
			 0.944879 -0.009981 -0.327267
			 0.918449 -0.023243  0.394857
			 0.407308 -0.012506  0.913205
			 0.027386 -0.002407  0.999622
			-0.464014  0.014197  0.885714
			-0.973848  0.041836  0.223314
			-0.888188  0.028844 -0.458574
			-0.932456  0.082399 -0.351762
			-0.027501  0.183441 -0.982646
			-0.334026  0.172337 -0.926675
			 0.535773  0.187562 -0.823267
			 0.959416  0.081540 -0.269948
			 0.938607  0.063105  0.339169
			 0.527979  0.052834  0.847612
			-0.022126  0.065214  0.997626
			-0.626742  0.114074  0.770832
			-0.949172  0.199186  0.243715
			-0.951784  0.171272 -0.254507
			-0.597096  0.186992 -0.780071
			 0.027029  0.250381 -0.967770
			 0.621670  0.136088 -0.771367
			 0.919977  0.296042 -0.256906
			 0.925121  0.285011  0.250839
			 0.620132  0.248844  0.743984
			 0.064167  0.194546  0.978792
			-0.533166  0.110726  0.838733
			-0.898865  0.252107  0.358447
			-0.928007 -0.093812 -0.360558
			-0.935439 -0.078050  0.344764
			-0.531355 -0.124886 -0.837894
			 0.083928 -0.085038 -0.992837
			 0.581419 -0.643177 -0.498271
			 0.935469 -0.070122 -0.346383
			 0.936736  0.050877  0.346321
			 0.581531  0.067355  0.810731
			 0.077078 -0.000878  0.997025
			-0.283218 -0.286701  0.915200
			-0.407839 -0.775996  0.481142
			-0.408769 -0.903572 -0.128321
			-0.124841 -0.988325 -0.087342
			 0.108394 -0.994013  0.013751
			-0.145477 -0.962772  0.227828
			 0.362455 -0.925727 -0.107964
			 0.588572 -0.774864  0.230584
			 0.359362 -0.606154  0.709533
			-0.013000 -0.490302  0.871456
			-0.186067 -0.635284  0.749529
			-0.096568 -0.936812  0.336240
			 0.059662 -0.979919 -0.190262
			 0.382267 -0.920658 -0.079131
			 0.051848 -0.781153  0.622183
			-0.441272 -0.889261 -0.120390
			-0.229562 -0.950884 -0.207656
			-0.052202 -0.998420 -0.020809
			 0.015980 -0.994580  0.102738
			 0.055394 -0.949744  0.308087
			 0.121643 -0.903569  0.410812
			 0.121683 -0.950089  0.287270
			 0.301556 -0.953419 -0.007455
			 0.429648 -0.841548 -0.327413
			 0.196464 -0.977490 -0.076907
			 0.092848 -0.978055 -0.186512
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			 0.016460 -0.031255 -0.009683
			 0.006000 -0.028520 -0.008335
			 0.006000 -0.031255  0.003181
			 0.027593 -0.033990 -0.003088
			 0.023854 -0.040317 -0.018777
			 0.030512 -0.041855 -0.003937
			-0.007977 -0.031083 -0.014009
			 0.006000 -0.038777 -0.028074
			 0.027593 -0.033990  0.009449
			 0.016009 -0.034675  0.015491
			 0.006000 -0.035359  0.013324
			-0.007525 -0.034503  0.019815
			-0.026973 -0.033649  0.012754
			-0.026973 -0.033649 -0.006393
			 0.030512 -0.041855  0.010298
			 0.024485 -0.052627 -0.019554
			 0.030804 -0.056559 -0.004021
			 0.030804 -0.056559  0.010382
			-0.015821 -0.046642 -0.023657
			 0.006000 -0.048694 -0.029719
			 0.019165 -0.036555  0.019372
			 0.006000 -0.031255  0.020178
			-0.031351 -0.054507  0.014025
			-0.011132 -0.042881  0.024252
			-0.031351 -0.054507 -0.007664
			 0.024576 -0.045959  0.026026
			 0.024937 -0.068357 -0.020108
			 0.038683 -0.068529 -0.006308
			 0.038683 -0.068529  0.012669
			 0.027371 -0.062202  0.029463
			-0.015280 -0.054165 -0.022992
			 0.006000 -0.068186 -0.023687
			 0.006000 -0.035359  0.036354
			-0.015371 -0.047497  0.029463
			-0.027850 -0.059636  0.013008
			-0.027850 -0.059636 -0.006647
			 0.006000 -0.055875  0.037450
			 0.022682 -0.080667 -0.017336
			 0.039000 -0.082205 -0.007241
			 0.039000 -0.082205  0.013603
			 0.028633 -0.070580  0.031017
			 0.006000 -0.058953  0.038273
			-0.011042 -0.065450 -0.017780
			 0.006000 -0.079129 -0.013818
			-0.013477 -0.059294  0.027134
			-0.020554 -0.062715  0.010890
			-0.020554 -0.062715 -0.004528
			-0.011312 -0.069554  0.024473
			 0.019616 -0.117256 -0.013565
			 0.038389 -0.101014 -0.006224
			 0.038389 -0.101014  0.012585
			 0.024304 -0.084087  0.025692
			 0.006000 -0.067159  0.028403
			-0.007256 -0.101014  0.019482
			-0.005362 -0.079642 -0.010793
			 0.006000 -0.133499 -0.007785
			-0.012676 -0.080155  0.008602
			-0.012676 -0.080155 -0.002242
			-0.010049 -0.134867  0.007841
			 0.017092 -0.186161 -0.010460
			 0.032262 -0.137261 -0.004445
			 0.032262 -0.137261  0.010806
			 0.019797 -0.110418  0.020148
			 0.006000 -0.083575  0.020452
			-0.010049 -0.169746  0.007841
			-0.004641 -0.126660  0.016267
			-0.002566 -0.134183 -0.007355
			 0.006000 -0.235059 -0.005866
			-0.010049 -0.134867 -0.001480
			-0.010049 -0.169746 -0.001480
			 0.014837 -0.254550 -0.007688
			 0.024092 -0.204625 -0.002072
			 0.024092 -0.204625  0.008433
			 0.015558 -0.169404  0.014935
			 0.006000 -0.134183  0.015243
			-0.001394 -0.202231  0.012274
			-0.007569 -0.220015  0.007121
			-0.005088 -0.270283  0.006401
			-0.001935 -0.202403 -0.006578
			 0.006000 -0.304476 -0.006689
			-0.000673 -0.287377 -0.005027
			-0.007569 -0.220015 -0.000760
			 0.014927 -0.320036 -0.007798
			 0.019423 -0.271990 -0.000716
			 0.019423 -0.271990  0.007078
			 0.013754 -0.253356  0.012718
			 0.006000 -0.234718  0.014147
			-0.002747 -0.286523  0.013938
			-0.010632 -0.338330  0.008009
			-0.005088 -0.270283 -0.000040
			-0.010632 -0.338330 -0.001648
			 0.006641 -0.368004 -0.010593
			-0.003919 -0.353205 -0.009019
			 0.018624 -0.365173 -0.012346
			 0.021174 -0.340039 -0.001225
			 0.021174 -0.340039  0.007586
			 0.014566 -0.320720  0.013717
			 0.006000 -0.301399  0.014970
			-0.003198 -0.336963  0.014492
			-0.011216 -0.372526  0.008178
			-0.011216 -0.372526 -0.001817
			-0.007256 -0.381415 -0.013121
			 0.006000 -0.390306 -0.020945
			 0.020879 -0.396121 -0.015118
			 0.025842 -0.386886 -0.002580
			 0.025842 -0.386886  0.008942
			 0.017812 -0.378509  0.017707
			 0.006000 -0.370131  0.020452
			-0.008879 -0.388938  0.021478
			-0.012391 -0.377167  0.013907
			-0.023764 -0.407746 -0.005459
			-0.023764 -0.407746  0.011821
			-0.011944 -0.406550 -0.018889
			 0.006000 -0.405352 -0.023414
			 0.020788 -0.406891 -0.015007
			 0.031970 -0.398514 -0.004361
			 0.031970 -0.398514  0.010722
			 0.022051 -0.392871  0.022920
			 0.006000 -0.387229  0.027580
			-0.010321 -0.400052  0.023253
			-0.020846 -0.412876  0.010974
			-0.020846 -0.412876 -0.004614
			-0.009058 -0.414073 -0.015340
			 0.006000 -0.415269 -0.017380
			 0.018263 -0.405010 -0.011902
			 0.028176 -0.406719 -0.003259
			 0.028176 -0.406719  0.009620
			 0.019616 -0.403472  0.019927
			 0.006000 -0.400223  0.023742
			-0.005542 -0.406550  0.017376
			-0.009466 -0.412876  0.007671
			-0.009466 -0.412876 -0.001310
			-0.004190 -0.408089 -0.009352
			 0.006000 -0.403302 -0.013269
			 0.013214 -0.404839 -0.005692
			 0.016505 -0.409115  0.000131
			 0.016505 -0.409115  0.006230
			 0.013574 -0.409115  0.012495
			 0.006000 -0.409115  0.016339
			-0.002870 -0.410539  0.013503
			-0.006558 -0.412772  0.010193
			-0.006558 -0.412772  0.003417
			-0.001574 -0.410312 -0.006135
			 0.006000 -0.400565 -0.008882
			 0.006000 -0.403984  0.003181
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 0 2 
					0 4 1 
					5 4 0 
					3 5 0 
					1 6 2 
					4 7 1 
					1 7 6 
					8 3 2 
					9 8 2 
					10 9 2 
					11 10 2 
					12 11 2 
					13 12 2 
					6 13 2 
					14 5 3 
					8 14 3 
					4 15 7 
					16 15 4 
					5 16 4 
					17 16 5 
					14 17 5 
					6 18 13 
					7 18 6 
					15 19 7 
					7 19 18 
					20 14 8 
					9 20 8 
					21 20 9 
					10 21 9 
					10 11 21 
					22 23 11 
					12 22 11 
					11 23 21 
					24 22 12 
					13 24 12 
					18 24 13 
					25 17 14 
					20 25 14 
					15 26 19 
					27 26 15 
					16 27 15 
					28 27 16 
					17 28 16 
					29 28 17 
					25 29 17 
					18 30 24 
					19 30 18 
					26 31 19 
					19 31 30 
					32 25 20 
					21 32 20 
					33 32 21 
					23 33 21 
					22 33 23 
					22 34 33 
					35 34 22 
					24 35 22 
					30 35 24 
					36 29 25 
					32 36 25 
					26 37 31 
					26 38 37 
					27 38 26 
					27 39 38 
					28 39 27 
					28 40 39 
					29 40 28 
					41 40 29 
					36 41 29 
					30 42 35 
					31 42 30 
					37 43 31 
					31 43 42 
					44 36 32 
					33 44 32 
					45 44 33 
					34 45 33 
					46 45 34 
					35 46 34 
					42 46 35 
					47 41 36 
					44 47 36 
					37 48 43 
					37 49 48 
					38 49 37 
					38 50 49 
					39 50 38 
					39 51 50 
					40 51 39 
					52 51 40 
					41 52 40 
					53 52 41 
					47 53 41 
					42 54 46 
					43 54 42 
					48 55 43 
					43 55 54 
					56 47 44 
					45 56 44 
					57 56 45 
					46 57 45 
					54 57 46 
					58 53 47 
					56 58 47 
					48 59 55 
					60 59 48 
					49 60 48 
					61 60 49 
					50 61 49 
					62 61 50 
					51 62 50 
					63 62 51 
					52 63 51 
					63 52 53 
					64 65 53 
					58 64 53 
					65 63 53 
					54 66 57 
					55 66 54 
					59 67 55 
					55 67 66 
					68 58 56 
					57 68 56 
					66 68 57 
					69 64 58 
					68 69 58 
					59 70 67 
					71 70 59 
					60 71 59 
					72 71 60 
					61 72 60 
					73 72 61 
					62 73 61 
					74 73 62 
					63 74 62 
					75 74 63 
					65 75 63 
					65 64 76 
					76 64 69 
					77 75 65 
					65 76 77 
					66 78 68 
					67 78 66 
					70 79 67 
					67 79 78 
					78 69 68 
					80 81 69 
					78 80 69 
					81 76 69 
					70 82 79 
					83 82 70 
					71 83 70 
					84 83 71 
					72 84 71 
					85 84 72 
					73 85 72 
					86 85 73 
					74 86 73 
					87 86 74 
					75 87 74 
					88 87 75 
					77 88 75 
					77 76 81 
					89 77 81 
					90 88 77 
					89 90 77 
					79 80 78 
					82 91 79 
					79 91 80 
					80 89 81 
					80 92 89 
					91 92 80 
					82 93 91 
					94 93 82 
					83 94 82 
					95 94 83 
					84 95 83 
					96 95 84 
					85 96 84 
					97 96 85 
					86 97 85 
					98 97 86 
					87 98 86 
					99 98 87 
					88 99 87 
					100 99 88 
					90 100 88 
					92 90 89 
					101 100 90 
					92 101 90 
					93 102 91 
					91 102 92 
					102 101 92 
					93 103 102 
					104 103 93 
					94 104 93 
					105 104 94 
					95 105 94 
					106 105 95 
					96 106 95 
					107 106 96 
					97 107 96 
					108 107 97 
					98 108 97 
					109 98 99 
					98 109 108 
					110 111 99 
					100 110 99 
					109 99 111 
					112 110 100 
					101 112 100 
					102 113 101 
					113 112 101 
					103 113 102 
					103 114 113 
					115 114 103 
					104 115 103 
					116 115 104 
					105 116 104 
					117 116 105 
					106 117 105 
					118 117 106 
					107 118 106 
					119 118 107 
					108 119 107 
					120 119 108 
					111 120 108 
					109 111 108 
					110 121 111 
					122 121 110 
					112 122 110 
					121 120 111 
					113 123 112 
					123 122 112 
					114 123 113 
					114 124 123 
					125 124 114 
					115 125 114 
					126 125 115 
					116 126 115 
					127 126 116 
					117 127 116 
					128 127 117 
					118 128 117 
					129 128 118 
					119 129 118 
					130 129 119 
					120 130 119 
					131 130 120 
					121 131 120 
					132 131 121 
					122 132 121 
					123 133 122 
					133 132 122 
					124 133 123 
					124 134 133 
					135 134 124 
					125 135 124 
					136 135 125 
					126 136 125 
					137 136 126 
					127 137 126 
					138 137 127 
					128 138 127 
					139 138 128 
					129 139 128 
					140 139 129 
					130 140 129 
					141 140 130 
					131 141 130 
					142 141 131 
					132 142 131 
					133 143 132 
					143 142 132 
					134 143 133 
					134 144 143 
					135 144 134 
					136 144 135 
					137 144 136 
					138 144 137 
					139 144 138 
					140 144 139 
					141 144 140 
					142 144 141 
					143 144 142 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
