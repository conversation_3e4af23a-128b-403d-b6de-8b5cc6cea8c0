<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="142" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="216">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.682012  0.731341 -0.000002
			 0.999921  0.012550 -0.000000
			-0.538379  0.649305  0.537169
			-0.525183  0.850123 -0.038389
			-0.553571  0.647705 -0.523487
			 0.879861 -0.472545  0.050459
			 0.914355  0.090392  0.394695
			 0.800160 -0.599787  0.000000
			 0.914354  0.090393 -0.394697
			 0.876395 -0.477971 -0.058960
			 0.137071  0.984914  0.105627
			-0.802760  0.431551  0.411510
			-0.936045  0.164865 -0.310868
			-0.611976  0.413103 -0.674412
			-0.208412  0.390452 -0.896723
			-0.164872  0.433503 -0.885941
			-0.353986  0.934335 -0.041366
			-0.145933  0.490810  0.858958
			-0.181189  0.454212  0.872274
			-0.458103  0.308095  0.833798
			-0.898801  0.065080  0.433498
			 0.063441  0.923035 -0.379448
			-0.845804  0.382110 -0.372301
			 0.852290 -0.316670  0.416321
			-0.144329 -0.826459 -0.544182
			-0.831372 -0.063245 -0.552106
			 0.260347 -0.965515 -0.000007
			 0.005978 -0.833654  0.552254
			 0.653036 -0.432593 -0.621617
			-0.869930 -0.091017  0.484704
			-0.172415  0.818844  0.547511
			 0.170351  0.936122  0.307661
			-0.636376  0.697210 -0.330037
			-0.147083 -0.684594 -0.713931
			 0.246649 -0.525430 -0.814302
			-0.241785 -0.348356 -0.905643
			-0.572268  0.256892 -0.778791
			-0.987236 -0.153947  0.040823
			-0.527205  0.216918  0.821585
			-0.241771 -0.348349  0.905649
			 0.309169 -0.527312  0.791427
			 0.117619 -0.702313  0.702084
			-0.703023  0.599824  0.382060
			 0.040224  0.825369 -0.563160
			-0.109611  0.428639 -0.896802
			-0.512447  0.356628  0.781162
			-0.453010 -0.754295 -0.475206
			 0.670876 -0.713968 -0.200437
			-0.668406 -0.200092 -0.716378
			 0.346870 -0.937913 -0.000005
			-0.750762  0.430246 -0.501244
			-0.925113  0.332163 -0.183943
			-0.324201  0.202433 -0.924075
			-0.453373 -0.754201  0.475009
			 0.586567 -0.803814 -0.099103
			-0.727335 -0.243955  0.641459
			-0.750418  0.430215  0.501784
			-0.925113  0.332163  0.183943
			-0.721981  0.577895  0.380499
			-0.610646  0.738819  0.285056
			-0.564497  0.818426  0.107339
			-0.175817  0.902660 -0.392802
			 0.168079  0.951856  0.256358
			-0.661597  0.699234 -0.270853
			 0.220106  0.971606  0.086806
			 0.533412 -0.845855  0.000011
			-0.249452 -0.968387  0.000013
			-0.886551 -0.458005  0.065261
			-0.886551 -0.458005 -0.065261
			-0.736449  0.598237  0.315839
			-0.011381  0.997941  0.063120
			-0.031497  0.984715 -0.171304
			-0.630803  0.773456  0.062079
			-0.293091  0.854560  0.428747
			-0.812134  0.542920 -0.213722
			-0.791403  0.468454 -0.392724
			-0.733866 -0.660207  0.159895
			 0.661504 -0.634989 -0.398999
			 0.428284 -0.588252 -0.685953
			 0.209089 -0.383649 -0.899497
			 0.285236 -0.394549 -0.873483
			-0.009363  0.775361 -0.631449
			-0.281879  0.428318 -0.858538
			 0.622164  0.607225 -0.494155
			 0.523201  0.595183 -0.609932
			-0.247794 -0.514204 -0.821092
			-0.079301 -0.975169 -0.206777
			 0.109875  0.750203 -0.652015
			 0.038690  0.541153 -0.840034
			-0.117224 -0.981220 -0.153184
			 0.412531 -0.704978 -0.576910
			-0.570648  0.371845 -0.732183
			-0.729229 -0.678698 -0.087143
			 0.415642 -0.609146  0.675413
			 0.657988 -0.652266  0.376299
			 0.210627 -0.380779  0.900357
			 0.353044 -0.419846  0.836116
			-0.045326  0.755166  0.653965
			-0.410375  0.645738  0.643906
			 0.455288  0.794785  0.401285
			 0.534260  0.812275 -0.234041
			-0.247927 -0.513887  0.821251
			 0.047840 -0.983962  0.171846
			 0.281206  0.895111  0.345977
			 0.132858  0.589586  0.796704
			 0.553206 -0.763061 -0.334216
			 0.202248 -0.945196  0.256319
			-0.570399  0.371863  0.732368
			 0.146184  0.869150  0.472449
			-0.094860  0.934311  0.343604
			 0.131526  0.990645  0.036366
			-0.461909  0.754989  0.465437
			-0.908364 -0.414398  0.056114
			-0.364983  0.551997 -0.749725
			-0.344931  0.479814  0.806723
			-0.182824  0.964429  0.190922
			-0.081829  0.821390 -0.564467
			-0.033465  0.982068 -0.185532
			-0.497589  0.766581 -0.405905
			 0.954310 -0.296438  0.037650
			 0.818347 -0.566169  0.098795
			 0.698002  0.715827 -0.019633
			 0.538141  0.663375 -0.519940
			 0.590854  0.657489 -0.467547
			 0.534517  0.469273 -0.702905
			 0.307194  0.589770 -0.746862
			 0.426472  0.761366 -0.488307
			 0.411455  0.239824 -0.879312
			 0.264012  0.420032 -0.868258
			 0.957876 -0.276257  0.078454
			 0.695775  0.516811  0.498802
			 0.545791  0.647445  0.531909
			 0.629375  0.651750  0.423212
			 0.519205  0.337111  0.785355
			 0.656126  0.613267  0.439776
			 0.450070  0.848193 -0.279297
			 0.465660  0.294967  0.834359
			 0.649050  0.705490  0.284637
			 0.470146  0.796935 -0.379285
			-0.565328  0.755544  0.330995
			 0.275873  0.583262  0.764003
			 0.501738  0.800442  0.327950
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.009008  0.097740  0.000128
			-0.003098  0.106414  0.000128
			-0.002501  0.106676 -0.003561
			-0.009939  0.097487  0.000128
			-0.002501  0.106676  0.003817
			-0.004522  0.090511 -0.001808
			-0.003179  0.105718 -0.001265
			-0.004523  0.090509  0.000128
			-0.003179  0.105718  0.001522
			-0.004522  0.090511  0.002064
			 0.002113  0.110556 -0.010386
			-0.003443  0.106470 -0.004225
			-0.004757  0.100578 -0.010064
			-0.010909  0.092771 -0.004490
			-0.016433  0.091014 -0.003197
			-0.022020  0.090403 -0.003005
			-0.022315  0.092426  0.000128
			-0.022020  0.090403  0.003261
			-0.016433  0.091014  0.003453
			-0.010909  0.092771  0.004746
			-0.004757  0.100578  0.010320
			 0.002113  0.110556  0.010642
			-0.003443  0.106470  0.004482
			 0.000445  0.096415 -0.008382
			-0.010386  0.088215 -0.005690
			-0.005515  0.092887 -0.010451
			-0.010390  0.088208  0.000128
			-0.010386  0.088215  0.005946
			 0.000445  0.096415  0.008638
			-0.005515  0.092887  0.010707
			 0.007247  0.105851 -0.010453
			 0.005798  0.107156 -0.011699
			 0.000876  0.106103 -0.019497
			-0.012942  0.087820 -0.004900
			-0.018829  0.084166 -0.004765
			-0.022442  0.081851 -0.004639
			-0.027475  0.086452 -0.003677
			-0.027300  0.090804  0.000128
			-0.027475  0.086452  0.003933
			-0.022442  0.081851  0.004896
			-0.018829  0.084166  0.005021
			-0.012942  0.087820  0.005156
			 0.000876  0.106103  0.019753
			 0.005798  0.107156  0.011955
			 0.007247  0.105851  0.010709
			 0.011620  0.100649 -0.009312
			 0.009059  0.094876 -0.008371
			 0.009440  0.106221 -0.018251
			-0.003239  0.094165 -0.019665
			-0.012945  0.087814  0.000128
			-0.001300  0.100487 -0.020956
			-0.004274  0.093756 -0.014824
			 0.011620  0.100649  0.009569
			 0.009059  0.094876  0.008627
			 0.009440  0.106221  0.018508
			-0.003239  0.094165  0.019921
			-0.001300  0.100487  0.021212
			-0.004274  0.093756  0.015080
			 0.011847  0.110862 -0.008428
			 0.011683  0.112269 -0.009360
			 0.010204  0.107232 -0.011199
			 0.011533  0.107802 -0.014183
			 0.007356  0.110068 -0.016077
			 0.003538  0.101760 -0.020771
			 0.008679  0.111845 -0.021827
			-0.018831  0.084161  0.000128
			-0.022445  0.081846  0.000128
			-0.027134  0.083269 -0.003587
			-0.027134  0.083269  0.003843
			 0.003538  0.101760  0.021015
			 0.008679  0.111845  0.022072
			 0.007356  0.110068  0.016333
			 0.010204  0.107232  0.011455
			 0.011533  0.107802  0.014439
			 0.011683  0.112269  0.009617
			 0.011847  0.110862  0.008684
			 0.010496  0.093915 -0.003494
			 0.029364  0.094633 -0.007279
			 0.026170  0.095683 -0.012782
			 0.022477  0.095663 -0.014277
			 0.018544  0.100568 -0.015675
			 0.012099  0.105042 -0.015570
			 0.007048  0.109562 -0.023719
			 0.009865  0.100218 -0.020411
			 0.009090  0.107606 -0.025800
			 0.008453  0.099145 -0.027718
			 0.012110  0.097840 -0.024772
			 0.015210  0.105271 -0.016655
			 0.015753  0.104336 -0.020017
			 0.015266  0.097424 -0.022562
			 0.017506  0.102791 -0.019334
			 0.005327  0.102296 -0.024136
			 0.010496  0.093915  0.003750
			 0.026170  0.095683  0.013038
			 0.029364  0.094633  0.007535
			 0.022477  0.095663  0.014533
			 0.018544  0.100568  0.015931
			 0.012099  0.105042  0.015826
			 0.007048  0.109562  0.023964
			 0.009090  0.107606  0.026045
			 0.009865  0.100218  0.020656
			 0.008453  0.099145  0.027962
			 0.012110  0.097840  0.025016
			 0.016031  0.098667  0.019360
			 0.015210  0.105271  0.016911
			 0.017782  0.097150  0.018707
			 0.015266  0.097424  0.022806
			 0.005327  0.102296  0.024380
			 0.013654  0.115136 -0.009939
			 0.014426  0.110233 -0.004396
			 0.015152  0.114334 -0.012208
			 0.013473  0.109845 -0.002037
			 0.010545  0.093217  0.000128
			 0.014309  0.113410 -0.013387
			 0.014309  0.113410  0.013643
			 0.015152  0.114334  0.012464
			 0.013654  0.115136  0.010195
			 0.014426  0.110233  0.004652
			 0.013473  0.109845  0.002293
			 0.030648  0.094798 -0.001843
			 0.030614  0.094943  0.000128
			 0.028127  0.108602 -0.006414
			 0.024738  0.109446 -0.009989
			 0.022266  0.110609 -0.011366
			 0.018807  0.107466 -0.014712
			 0.017348  0.104116 -0.019839
			 0.012902  0.099904 -0.021025
			 0.010615  0.100553 -0.027170
			 0.014897  0.098650 -0.021810
			 0.030648  0.094798  0.002099
			 0.028127  0.108602  0.006670
			 0.024738  0.109446  0.010245
			 0.022266  0.110609  0.011622
			 0.018807  0.107466  0.014968
			 0.017625  0.098452  0.019190
			 0.012902  0.099904  0.021270
			 0.010615  0.100553  0.027415
			 0.014897  0.098650  0.022055
			 0.017407  0.113472 -0.011447
			 0.016281  0.109094  0.000128
			 0.021851  0.121234  0.000128
			 0.017407  0.113472  0.011703
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					2 3 0 
					4 1 0 
					0 3 4 
					5 6 1 
					7 5 1 
					1 6 2 
					1 8 9 
					1 9 7 
					4 8 1 
					10 2 6 
					10 11 2 
					2 12 13 3 
					12 2 11 
					3 14 15 16 
					13 14 3 
					16 17 18 3 
					3 18 19 
					3 19 20 4 
					8 4 21 
					4 22 21 
					22 4 20 
					23 6 5 
					23 5 24 25 
					24 5 7 26 
					10 6 23 
					26 7 9 27 
					28 8 21 
					9 8 28 
					29 27 9 28 
					30 31 10 23 
					10 32 12 11 
					10 31 32 
					13 12 24 33 
					12 25 24 
					12 32 25 
					14 13 33 34 
					14 34 35 
					15 14 35 
					16 15 36 37 
					35 36 15 
					37 38 17 16 
					39 18 17 
					17 38 39 
					39 40 18 
					40 41 19 18 
					41 27 20 19 
					22 20 42 21 
					27 29 20 
					29 42 20 
					28 21 43 44 
					42 43 21 
					30 23 45 
					45 23 46 
					47 46 23 
					47 23 25 48 
					49 33 24 26 
					25 32 50 
					25 50 51 
					25 51 48 
					26 27 41 49 
					52 28 44 
					53 28 52 
					28 53 54 
					55 29 28 54 
					56 42 29 
					57 56 29 
					55 57 29 
					58 59 30 45 
					59 60 30 
					60 31 30 
					61 62 31 60 
					62 32 31 
					32 63 50 
					32 62 64 63 
					65 34 33 49 
					66 35 34 65 
					66 37 35 
					67 35 37 
					67 36 35 
					36 67 37 
					39 37 66 
					37 68 38 
					37 39 68 
					39 38 68 
					65 40 39 66 
					49 41 40 65 
					56 69 42 
					69 70 71 42 
					43 42 71 
					72 43 71 73 
					44 43 72 
					52 44 74 75 
					44 72 74 
					58 45 46 
					58 46 76 
					77 76 46 78 
					78 46 79 
					79 46 80 
					46 47 80 
					81 47 62 61 
					47 64 62 
					47 82 64 
					47 83 84 82 
					47 48 85 
					47 85 86 
					83 47 87 88 
					47 81 87 
					89 90 47 
					90 80 47 
					47 86 89 
					51 50 48 
					50 91 48 
					91 85 48 
					63 91 50 
					53 52 75 
					92 53 75 
					93 53 92 94 
					95 53 93 
					96 53 95 
					96 54 53 
					73 71 54 97 
					71 70 54 
					70 98 54 
					98 99 100 54 
					101 55 54 
					102 101 54 
					103 104 54 100 
					104 97 54 
					54 105 106 
					54 96 105 
					106 102 54 
					55 56 57 
					55 107 56 
					55 101 107 
					56 107 69 
					108 58 109 
					110 59 58 108 
					109 58 111 
					111 58 76 112 
					110 113 60 59 
					113 61 60 
					81 61 113 
					82 63 64 
					91 63 82 
					70 69 98 
					98 69 107 
					74 72 114 115 
					72 73 114 
					114 73 97 
					116 75 74 115 
					117 75 116 
					118 75 117 
					112 92 75 118 
					76 77 119 120 112 
					77 121 119 
					78 122 121 77 
					79 122 78 
					80 123 122 79 
					80 124 123 
					80 90 125 124 
					87 81 113 
					82 84 91 
					126 84 83 
					126 83 88 
					84 127 91 
					126 127 84 
					91 127 85 
					86 85 127 
					89 86 127 128 
					125 88 87 124 
					124 87 113 
					128 126 88 125 
					90 89 128 125 
					112 120 129 94 92 
					94 130 131 93 
					93 131 95 
					129 130 94 
					95 131 132 96 
					132 133 96 
					133 134 105 96 
					114 97 104 
					107 99 98 
					100 99 135 
					107 136 99 
					99 136 135 
					103 100 135 
					101 136 107 
					136 101 102 
					137 136 102 106 
					133 104 103 134 
					134 103 135 137 
					114 104 133 
					134 137 106 105 
					123 138 108 109 
					138 110 108 
					123 109 122 
					122 109 121 
					109 139 140 121 
					109 111 139 
					124 113 110 138 
					139 111 112 
					112 118 139 
					141 115 114 133 
					116 115 141 
					117 116 141 132 
					131 117 132 
					130 117 131 
					130 140 139 117 
					139 118 117 
					121 140 120 119 
					129 120 140 130 
					123 124 138 
					127 126 128 
					141 133 132 
					137 135 136 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 40 43 47 50 54 57 61 64 67 70 73 77 81 84 88 91 94 98 102 106 109 113 116 119 123 126 129 133 136 140 143 146 149 153 157 161 164 167 171 174 177 180 183 187 191 194 197 200 204 207 210 213 217 220 223 226 230 233 236 240 243 246 250 254 258 261 264 267 270 273 276 279 282 286 290 293 297 300 304 307 311 314 317 320 324 327 330 333 337 340 343 347 350 353 357 360 363 366 369 372 375 378 381 384 387 391 394 397 400 404 407 410 414 417 420 424 427 430 433 436 439 442 445 448 451 455 458 462 466 469 472 475 478 481 484 488 491 494 498 501 504 508 513 516 520 523 527 530 534 537 540 543 546 549 552 555 558 562 566 569 573 577 582 586 589 592 596 599 603 606 609 612 615 618 621 624 627 631 635 639 642 646 650 653 656 659 663 666 670 673 676 680 683 687 690 693 697 700 704 708 711 714 717 720 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
