<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="212" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="424">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			 0.774588 -0.632466  0.000334
			 0.606978 -0.772585  0.186254
			 0.164198 -0.986427  0.000015
			 0.953679  0.073030  0.291827
			 0.998232 -0.059440  0.000221
			 0.607076 -0.772565 -0.186019
			 0.953761  0.073116 -0.291538
			 0.592688 -0.644613  0.482902
			 0.320673 -0.593241  0.738399
			-0.190553 -0.981545 -0.016097
			-0.307728 -0.951474 -0.000014
			-0.242406 -0.955411  0.168612
			-0.060506 -0.690041  0.721236
			 0.592685 -0.644619 -0.482899
			 0.320672 -0.593244 -0.738397
			-0.190550 -0.981546  0.016071
			-0.242400 -0.955413 -0.168609
			-0.060499 -0.690046 -0.721233
			 0.926066  0.377362  0.000174
			 0.850579  0.397591  0.344148
			 0.619628  0.255712  0.742073
			 0.850676  0.397651 -0.343837
			 0.619627  0.255713 -0.742074
			 0.202954  0.190747  0.960430
			-0.769041 -0.638072 -0.037951
			-0.930538 -0.366196 -0.000020
			-0.769041 -0.638073  0.037947
			-0.242492 -0.078019  0.967011
			-0.652872 -0.532873  0.538335
			-0.848961 -0.486566 -0.206202
			 0.202953  0.190746 -0.960430
			-0.652872 -0.532873 -0.538335
			-0.242492 -0.078019 -0.967011
			-0.848961 -0.486566  0.206202
			 0.998164  0.060571  0.000371
			 0.923584 -0.037319  0.381576
			 0.567695  0.305324  0.764526
			 0.923701 -0.037302 -0.381293
			 0.567695  0.305323 -0.764526
			 0.186350  0.191666  0.963607
			-0.951637 -0.147392 -0.269559
			-0.986177 -0.165696 -0.000082
			-0.951646 -0.147400  0.269525
			-0.148653  0.006164  0.988870
			-0.035886 -0.502194  0.864010
			-0.665939 -0.605432 -0.435864
			 0.186350  0.191665 -0.963607
			-0.035888 -0.502192 -0.864011
			-0.148653  0.006164 -0.988870
			-0.665938 -0.605432  0.435866
			 0.999703 -0.024377  0.000216
			 0.941336 -0.081063  0.327591
			 0.575384 -0.200901  0.792825
			 0.941393 -0.081046 -0.327431
			 0.575385 -0.200897 -0.792825
			 0.230762 -0.282581  0.931073
			-0.952070 -0.144230 -0.269740
			-0.409326 -0.004055 -0.912379
			-0.989176 -0.146731 -0.000159
			-0.952095 -0.144258  0.269639
			-0.409326 -0.004055  0.912379
			 0.024187 -0.299717  0.953721
			 0.138634  0.022362  0.990091
			 0.056109 -0.978957  0.196202
			-0.091152 -0.637967  0.764650
			 0.033629 -0.073329  0.996741
			 0.650378  0.224670 -0.725626
			 0.230764 -0.282577 -0.931074
			 0.138630  0.022358 -0.990092
			 0.024181 -0.299714 -0.953723
			-0.091160 -0.637970 -0.764646
			 0.056108 -0.978957 -0.196200
			 0.033629 -0.073328 -0.996741
			 0.650553  0.224768  0.725438
			 0.813205  0.581977  0.000183
			 0.667719  0.661183  0.342036
			 0.606418 -0.187581  0.772703
			 0.667758  0.661214 -0.341898
			 0.606421 -0.187578 -0.772702
			 0.197111 -0.323518  0.925464
			-0.583585  0.742541 -0.328728
			-0.241900 -0.018746 -0.970120
			-0.740744  0.671787 -0.000228
			-0.583638  0.742560  0.328589
			-0.241900 -0.018746  0.970120
			-0.073189 -0.220695  0.972593
			-0.199219  0.221671  0.954554
			-0.184223  0.687663  0.702269
			-0.114398 -0.575319  0.809890
			-0.214658 -0.638581  0.739010
			 0.483088 -0.116915  0.867731
			 0.876245 -0.163562  0.453258
			 0.053443  0.218558  0.974359
			-0.439201 -0.658865  0.610737
			 0.975858  0.218408 -0.000298
			 0.874941 -0.233019 -0.424477
			-0.616807  0.787114  0.000044
			-0.487432  0.492636 -0.720916
			 0.197115 -0.323517 -0.925463
			-0.199220  0.221661 -0.954557
			-0.073193 -0.220698 -0.972592
			-0.184223  0.687661 -0.702271
			-0.214656 -0.638581 -0.739011
			-0.114398 -0.575317 -0.809891
			 0.483087 -0.116916 -0.867731
			 0.876245 -0.163562 -0.453257
			-0.439201 -0.658865 -0.610737
			 0.053443  0.218558 -0.974359
			 0.874974 -0.233115  0.424355
			-0.487347  0.492689  0.720937
			-0.050421  0.998728  0.000000
			 0.401749  0.746407  0.530542
			 0.207936  0.516232  0.830823
			 0.401748  0.746409 -0.530540
			 0.207940  0.516233 -0.830822
			-0.156426  0.449842  0.879303
			-0.297463  0.949221 -0.102447
			-0.152413  0.924613  0.349085
			-0.716759  0.487393 -0.498704
			-0.219446  0.897887  0.381632
			-0.152413  0.924613 -0.349085
			-0.297464  0.949221  0.102447
			-0.716759  0.487393  0.498704
			-0.219436  0.897893 -0.381624
			 0.217224  0.633576  0.742560
			 0.213155  0.673869  0.707436
			 0.450558  0.358475  0.817614
			 0.326519 -0.650458  0.685777
			 0.504704  0.265698  0.821388
			-0.852057  0.113468  0.511002
			-0.022759 -0.003428  0.999735
			-0.670948 -0.674488 -0.308049
			-0.945984  0.087943 -0.312058
			 0.924268  0.029042  0.380637
			-0.023912  0.225504  0.973949
			 0.103237  0.008543  0.994620
			-0.790634 -0.534310  0.299016
			-0.975805  0.085983 -0.201027
			 0.216940 -0.976185 -0.000120
			 0.097191 -0.916143 -0.388890
			-0.916850  0.399232  0.000216
			-0.691184  0.625300  0.362304
			-0.871335  0.447560  0.201159
			-0.933473  0.284762 -0.218034
			-0.691094  0.625493 -0.362142
			-0.871291  0.447717 -0.200999
			-0.933461  0.284751  0.218102
			-0.814942  0.239380 -0.527794
			-0.156422  0.449849 -0.879300
			 0.217224  0.633576 -0.742560
			 0.213154  0.673870 -0.707436
			-0.022759 -0.003428 -0.999735
			-0.852057  0.113468 -0.511002
			 0.326520 -0.650456 -0.685778
			-0.670948 -0.674488  0.308049
			-0.945989  0.087935  0.312044
			 0.450556  0.358480 -0.817613
			 0.504703  0.265700 -0.821388
			 0.924268  0.029042 -0.380637
			-0.023912  0.225504 -0.973949
			-0.790634 -0.534310 -0.299016
			 0.103237  0.008543 -0.994620
			-0.975805  0.085983  0.201027
			 0.097321 -0.916148  0.388846
			-0.814943  0.239385  0.527791
			-0.881007  0.330349 -0.338668
			-0.656065  0.042355 -0.753515
			-0.881007  0.330350  0.338668
			-0.656065  0.042355  0.753515
			-0.486400 -0.366004  0.793383
			-0.511894 -0.814330  0.273553
			-0.891161  0.124786  0.436190
			-0.781075 -0.471208  0.409738
			-0.621708 -0.662988  0.417044
			-0.905951  0.339379  0.253131
			 0.557009 -0.779759  0.285862
			 0.313053 -0.892483 -0.324764
			 0.834047 -0.551694 -0.000083
			 0.313025 -0.892538  0.324640
			-0.798599  0.266579  0.539607
			-0.894157  0.447753 -0.000860
			-0.818183  0.094605  0.567121
			-0.798669  0.266565 -0.539511
			-0.817852  0.095229 -0.567493
			-0.905951  0.339379 -0.253131
			-0.781074 -0.471209 -0.409739
			-0.891161  0.124786 -0.436190
			-0.621709 -0.662988 -0.417044
			-0.486399 -0.365997 -0.793386
			-0.511894 -0.814330 -0.273553
			 0.557011 -0.779758 -0.285862
			-0.659822  0.036413  0.750539
			 0.107088 -0.942167  0.317575
			 0.052325 -0.964319 -0.259520
			 0.741621 -0.670819  0.000016
			 0.052326 -0.964318  0.259524
			 0.107068 -0.942153 -0.317622
			-0.766669  0.642041 -0.001369
			-0.658732  0.036096 -0.751512
			-0.136896  0.212529  0.967518
			-0.015552 -0.046701  0.998788
			 0.613300 -0.658278  0.436501
			 0.230825 -0.972995 -0.000440
			 0.613290 -0.658268 -0.436531
			-0.015551 -0.046701 -0.998788
			-0.835885  0.000808  0.548903
			-0.966072 -0.258270 -0.000957
			-0.136135  0.211771 -0.967791
			-0.834898  0.000493 -0.550404
			-0.227579 -0.691111  0.685983
			-0.528901 -0.848683 -0.000881
			-0.226875 -0.690794 -0.686536
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.034864  0.259886  0.000024
			-0.035641  0.260079  0.007987
			-0.048872  0.258112  0.000003
			-0.034792  0.264004  0.009581
			-0.034194  0.261446  0.000026
			-0.035641  0.260079 -0.007987
			-0.034792  0.264004 -0.009581
			-0.039933  0.260071  0.016272
			-0.046741  0.259436  0.020303
			-0.061297  0.255753  0.004802
			-0.061465  0.256004  0.000003
			-0.061701  0.255943  0.014136
			-0.057436  0.257765  0.020412
			-0.039933  0.260071 -0.016272
			-0.046741  0.259436 -0.020303
			-0.061297  0.255753 -0.004802
			-0.061701  0.255943 -0.014136
			-0.057436  0.257765 -0.020412
			-0.035421  0.265679  0.000026
			-0.036821  0.265964  0.010257
			-0.040187  0.264168  0.017288
			-0.036821  0.265964 -0.010257
			-0.040187  0.264168 -0.017288
			-0.048426  0.263919  0.020998
			-0.065602  0.258481  0.004942
			-0.065551  0.258366  0.000003
			-0.065602  0.258481 -0.004942
			-0.059369  0.262609  0.021147
			-0.064763  0.256158  0.015875
			-0.066735  0.259718  0.009393
			-0.048426  0.263919 -0.020998
			-0.064763  0.256158 -0.015875
			-0.059369  0.262609 -0.021147
			-0.066735  0.259718 -0.009393
			-0.036324  0.274739  0.000029
			-0.037632  0.273600  0.009900
			-0.042434  0.266632  0.016856
			-0.037632  0.273600 -0.009899
			-0.042434  0.266632 -0.016856
			-0.049034  0.267843  0.019102
			-0.066228  0.265249  0.006337
			-0.065726  0.265321  0.000005
			-0.066228  0.265249 -0.006337
			-0.059697  0.266061  0.020014
			-0.064878  0.264108  0.018925
			-0.068189  0.265602  0.009692
			-0.049034  0.267843 -0.019102
			-0.064878  0.264108 -0.018925
			-0.059697  0.266061 -0.020014
			-0.068189  0.265602 -0.009692
			-0.036215  0.278366  0.000030
			-0.036801  0.277949  0.010950
			-0.043667  0.273580  0.017504
			-0.036801  0.277949 -0.010950
			-0.043667  0.273580 -0.017504
			-0.048699  0.273280  0.019190
			-0.068574  0.274407  0.006548
			-0.070642  0.274635  0.010134
			-0.067843  0.274565  0.000007
			-0.068574  0.274407 -0.006548
			-0.070642  0.274635 -0.010134
			-0.059075  0.272773  0.020158
			-0.064047  0.273271  0.020550
			-0.071056  0.263199  0.017049
			-0.068473  0.266005  0.020902
			-0.070688  0.272571  0.024994
			-0.076918  0.266482  0.003720
			-0.048699  0.273280 -0.019190
			-0.064047  0.273271 -0.020550
			-0.059075  0.272773 -0.020158
			-0.068473  0.266005 -0.020902
			-0.071056  0.263199 -0.017049
			-0.070688  0.272571 -0.024994
			-0.076918  0.266482 -0.003720
			-0.036845  0.282359  0.000031
			-0.037672  0.282071  0.011151
			-0.042085  0.277154  0.018528
			-0.037672  0.282071 -0.011151
			-0.042085  0.277154 -0.018528
			-0.048299  0.276165  0.021218
			-0.069894  0.280907  0.008348
			-0.070484  0.279288  0.010881
			-0.068101  0.280213  0.000009
			-0.069894  0.280907 -0.008348
			-0.070484  0.279288 -0.010881
			-0.057282  0.276831  0.022241
			-0.065542  0.277137  0.019859
			-0.072111  0.277736  0.019906
			-0.080577  0.269466  0.023072
			-0.078275  0.265859  0.017146
			-0.074670  0.260652  0.024679
			-0.072789  0.260606  0.010508
			-0.075040  0.269006  0.023736
			-0.075977  0.262494  0.021578
			-0.076934  0.261484  0.000004
			-0.075026  0.252844  0.005103
			-0.085768  0.266672  0.000005
			-0.079861  0.271637  0.007700
			-0.048299  0.276165 -0.021218
			-0.065542  0.277137 -0.019859
			-0.057282  0.276831 -0.022241
			-0.072111  0.277736 -0.019906
			-0.078275  0.265859 -0.017146
			-0.080577  0.269466 -0.023072
			-0.074670  0.260652 -0.024679
			-0.072789  0.260606 -0.010508
			-0.075977  0.262494 -0.021578
			-0.075040  0.269006 -0.023736
			-0.075026  0.252844 -0.005103
			-0.079861  0.271637 -0.007700
			-0.052473  0.281286  0.000009
			-0.042781  0.281480  0.018784
			-0.049918  0.281086  0.022243
			-0.042781  0.281480 -0.018784
			-0.049918  0.281086 -0.022243
			-0.059077  0.279968  0.022379
			-0.079726  0.279966  0.015963
			-0.072007  0.279398  0.014072
			-0.079181  0.277578  0.010040
			-0.067905  0.280621  0.017079
			-0.072007  0.279398 -0.014072
			-0.079726  0.279966 -0.015963
			-0.079181  0.277578 -0.010040
			-0.067905  0.280621 -0.017079
			-0.077995  0.273651  0.021186
			-0.077357  0.276541  0.018352
			-0.092885  0.277831  0.034704
			-0.086084  0.270235  0.021043
			-0.081127  0.274729  0.021225
			-0.084608  0.257912  0.012613
			-0.080352  0.259791  0.015440
			-0.092406  0.269029  0.025270
			-0.084037  0.268052  0.012650
			-0.073090  0.258708  0.011971
			-0.083985  0.254334  0.017007
			-0.078975  0.268877  0.026253
			-0.080357  0.266743  0.023941
			-0.082213  0.272937  0.024279
			-0.078316  0.257108  0.000003
			-0.074631  0.251965  0.006687
			-0.091149  0.256859  0.000004
			-0.086447  0.263396  0.004946
			-0.082401  0.262881  0.008282
			-0.082856  0.266729  0.010379
			-0.086447  0.263396 -0.004946
			-0.082401  0.262881 -0.008282
			-0.082856  0.266729 -0.010379
			-0.081018  0.271980  0.011325
			-0.059077  0.279968 -0.022379
			-0.077995  0.273651 -0.021186
			-0.077357  0.276541 -0.018352
			-0.080352  0.259791 -0.015440
			-0.084608  0.257912 -0.012613
			-0.086084  0.270235 -0.021043
			-0.092406  0.269029 -0.025270
			-0.084037  0.268052 -0.012650
			-0.092885  0.277831 -0.034704
			-0.081127  0.274729 -0.021225
			-0.073090  0.258708 -0.011971
			-0.083985  0.254334 -0.017007
			-0.080357  0.266743 -0.023941
			-0.078975  0.268877 -0.026253
			-0.082213  0.272937 -0.024279
			-0.074631  0.251965 -0.006687
			-0.081018  0.271980 -0.011325
			-0.098177  0.274427  0.029089
			-0.085199  0.273158  0.013891
			-0.098177  0.274427 -0.029089
			-0.085199  0.273158 -0.013891
			-0.096171  0.273318  0.038453
			-0.097724  0.271095  0.029880
			-0.086728  0.255152  0.010239
			-0.086416  0.251143  0.009688
			-0.083193  0.248508  0.016765
			-0.084693  0.259761  0.008830
			-0.075563  0.249845  0.013289
			-0.077405  0.254472  0.005318
			-0.080376  0.252216  0.000001
			-0.077405  0.254472 -0.005318
			-0.089850  0.259237  0.004177
			-0.094940  0.251397 -0.000016
			-0.089035  0.255315  0.004063
			-0.089850  0.259237 -0.004177
			-0.089035  0.255315 -0.004063
			-0.084693  0.259761 -0.008830
			-0.086416  0.251143 -0.009688
			-0.086728  0.255152 -0.010239
			-0.083193  0.248508 -0.016765
			-0.096171  0.273318 -0.038453
			-0.097724  0.271095 -0.029880
			-0.075563  0.249845 -0.013289
			-0.088753  0.251507  0.004239
			-0.087544  0.249744  0.004970
			-0.082241  0.249133  0.007621
			-0.086845  0.245420 -0.000000
			-0.082241  0.249133 -0.007621
			-0.087544  0.249744 -0.004970
			-0.100866  0.243949 -0.000020
			-0.088753  0.251507 -0.004239
			-0.092957  0.244566  0.004612
			-0.089470  0.242894  0.004629
			-0.092634  0.236724  0.003595
			-0.094380  0.235970 -0.000003
			-0.092634  0.236724 -0.003595
			-0.089470  0.242894 -0.004629
			-0.101891  0.240727  0.004663
			-0.102610  0.239381 -0.000022
			-0.092957  0.244566 -0.004612
			-0.101891  0.240727 -0.004663
			-0.099717  0.237806  0.005430
			-0.098507  0.236281 -0.000021
			-0.099717  0.237806 -0.005430
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					3 1 0 
					3 0 4 
					2 5 0 
					0 5 6 
					4 0 6 
					1 7 2 
					7 1 3 
					7 8 2 
					9 10 2 
					2 11 9 
					12 11 2 
					2 8 12 
					2 13 5 
					2 14 13 
					2 10 15 
					15 16 2 
					2 16 17 
					17 14 2 
					18 19 3 
					18 3 4 
					20 3 19 
					7 3 20 
					4 6 18 
					6 5 13 
					6 21 18 
					21 6 22 
					22 6 13 
					8 7 20 
					8 20 23 
					12 8 23 
					10 9 24 
					11 24 9 
					10 24 25 
					26 15 10 
					25 26 10 
					27 28 11 
					27 11 12 
					11 28 29 
					11 29 24 
					12 23 27 
					22 13 14 
					30 22 14 
					30 14 17 
					15 26 16 
					16 31 32 
					17 16 32 
					33 31 16 
					26 33 16 
					32 30 17 
					34 19 18 
					18 21 34 
					34 35 19 
					36 19 35 
					20 19 36 
					23 20 36 
					21 37 34 
					37 21 38 
					38 21 22 
					38 22 30 
					23 36 39 
					27 23 39 
					40 25 24 
					40 24 29 
					25 40 41 
					41 42 25 
					26 25 42 
					33 26 42 
					27 39 43 
					43 28 27 
					43 44 28 
					29 28 45 
					28 44 45 
					40 29 45 
					46 38 30 
					46 30 32 
					31 47 48 
					32 31 48 
					49 31 33 
					49 47 31 
					48 46 32 
					49 33 42 
					50 35 34 
					34 37 50 
					50 51 35 
					52 35 51 
					36 35 52 
					39 36 52 
					37 53 50 
					53 37 54 
					54 37 38 
					54 38 46 
					39 52 55 
					43 39 55 
					41 40 56 
					40 57 56 
					40 45 57 
					41 56 58 
					59 42 41 
					58 59 41 
					59 60 42 
					60 49 42 
					43 55 61 
					61 44 43 
					61 62 44 
					63 44 64 
					65 64 44 
					65 44 62 
					44 63 45 
					57 45 66 
					66 45 63 
					67 54 46 
					67 46 48 
					47 68 69 
					48 47 69 
					70 47 71 
					47 70 72 
					68 47 72 
					49 71 47 
					69 67 48 
					73 49 60 
					71 49 73 
					74 51 50 
					50 53 74 
					74 75 51 
					76 51 75 
					52 51 76 
					55 52 76 
					53 77 74 
					77 53 78 
					78 53 54 
					78 54 67 
					55 76 79 
					61 55 79 
					58 56 80 
					56 57 81 
					56 81 80 
					66 81 57 
					58 80 82 
					83 59 58 
					82 83 58 
					84 60 59 
					83 84 59 
					60 84 73 
					61 79 85 
					85 62 61 
					85 86 62 
					87 65 62 
					87 62 86 
					88 89 63 
					88 63 90 
					63 64 65 
					63 65 90 
					63 89 91 
					63 91 66 
					65 92 93 
					65 93 90 
					87 92 65 
					94 66 95 
					66 94 96 
					66 96 97 
					91 95 66 
					66 97 81 
					98 78 67 
					98 67 69 
					68 99 100 
					69 68 100 
					68 72 101 
					99 68 101 
					100 98 69 
					72 70 71 
					71 102 103 
					104 71 103 
					104 72 71 
					105 102 71 
					73 105 71 
					106 107 72 
					104 106 72 
					72 107 101 
					108 73 94 
					96 94 73 
					109 96 73 
					73 108 105 
					84 109 73 
					75 74 110 
					110 74 77 
					76 75 111 
					111 75 110 
					112 79 76 
					112 76 111 
					113 77 78 
					110 77 113 
					78 98 114 
					113 78 114 
					115 85 79 
					115 79 112 
					116 117 80 
					80 118 116 
					80 97 118 
					97 80 81 
					82 80 110 
					80 119 110 
					117 119 80 
					110 83 82 
					83 120 121 
					121 122 83 
					122 109 83 
					84 83 109 
					110 123 83 
					83 123 120 
					115 86 85 
					119 87 86 
					115 119 86 
					119 117 87 
					87 124 92 
					124 87 125 
					125 87 117 
					126 127 88 
					127 89 88 
					88 90 124 
					128 88 124 
					126 88 128 
					129 130 89 
					127 131 89 
					89 130 91 
					89 132 129 
					89 131 132 
					90 93 124 
					95 91 133 
					134 133 91 
					134 91 130 
					124 135 92 
					135 93 92 
					135 136 93 
					136 137 93 
					93 137 124 
					94 95 138 
					138 108 94 
					138 95 139 
					95 133 139 
					140 141 96 
					96 141 142 
					96 142 143 
					143 97 96 
					96 144 140 
					145 144 96 
					146 145 96 
					96 109 146 
					143 147 97 
					97 147 118 
					98 100 148 
					114 98 148 
					100 99 148 
					99 101 123 
					99 123 148 
					101 120 123 
					107 149 101 
					150 101 149 
					120 101 150 
					102 151 152 
					103 102 153 
					102 154 153 
					105 151 102 
					152 155 102 
					155 154 102 
					103 153 156 
					149 104 103 
					149 103 157 
					157 103 156 
					149 106 104 
					158 105 108 
					105 158 159 
					151 105 159 
					106 160 161 
					107 106 161 
					106 162 160 
					149 162 106 
					107 161 149 
					163 108 138 
					163 158 108 
					109 164 146 
					122 164 109 
					112 111 110 
					115 112 110 
					119 115 110 
					110 113 114 
					110 114 148 
					110 148 123 
					117 116 125 
					126 125 116 
					165 126 116 
					165 116 166 
					116 147 166 
					147 116 118 
					150 121 120 
					121 150 156 
					121 156 167 
					168 121 167 
					168 164 121 
					122 121 164 
					124 137 135 
					124 125 128 
					126 128 125 
					126 169 127 
					169 126 165 
					127 169 170 
					131 127 170 
					134 130 129 
					129 171 172 
					129 172 173 
					129 173 134 
					171 129 174 
					132 174 129 
					131 170 165 
					131 165 166 
					147 132 131 
					147 131 166 
					143 132 147 
					174 132 142 
					142 132 143 
					134 175 133 
					133 175 139 
					134 173 175 
					135 137 136 
					138 139 176 
					138 176 177 
					178 163 138 
					177 178 138 
					139 175 176 
					179 141 140 
					140 180 181 
					140 181 179 
					140 144 182 
					183 180 140 
					182 183 140 
					179 142 141 
					179 174 142 
					144 145 182 
					145 184 182 
					145 155 184 
					146 155 145 
					164 155 146 
					161 162 149 
					157 150 149 
					150 157 156 
					152 151 159 
					185 186 152 
					187 185 152 
					159 187 152 
					184 152 186 
					152 184 155 
					153 188 156 
					189 188 153 
					189 153 154 
					167 189 154 
					168 167 154 
					154 155 164 
					168 154 164 
					167 156 188 
					158 190 159 
					163 190 158 
					190 187 159 
					160 162 161 
					178 190 163 
					169 165 170 
					189 167 188 
					181 171 174 
					191 172 171 
					191 171 181 
					192 193 172 
					192 172 191 
					193 173 172 
					193 175 173 
					181 174 179 
					176 175 193 
					176 193 192 
					176 192 194 
					177 176 194 
					194 178 177 
					195 190 178 
					196 195 178 
					194 196 178 
					180 197 191 
					180 191 181 
					198 197 180 
					183 198 180 
					182 184 183 
					184 186 183 
					183 186 198 
					186 185 198 
					185 195 196 
					198 185 196 
					185 187 195 
					187 190 195 
					197 199 191 
					200 192 191 
					200 191 199 
					201 194 192 
					201 192 200 
					194 201 202 
					202 203 194 
					196 194 203 
					198 196 204 
					204 196 203 
					197 205 199 
					206 205 197 
					207 208 197 
					198 207 197 
					197 208 206 
					207 198 204 
					201 200 199 
					201 199 209 
					205 209 199 
					202 201 209 
					202 209 210 
					211 203 202 
					210 211 202 
					207 204 203 
					211 207 203 
					210 209 205 
					210 205 206 
					206 208 210 
					207 211 208 
					208 211 210 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 1155 1158 1161 1164 1167 1170 1173 1176 1179 1182 1185 1188 1191 1194 1197 1200 1203 1206 1209 1212 1215 1218 1221 1224 1227 1230 1233 1236 1239 1242 1245 1248 1251 1254 1257 1260 1263 1266 1269 1272 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
