<?xml version="1.0"?>
<VTKFile type="PolyData" version="0.1" byte_order="LittleEndian" compressor="vtkZLibDataCompressor">
	<PolyData>
		<Piece NumberOfPoints="194" NumberOfVerts="0" NumberOfLines="0" NumberOfStrips="0" NumberOfPolys="384">
		<PointData Normals="Normals">
		<DataArray type="Float32" Name="Normals" NumberOfComponents="3" format="ascii">
			-0.363482 -0.592931 -0.718550
			-0.002964 -0.903032 -0.429563
			-0.302980 -0.542084 -0.783803
			-0.024823 -0.402712 -0.914990
			 0.193508 -0.761705 -0.618353
			-0.305790  0.351939 -0.884665
			 0.007651  0.278694 -0.960350
			-0.314158 -0.929674 -0.192384
			-0.031975 -0.898751 -0.437291
			-0.904251  0.072850 -0.420741
			-0.718065 -0.567336 -0.403128
			 0.570432  0.192271 -0.798523
			 0.453869 -0.773634 -0.442147
			 0.833942  0.225378 -0.503732
			 0.566003  0.606149 -0.558770
			 0.146946  0.559798 -0.815496
			-0.369143  0.505762 -0.779704
			-0.868244 -0.425748  0.254737
			-0.912182 -0.260298  0.316495
			 0.138799 -0.921935 -0.361623
			-0.229127 -0.971880 -0.054323
			-0.654268 -0.745887  0.124847
			 0.525184 -0.780072 -0.340101
			 0.303639 -0.857076 -0.416202
			 0.078021 -0.942076 -0.326198
			-0.413216 -0.888180 -0.200971
			-0.385113 -0.922854  0.005287
			-0.719322  0.392949 -0.572859
			-0.933890  0.112106  0.339531
			 0.733266 -0.633622 -0.246666
			 0.930161  0.028086 -0.366076
			 0.782731  0.496100 -0.375789
			 0.505581  0.351832 -0.787783
			 0.809735  0.268900 -0.521558
			-0.007354  0.418306 -0.908276
			-0.947946 -0.255798  0.189646
			-0.753472 -0.643317  0.135731
			-0.785837 -0.496256  0.369038
			-0.306204 -0.939725  0.152170
			-0.624249 -0.704419  0.337796
			 0.852703 -0.521487  0.030813
			 0.879084 -0.476545  0.010785
			-0.112657 -0.977906  0.176092
			 0.457618 -0.763240  0.456124
			-0.624493 -0.490377  0.607897
			-0.614175 -0.556975  0.559077
			-0.566401 -0.396983  0.722215
			 0.080596  0.152459 -0.985018
			-0.923545  0.223526 -0.311611
			-0.998526  0.050441 -0.020037
			 0.976895 -0.156154 -0.145921
			 0.907894 -0.417637  0.036166
			 0.914540  0.320785 -0.246401
			 0.759977  0.102006 -0.641896
			 0.838229  0.218955  0.499430
			-0.907132 -0.174173  0.383112
			 0.711973 -0.222458  0.666038
			 0.854858 -0.211281  0.473896
			-0.348682 -0.440068  0.827503
			 0.126554 -0.265002  0.955907
			-0.518216 -0.162144  0.839739
			-0.597228 -0.042999  0.800918
			-0.154282  0.065315  0.985866
			-0.705236  0.087235 -0.703585
			 0.161624  0.004669 -0.986841
			 0.621169 -0.031288 -0.783052
			-0.989055  0.132537 -0.064840
			 0.967647  0.068802  0.242748
			 0.986240 -0.003201 -0.165290
			 0.703402  0.095260  0.704380
			 0.147880  0.137756  0.979364
			-0.773629  0.144394  0.616967
			-0.305872  0.107263  0.946011
			-0.537723 -0.008178 -0.843082
			-0.987148  0.096196  0.127610
			 0.980327 -0.075211 -0.182491
			 0.865072  0.021292  0.501196
			 0.249008  0.061083  0.966573
			-0.736534  0.110366  0.667335
			-0.240606  0.068609  0.968195
			-0.187014 -0.166390 -0.968163
			-0.882524  0.084540  0.462606
			-0.937854  0.089990  0.335159
			-0.676970  0.093128  0.730095
			 0.899486 -0.094292 -0.426654
			 0.929598  0.015102  0.368266
			 0.419478  0.057885  0.905918
			 0.652917  0.060983  0.754970
			-0.291916  0.083143  0.952823
			 0.179157 -0.164077 -0.970042
			-0.823780 -0.027363 -0.566249
			-0.533655  0.120727  0.837041
			-0.173944  0.202731  0.963661
			-0.616751  0.167759  0.769074
			-0.980084 -0.063360 -0.188204
			-0.345739 -0.170359 -0.922736
			-0.083206  0.167683  0.982323
			 0.871696 -0.067005 -0.485444
			 0.892076  0.133834  0.431611
			 0.478534  0.184248  0.858521
			-0.077170  0.115630  0.990290
			 0.439728  0.219980  0.870774
			 0.164688  0.212309  0.963225
			-0.402931  0.156278  0.901789
			-0.863947  0.073944  0.498125
			-0.377527 -0.078365 -0.922677
			-0.792345 -0.012996 -0.609935
			-0.999777 -0.004571  0.020631
			 0.785947 -0.192996 -0.587401
			 0.223339 -0.312312 -0.923353
			 0.927970  0.072754  0.365484
			 0.699067  0.014885  0.714901
			 0.035512 -0.007966  0.999337
			-0.823872 -0.015484  0.566565
			-0.440465 -0.082136  0.894005
			-0.500323  0.062476 -0.863582
			-0.007604 -0.021677 -0.999736
			 0.170283 -0.225780 -0.959180
			 0.247887 -0.062650 -0.966761
			-0.836258 -0.035208 -0.547205
			-0.997812 -0.065799  0.006470
			 0.925408 -0.140294 -0.352048
			 0.626470 -0.304973 -0.717305
			 0.575895 -0.288643 -0.764873
			 0.965647  0.004058 -0.259826
			 0.997038  0.041247  0.064913
			 0.957188 -0.288888  0.018312
			 0.809657 -0.452706  0.373513
			 0.491605 -0.469750  0.733253
			 0.233742 -0.349893  0.907160
			-0.249626 -0.439066  0.863081
			-0.807842 -0.293628  0.511051
			-0.514144 -0.124107 -0.848677
			 0.557909  0.021212 -0.829631
			 0.098860 -0.418088 -0.903011
			 0.623829 -0.142468 -0.768466
			 0.713116 -0.126501 -0.689538
			 0.686142  0.041871 -0.726262
			-0.859807 -0.099413 -0.500849
			-0.957923 -0.287008 -0.003219
			 0.875858  0.084005 -0.475202
			 0.883970 -0.040933 -0.465748
			 0.840496 -0.362005 -0.403137
			 0.742683 -0.523406 -0.417694
			 0.693798 -0.716275  0.074800
			 0.808608 -0.466100  0.359032
			 0.686788 -0.341465  0.641657
			 0.130243 -0.427387  0.894638
			 0.432288 -0.120454  0.893654
			-0.712867 -0.227455  0.663389
			-0.216521 -0.291191  0.931840
			-0.960977 -0.202253  0.188725
			-0.520036 -0.357862 -0.775563
			 0.424793 -0.532439 -0.732161
			 0.602214 -0.529596 -0.597383
			-0.053260 -0.405639 -0.912480
			-0.860522 -0.295007 -0.415297
			-0.934336 -0.300052 -0.192315
			 0.363053 -0.154768 -0.918825
			 0.681014  0.306340 -0.665113
			 0.890942 -0.354089 -0.284329
			 0.940065 -0.335548  0.060709
			 0.919159  0.096795  0.381808
			 0.687492  0.167105  0.706704
			 0.127282  0.190221  0.973455
			 0.384897  0.559743  0.733854
			-0.555866  0.296884  0.776449
			-0.302794  0.151007  0.941017
			-0.823581  0.155973  0.545331
			-0.936077  0.081945  0.342119
			-0.877618  0.474629  0.067190
			-0.966602  0.053718 -0.250589
			-0.489832 -0.337555 -0.803817
			-0.784919 -0.254066 -0.565113
			-0.461973 -0.122156 -0.878441
			-0.069994 -0.121499 -0.990121
			 0.153705  0.437866 -0.885804
			 0.755046  0.649349 -0.090834
			 0.172347  0.960320 -0.219277
			 0.785845  0.555359  0.272074
			 0.630357  0.635556  0.445779
			-0.070430  0.715180  0.695382
			 0.185951  0.976058  0.112839
			-0.648841  0.628074  0.429568
			-0.345820  0.733311  0.585375
			-0.645576  0.623062 -0.441617
			-0.242668  0.969764 -0.025872
			-0.782191  0.007638 -0.622992
			-0.356650  0.317925 -0.878479
			-0.042656  0.933488 -0.356064
			 0.284110  0.955848  0.075072
			 0.081354  0.996266 -0.028896
			-0.052081  0.992455  0.111001
			-0.100468  0.979748 -0.173206
		</DataArray>
	</PointData>
	<Points>
		<DataArray type="Float32" NumberOfComponents="3" format="ascii">
			-0.012807 -0.227620  0.011017
			-0.013906 -0.229378  0.014953
			-0.018792 -0.229397  0.015465
			-0.007831 -0.227726  0.008202
			-0.003959 -0.228413  0.009251
			-0.010300 -0.222893  0.008970
			-0.006925 -0.224190  0.007649
			-0.016840 -0.233137  0.020368
			-0.003570 -0.232487  0.027583
			-0.020873 -0.226810  0.016046
			-0.019980 -0.229816  0.016064
			-0.003494 -0.225922  0.009132
			-0.001507 -0.229580  0.011938
			-0.000459 -0.227416  0.012410
			-0.006535 -0.220379  0.013224
			-0.011412 -0.218668  0.012810
			-0.012698 -0.218634  0.013016
			-0.016802 -0.231600  0.023950
			-0.020038 -0.229244  0.017451
			-0.008889 -0.234115  0.027961
			-0.014345 -0.233849  0.023899
			-0.015652 -0.232957  0.023784
			 0.002201 -0.233835  0.027081
			 0.001082 -0.236946  0.031757
			 0.001192 -0.232679  0.024164
			-0.004699 -0.237392  0.032661
			-0.007458 -0.235616  0.031222
			-0.016402 -0.215415  0.017506
			-0.018751 -0.224172  0.019161
			 0.002611 -0.232528  0.025187
			 0.002632 -0.228528  0.019733
			 0.000960 -0.224825  0.023096
			-0.006479 -0.214361  0.016863
			-0.005214 -0.219404  0.015905
			-0.010012 -0.214441  0.015198
			-0.016276 -0.231489  0.026161
			-0.015604 -0.232902  0.025442
			-0.015321 -0.232827  0.026630
			-0.010171 -0.234620  0.029519
			-0.014596 -0.233642  0.026839
			 0.001925 -0.236103  0.033050
			 0.003622 -0.231543  0.031412
			-0.001760 -0.238496  0.033939
			 0.000171 -0.237757  0.034630
			-0.007060 -0.234733  0.032732
			-0.004768 -0.236899  0.033883
			-0.012831 -0.230181  0.031432
			-0.010531 -0.208876  0.016641
			-0.014505 -0.209184  0.018183
			-0.016791 -0.226552  0.025854
			 0.004889 -0.229356  0.027259
			 0.004817 -0.230221  0.030299
			 0.004709 -0.225450  0.029131
			-0.003106 -0.209231  0.021412
			 0.001638 -0.216138  0.032050
			-0.017714 -0.225712  0.028610
			 0.001706 -0.235556  0.035160
			 0.004170 -0.230514  0.033350
			-0.002656 -0.236867  0.035771
			-0.001335 -0.235448  0.036616
			-0.004100 -0.232077  0.035333
			-0.016491 -0.225106  0.030221
			-0.002620 -0.230562  0.036240
			-0.011755 -0.194929  0.017976
			-0.007865 -0.187825  0.017667
			-0.003824 -0.184567  0.019537
			-0.015071 -0.208991  0.024859
			 0.004859 -0.227537  0.032199
			-0.000879 -0.187407  0.025051
			-0.001127 -0.194182  0.029375
			-0.004849 -0.208294  0.032003
			-0.016345 -0.222652  0.029893
			-0.011011 -0.196621  0.030061
			-0.008743 -0.146781  0.016471
			-0.013017 -0.187801  0.024766
			 0.001280 -0.141554  0.021405
			-0.000488 -0.170097  0.026568
			-0.004050 -0.183105  0.030044
			-0.010782 -0.180135  0.027759
			-0.006709 -0.169985  0.029394
			-0.006915 -0.108148  0.009187
			-0.009404 -0.150746  0.025524
			-0.007713 -0.101541  0.018654
			-0.008273 -0.154829  0.027435
			 0.004237 -0.074194  0.009913
			 0.002744 -0.112028  0.020382
			-0.002631 -0.136804  0.027173
			 0.000514 -0.111550  0.023618
			-0.005385 -0.145584  0.028053
			-0.005447 -0.077090  0.004369
			-0.007081 -0.077017  0.004865
			-0.007344 -0.136928  0.026499
			-0.002792 -0.075317  0.016380
			-0.004357 -0.039016  0.009127
			-0.006724 -0.046001  0.002282
			-0.005179 -0.061822  0.002923
			-0.001008 -0.106362  0.023414
			 0.003460 -0.066548  0.006608
			 0.003693 -0.075809  0.014133
			 0.001330 -0.084748  0.018840
			-0.003914 -0.131994  0.026995
			 0.001031 -0.075384  0.016241
			-0.000632 -0.032441  0.008162
			-0.003284 -0.023968  0.005203
			-0.006506 -0.030551  0.004339
			-0.003637 -0.027095 -0.004824
			-0.006663 -0.026528 -0.002639
			-0.007226 -0.027171  0.000762
			 0.003416 -0.048575  0.002369
			-0.000447 -0.043640 -0.001803
			 0.002810 -0.061447  0.011018
			 0.002935 -0.018762  0.003659
			-0.000056 -0.018105  0.004702
			-0.006578 -0.018017  0.002388
			-0.004396 -0.013991  0.004581
			-0.005425 -0.023554 -0.003148
			-0.001207 -0.024063 -0.004948
			 0.000868 -0.035545 -0.003937
			 0.001047 -0.031617 -0.004724
			-0.006546 -0.018377 -0.002195
			-0.007401 -0.016017  0.000166
			 0.003479 -0.044503  0.001388
			 0.002715 -0.044268  0.000345
			 0.002340 -0.040314 -0.002064
			 0.004772 -0.024184 -0.001531
			 0.004433 -0.018747  0.000697
			 0.004551 -0.010046 -0.000245
			 0.003862 -0.010011  0.002737
			 0.002312 -0.009756  0.004922
			 0.000061 -0.009267  0.006088
			-0.002793 -0.009485  0.006204
			-0.007544 -0.009581  0.002727
			-0.004422 -0.019166 -0.004021
			 0.002231 -0.019494 -0.003887
			-0.000838 -0.011546 -0.006633
			 0.002006 -0.015365 -0.004294
			 0.003190 -0.031854 -0.003510
			 0.003630 -0.024232 -0.003397
			-0.006674 -0.013473 -0.003189
			-0.007898 -0.009528  0.001405
			 0.003888 -0.019426 -0.002119
			 0.003811 -0.014798 -0.002163
			 0.003255 -0.010964 -0.003995
			 0.005935 -0.007421 -0.006119
			 0.006295 -0.007474 -0.000012
			 0.007758 -0.005096  0.004224
			 0.005232 -0.004721  0.007624
			 0.000527 -0.004327  0.010148
			 0.003066 -0.004451  0.009379
			-0.008384 -0.004303  0.006957
			-0.003295 -0.004456  0.009921
			-0.010383 -0.004497  0.002311
			-0.003923 -0.011275 -0.005973
			 0.003492 -0.009098 -0.007208
			 0.002209 -0.011468 -0.005278
			-0.000624 -0.009400 -0.008427
			-0.007047 -0.009992 -0.003084
			-0.010446 -0.004622 -0.000589
			 0.003387 -0.005394 -0.008432
			 0.005580 -0.000392 -0.007261
			 0.007822 -0.006408 -0.003516
			 0.008658 -0.005775 -0.000174
			 0.007988 -0.002455  0.004045
			 0.005633 -0.001649  0.007309
			-0.001340 -0.001469  0.010182
			 0.003438  0.000696  0.007988
			-0.006608 -0.000038  0.007873
			-0.003223 -0.001531  0.010182
			-0.009383 -0.001136  0.005472
			-0.009975 -0.001139  0.003982
			-0.010170  0.001355  0.001946
			-0.010529 -0.001432 -0.001122
			-0.003776 -0.008705 -0.007860
			-0.008142 -0.006249 -0.004253
			-0.003608 -0.003195 -0.009032
			-0.001906 -0.004884 -0.009199
			 0.000338 -0.000568 -0.009643
			 0.007954  0.000446 -0.000758
			 0.004665  0.000666 -0.006121
			 0.007756 -0.000034  0.003134
			 0.005622  0.000272  0.006268
			-0.002526  0.000936  0.009179
			 0.002673  0.001419  0.006609
			-0.008684  0.001556  0.004980
			-0.006185  0.001340  0.007391
			-0.008504  0.001057 -0.004229
			-0.008497  0.002203  0.001495
			-0.009200 -0.001812 -0.004161
			-0.003293 -0.000065 -0.009131
			-0.002797  0.001095 -0.007738
			 0.007013  0.000477  0.003040
			 0.001661 -0.000403 -0.001182
			-0.007280  0.002313  0.004042
			-0.007127  0.002063 -0.003676
		</DataArray>
	</Points>
			<Polys>
				<DataArray type="Int32" Name="connectivity" format="ascii">
					0 1 2 
					0 3 4 
					5 6 0 
					3 0 6 
					2 5 0 
					1 0 4 
					2 1 7 
					1 4 8 
					8 7 1 
					9 2 10 
					7 10 2 
					2 9 5 
					4 3 11 
					11 3 6 
					8 4 12 
					12 4 13 
					11 13 4 
					6 5 14 
					5 15 14 
					5 16 15 
					16 5 9 
					6 14 11 
					7 17 18 
					7 18 10 
					7 19 20 
					7 8 19 
					7 20 21 
					7 21 17 
					8 22 23 
					12 24 8 
					8 24 22 
					25 8 23 
					26 8 25 
					26 19 8 
					16 9 27 
					27 9 28 
					28 9 18 
					9 10 18 
					11 14 13 
					24 12 29 
					13 30 12 
					29 12 30 
					31 13 14 
					31 30 13 
					15 32 14 
					14 32 33 
					31 14 33 
					15 16 34 
					15 34 32 
					34 16 27 
					35 28 17 
					17 21 36 
					35 17 37 
					17 36 37 
					18 17 28 
					38 19 26 
					19 38 20 
					21 20 39 
					39 20 38 
					21 39 36 
					23 22 40 
					24 29 22 
					22 29 41 
					40 22 41 
					42 25 23 
					40 43 23 
					23 43 42 
					26 25 44 
					45 44 25 
					25 42 45 
					46 38 26 
					44 46 26 
					47 27 48 
					27 47 34 
					27 28 48 
					49 48 28 
					35 49 28 
					50 29 30 
					41 29 51 
					50 51 29 
					50 30 52 
					52 30 31 
					33 52 31 
					53 33 32 
					53 32 47 
					34 47 32 
					54 33 53 
					33 54 52 
					49 35 55 
					35 37 55 
					37 36 39 
					37 39 55 
					38 46 39 
					39 46 55 
					40 41 56 
					56 43 40 
					56 41 57 
					41 51 57 
					58 45 42 
					43 58 42 
					58 43 59 
					56 59 43 
					46 44 60 
					44 45 60 
					60 45 58 
					61 46 62 
					60 62 46 
					46 61 55 
					63 47 48 
					64 47 63 
					65 47 64 
					65 53 47 
					66 48 49 
					48 66 63 
					49 55 66 
					50 52 67 
					50 67 51 
					57 51 67 
					54 67 52 
					65 68 53 
					54 53 68 
					54 69 70 
					68 69 54 
					57 67 54 
					62 54 70 
					56 54 62 
					56 57 54 
					55 71 66 
					61 71 55 
					62 59 56 
					60 58 62 
					59 62 58 
					62 71 61 
					70 72 62 
					72 71 62 
					63 73 64 
					63 66 74 
					73 63 74 
					64 73 65 
					75 65 73 
					65 75 68 
					74 66 71 
					76 68 75 
					76 69 68 
					69 76 77 
					77 70 69 
					70 77 72 
					71 72 78 
					74 71 78 
					77 79 72 
					78 72 79 
					73 80 75 
					81 73 74 
					73 81 82 
					73 82 80 
					78 83 74 
					83 81 74 
					75 80 84 
					85 75 84 
					76 75 86 
					87 86 75 
					75 85 87 
					86 77 76 
					77 86 79 
					83 78 79 
					79 88 83 
					88 79 86 
					80 89 84 
					90 80 82 
					89 80 90 
					88 91 81 
					83 88 81 
					82 81 91 
					82 92 93 
					82 94 95 
					82 91 96 
					92 82 96 
					90 82 95 
					82 93 94 
					89 97 84 
					98 85 84 
					84 97 98 
					85 99 87 
					85 98 99 
					86 96 100 
					100 88 86 
					87 96 86 
					87 99 96 
					91 88 100 
					89 90 95 
					97 89 95 
					96 91 100 
					92 96 99 
					99 101 92 
					101 102 92 
					102 93 92 
					103 93 102 
					93 104 94 
					104 93 103 
					94 105 95 
					106 105 94 
					107 94 104 
					106 94 107 
					108 97 95 
					109 95 105 
					108 95 109 
					110 98 97 
					97 108 110 
					101 99 98 
					101 98 110 
					102 101 110 
					110 111 102 
					112 102 111 
					102 112 103 
					113 104 103 
					114 103 112 
					103 114 113 
					113 107 104 
					105 106 115 
					105 115 116 
					109 105 117 
					118 117 105 
					116 118 105 
					119 115 106 
					119 106 107 
					113 120 107 
					107 120 119 
					110 108 121 
					122 108 109 
					121 108 122 
					109 117 123 
					123 122 109 
					124 110 121 
					110 124 125 
					125 111 110 
					126 111 125 
					127 111 126 
					128 111 127 
					112 111 129 
					111 128 129 
					130 112 129 
					130 114 112 
					113 131 120 
					131 113 114 
					130 131 114 
					132 116 115 
					132 115 119 
					133 118 116 
					116 132 134 
					134 135 116 
					133 116 135 
					136 123 117 
					117 118 136 
					136 118 137 
					137 118 133 
					119 138 132 
					138 119 120 
					131 139 120 
					138 120 139 
					122 123 121 
					124 121 136 
					123 136 121 
					124 137 140 
					124 136 137 
					140 125 124 
					126 125 141 
					140 141 125 
					141 142 126 
					143 144 126 
					142 143 126 
					144 127 126 
					145 146 127 
					127 144 145 
					128 127 146 
					129 128 147 
					148 128 146 
					147 128 148 
					130 129 147 
					131 130 149 
					130 147 150 
					149 130 150 
					131 151 139 
					151 131 149 
					152 132 138 
					152 134 132 
					133 141 140 
					140 137 133 
					135 141 133 
					134 153 154 
					155 134 152 
					154 135 134 
					155 153 134 
					135 154 142 
					135 142 141 
					138 156 152 
					139 156 138 
					157 139 151 
					156 139 157 
					154 143 142 
					158 143 153 
					159 143 158 
					160 143 159 
					154 153 143 
					143 160 144 
					161 144 160 
					161 145 144 
					145 161 162 
					145 162 146 
					146 163 148 
					163 146 162 
					147 164 150 
					164 147 148 
					148 163 165 
					164 148 165 
					166 149 167 
					150 167 149 
					166 168 149 
					151 149 169 
					169 149 168 
					167 150 164 
					151 170 171 
					171 157 151 
					169 170 151 
					172 155 152 
					172 152 173 
					152 156 173 
					158 153 155 
					172 174 155 
					174 175 155 
					175 158 155 
					157 173 156 
					173 157 171 
					159 158 176 
					175 176 158 
					159 177 160 
					178 159 176 
					178 177 159 
					177 161 160 
					177 162 161 
					177 179 162 
					163 162 180 
					180 162 179 
					165 163 180 
					167 164 181 
					165 181 164 
					182 165 180 
					181 165 182 
					166 183 168 
					166 184 183 
					184 166 181 
					167 181 166 
					168 183 169 
					170 169 183 
					171 170 185 
					185 170 186 
					170 183 186 
					187 171 185 
					173 171 187 
					174 172 173 
					174 173 187 
					174 188 175 
					174 187 188 
					176 175 188 
					189 176 188 
					189 178 176 
					177 190 179 
					191 177 178 
					190 177 191 
					191 178 189 
					180 179 190 
					190 182 180 
					182 192 181 
					192 184 181 
					192 182 191 
					182 190 191 
					184 192 183 
					183 192 186 
					185 188 187 
					193 189 185 
					189 188 185 
					186 193 185 
					186 192 191 
					193 186 191 
					189 193 191 
				</DataArray>
				<DataArray type="Int32" Name="offsets" format="ascii">
					3 6 9 12 15 18 21 24 27 30 33 36 39 42 45 48 51 54 57 60 63 66 69 72 75 78 81 84 87 90 93 96 99 102 105 108 111 114 117 120 123 126 129 132 135 138 141 144 147 150 153 156 159 162 165 168 171 174 177 180 183 186 189 192 195 198 201 204 207 210 213 216 219 222 225 228 231 234 237 240 243 246 249 252 255 258 261 264 267 270 273 276 279 282 285 288 291 294 297 300 303 306 309 312 315 318 321 324 327 330 333 336 339 342 345 348 351 354 357 360 363 366 369 372 375 378 381 384 387 390 393 396 399 402 405 408 411 414 417 420 423 426 429 432 435 438 441 444 447 450 453 456 459 462 465 468 471 474 477 480 483 486 489 492 495 498 501 504 507 510 513 516 519 522 525 528 531 534 537 540 543 546 549 552 555 558 561 564 567 570 573 576 579 582 585 588 591 594 597 600 603 606 609 612 615 618 621 624 627 630 633 636 639 642 645 648 651 654 657 660 663 666 669 672 675 678 681 684 687 690 693 696 699 702 705 708 711 714 717 720 723 726 729 732 735 738 741 744 747 750 753 756 759 762 765 768 771 774 777 780 783 786 789 792 795 798 801 804 807 810 813 816 819 822 825 828 831 834 837 840 843 846 849 852 855 858 861 864 867 870 873 876 879 882 885 888 891 894 897 900 903 906 909 912 915 918 921 924 927 930 933 936 939 942 945 948 951 954 957 960 963 966 969 972 975 978 981 984 987 990 993 996 999 1002 1005 1008 1011 1014 1017 1020 1023 1026 1029 1032 1035 1038 1041 1044 1047 1050 1053 1056 1059 1062 1065 1068 1071 1074 1077 1080 1083 1086 1089 1092 1095 1098 1101 1104 1107 1110 1113 1116 1119 1122 1125 1128 1131 1134 1137 1140 1143 1146 1149 1152 
				</DataArray>
			</Polys>
		</Piece>
	</PolyData>
</VTKFile>
