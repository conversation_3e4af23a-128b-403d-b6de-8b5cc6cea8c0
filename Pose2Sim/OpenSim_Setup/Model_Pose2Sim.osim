<?xml version="1.0" encoding="UTF-8" ?>
<OpenSimDocument Version="40000">
	<Model name="Pose2Sim">
		<!--The model's ground reference frame.-->
		<Ground name="ground">
			<!--The geometry used to display the axes of this Frame.-->
			<FrameGeometry name="frame_geometry">
				<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
				<socket_frame>..</socket_frame>
				<!--Scale factors in X, Y, Z directions respectively.-->
				<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
			</FrameGeometry>
			<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
			<attached_geometry />
			<!--Set of wrap objects fixed to this body that GeometryPath<PERSON> can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
			<WrapObjectSet name="wrapobjectset">
				<objects />
				<groups />
			</WrapObjectSet>
		</Ground>
		<!--Acceleration due to gravity, expressed in ground.-->
		<gravity>0 -9.8066499999999994 0</gravity>
		<!--Credits (e.g., model author names) associated with the model.-->
		<credits>Full-body model adapted from Beaucage-Gauvreau et al., with Rajagopal et al. knee angles. No muscles included. Free pelvis translation, added ball joint between head and torso, locked arm pronation/supination, clamped hip flexion to 150° instead of 120°, hip adduction -60° instead of -50°, hip rotation -70° instead of -40°, knee 155° instead of 120°.</credits>
		<!--Publications and references associated with the model.-->
		<publications>Pagnon, D., Domalain, M., Reveret, L. (2021). Pose2Sim: An End-to-End Workflow for 3D Markerless Sports Kinematics—Part 1: Robustness. Sensors, 21(19), 6530.</publications>
		<!--Units for all lengths.-->
		<length_units>meters</length_units>
		<!--Units for all forces.-->
		<force_units>N</force_units>
		<!--List of bodies that make up this model.-->
		<BodySet name="bodyset">
			<objects>
				<Body name="pelvis">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="pelvis_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>r_pelvis.vtp</mesh_file>
						</Mesh>
						<Mesh name="pelvis_geom_2">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>l_pelvis.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>11.776999999999999</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>-0.070699999999999999 0 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.1028 0.087099999999999997 0.0579 0 0 0</inertia>
				</Body>
				<Body name="sacrum">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="sacrum_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>sacrum.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 0 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0 0 0 0 0 0</inertia>
				</Body>
				<Body name="femur_r">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="femur_r_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>femur_r.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>9.3013999999999992</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.17000000000000001 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.13389999999999999 0.035099999999999999 0.14119999999999999 0 0 0</inertia>
				</Body>
				<Body name="patella_r">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="patella_r_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>r_patella.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.086199999999999999</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.0018 0.0264 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>2.8700000000000001e-06 1.311e-05 1.311e-05 0 0 0</inertia>
				</Body>
				<Body name="tibia_r">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="tibia_r_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>tibia_r.vtp</mesh_file>
						</Mesh>
						<Mesh name="tibia_r_geom_2">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>fibula_r.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>3.7075</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.1867 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.0504 0.0051000000000000004 0.0511 0 0 0</inertia>
				</Body>
				<Body name="talus_r">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="talus_r_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>talus_rv.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.10000000000000001</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 0 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.001 0.001 0.001 0 0 0</inertia>
				</Body>
				<Body name="calcn_r">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="calcn_r_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>foot.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>1.25</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.10000000000000001 0.029999999999999999 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.0014 0.0038999999999999998 0.0041000000000000003 0 0 0</inertia>
				</Body>
				<Body name="toes_r">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="toes_r_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>bofoot.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.21659999999999999</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.034599999999999999 0.0060000000000000001 -0.017500000000000002</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.0001 0.00020000000000000001 0.001 0 0 0</inertia>
				</Body>
				<Body name="femur_l">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="femur_l_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>femur_l.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>9.3013999999999992</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.17000000000000001 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.13389999999999999 0.035099999999999999 0.14119999999999999 0 0 0</inertia>
				</Body>
				<Body name="patella_l">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="patella_l_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>l_patella.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.086199999999999999</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.0018 0.0264 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>2.8700000000000001e-06 1.311e-05 1.311e-05 0 0 0</inertia>
				</Body>
				<Body name="tibia_l">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="tibia_l_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>tibia_l.vtp</mesh_file>
						</Mesh>
						<Mesh name="tibia_l_geom_2">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>fibula_l.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>3.7075</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.1867 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.0504 0.0051000000000000004 0.0511 0 0 0</inertia>
				</Body>
				<Body name="talus_l">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="talus_l_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>talus_lv.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.10000000000000001</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 0 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.001 0.001 0.001 0 0 0</inertia>
				</Body>
				<Body name="calcn_l">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="calcn_l_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>l_foot.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>1.25</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.10000000000000001 0.029999999999999999 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.0014 0.0038999999999999998 0.0041000000000000003 0 0 0</inertia>
				</Body>
				<Body name="toes_l">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="toes_l_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>l_bofoot.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.21659999999999999</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.034599999999999999 0.0060000000000000001 0.017500000000000002</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.0001 0.00020000000000000001 0.001 0 0 0</inertia>
				</Body>
				<Body name="lumbar5">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="lumbar5_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.93000000000000005 0.93000000000000005 0.93000000000000005</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>lumbar5.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>1.8240000000000001</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.01728 0.0183 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.17821600000000001 0.021727400000000001 0.171486 0 0 0</inertia>
				</Body>
				<Body name="lumbar4">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="lumbar4_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.93000000000000005 0.93000000000000005 0.93000000000000005</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>lumbar4.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>1.7989999999999999</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.018599999999999998 0.0217953 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.171208 0.020679199999999998 0.16517799999999999 0 0 0</inertia>
				</Body>
				<Body name="lumbar3">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="lumbar3_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.93000000000000005 0.93000000000000005 0.93000000000000005</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>lumbar3.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>1.6699999999999999</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.017893900000000001 0.0232739 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.157697 0.0199107 0.152447 0 0 0</inertia>
				</Body>
				<Body name="lumbar2">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="lumbar2_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.93000000000000005 0.93000000000000005 0.93000000000000005</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>lumbar2.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>1.6890000000000001</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.0252462 0.020212399999999998 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.16270499999999999 0.021975100000000001 0.15770500000000001 0 0 0</inertia>
				</Body>
				<Body name="lumbar1">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="lumbar1_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.93000000000000005 0.93000000000000005 0.93000000000000005</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>lumbar1.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>1.677</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.032739999999999998 0.019407299999999999 0.00132891</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.162657 0.024131199999999998 0.15792700000000001 0 0 0</inertia>
				</Body>
				<Body name="torso">
					<!--List of components that this component owns and serializes.-->
					<components>
						<PhysicalOffsetFrame name="torso_geom_frame_1">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_1">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>cerv1sm.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.020500000000000001 0.40200000000000002 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_2">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_2">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>cerv2sm.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.020500000000000001 0.40200000000000002 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_3">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_3">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>cerv3sm.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.020500000000000001 0.40200000000000002 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_4">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_4">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>cerv4sm.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.020500000000000001 0.40200000000000002 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_5">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_5">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>cerv5sm.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.020500000000000001 0.40200000000000002 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_6">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_6">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>cerv6sm.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.020500000000000001 0.40200000000000002 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_7">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_7">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>cerv7.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.020500000000000001 0.40200000000000002 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_8">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_8">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic12_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_9">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_9">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic11_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_10">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_10">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic10_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_11">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_11">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic9_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_12">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_12">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic8_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_13">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_13">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic7_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_14">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_14">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic6_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_15">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_15">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic5_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_16">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_16">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic4_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_17">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_17">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic3_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_18">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_18">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic2_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_19">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_19">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>0.89000000000000001 0.89000000000000001 0.89000000000000001</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>thoracic1_s.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0035000000000000001 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_geom_frame_20">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
							<attached_geometry>
								<Mesh name="torso_geom_20">
									<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
									<socket_frame>..</socket_frame>
									<!--Scale factors in X, Y, Z directions respectively.-->
									<scale_factors>1 1.1000000000000001 1</scale_factors>
									<!--Default appearance attributes for this Geometry-->
									<Appearance>
										<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
										<opacity>1</opacity>
										<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
										<color>1 1 1</color>
									</Appearance>
									<!--Name of geometry file.-->
									<mesh_file>hat_ribs_scap.vtp</mesh_file>
								</Mesh>
							</attached_geometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>..</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0085000000000000006 -0.044999999999999998 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0 0 -0</orientation>
						</PhysicalOffsetFrame>
					</components>
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry />
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects>
							<WrapCylinder name="Cylinder">
								<!--Whether or not the WrapObject is considered active in computing paths-->
								<active>true</active>
								<!--Body-fixed Euler angle sequence for the orientation of the WrapObject-->
								<xyz_body_rotation>-0 -0 0</xyz_body_rotation>
								<!--Translation of the WrapObject.-->
								<translation>-0.20799999999999999 0.040000000000000001 -0</translation>
								<!--The name of quadrant over which the wrap object is active. For example, '+x' or '-y' to set the sidedness of the wrapping.-->
								<quadrant>all</quadrant>
								<!--Default appearance for this Geometry-->
								<Appearance>
									<!--Flag indicating whether the associated Geometry is visible or hidden.-->
									<visible>false</visible>
									<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
									<opacity>1</opacity>
									<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
									<color>0 1 1</color>
									<!--Visuals applied to surfaces associated with this Appearance.-->
									<SurfaceProperties>
										<!--The representation (1:Points, 2:Wire, 3:Shaded) used to display the object.-->
										<representation>3</representation>
									</SurfaceProperties>
								</Appearance>
								<!--The radius of the cylinder.-->
								<radius>0.17000000000000001</radius>
								<!--The length of the cylinder.-->
								<length>0.26000000000000001</length>
							</WrapCylinder>
							<WrapEllipsoid name="Chest_r">
								<!--Whether or not the WrapObject is considered active in computing paths-->
								<active>true</active>
								<!--Body-fixed Euler angle sequence for the orientation of the WrapObject-->
								<xyz_body_rotation>0 -0.40000000000000002 0.20000000000000001</xyz_body_rotation>
								<!--Translation of the WrapObject.-->
								<translation>0 0.20999999999999999 0.01</translation>
								<!--The name of quadrant over which the wrap object is active. For example, '+x' or '-y' to set the sidedness of the wrapping.-->
								<quadrant>z</quadrant>
								<!--Default appearance for this Geometry-->
								<Appearance>
									<!--Flag indicating whether the associated Geometry is visible or hidden.-->
									<visible>false</visible>
									<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
									<opacity>1</opacity>
									<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
									<color>0 1 1</color>
									<!--Visuals applied to surfaces associated with this Appearance.-->
									<SurfaceProperties>
										<!--The representation (1:Points, 2:Wire, 3:Shaded) used to display the object.-->
										<representation>3</representation>
									</SurfaceProperties>
								</Appearance>
								<!--The length of the radii of the ellipsoid.-->
								<dimensions>0.074999999999999997 0.17000000000000001 0.13</dimensions>
							</WrapEllipsoid>
							<WrapEllipsoid name="Chest_l">
								<!--Whether or not the WrapObject is considered active in computing paths-->
								<active>true</active>
								<!--Body-fixed Euler angle sequence for the orientation of the WrapObject-->
								<xyz_body_rotation>0 0.40000000000000002 0.20000000000000001</xyz_body_rotation>
								<!--Translation of the WrapObject.-->
								<translation>0 0.20999999999999999 -0.01</translation>
								<!--The name of quadrant over which the wrap object is active. For example, '+x' or '-y' to set the sidedness of the wrapping.-->
								<quadrant>-z</quadrant>
								<!--Default appearance for this Geometry-->
								<Appearance>
									<!--Flag indicating whether the associated Geometry is visible or hidden.-->
									<visible>false</visible>
									<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
									<opacity>1</opacity>
									<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
									<color>0 1 1</color>
									<!--Visuals applied to surfaces associated with this Appearance.-->
									<SurfaceProperties>
										<!--The representation (1:Points, 2:Wire, 3:Shaded) used to display the object.-->
										<representation>3</representation>
									</SurfaceProperties>
								</Appearance>
								<!--The length of the radii of the ellipsoid.-->
								<dimensions>0.074999999999999997 0.17000000000000001 0.13</dimensions>
							</WrapEllipsoid>
						</objects>
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>22.530000000000001</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.016232595708608341 0.18022592527648448 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>1.3661962752309638 0.70000765407730969 1.3262620199156339 0 0 0</inertia>
				</Body>
				<Body name="head">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="torso_geom_21">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>hat_jaw.vtp</mesh_file>
						</Mesh>
						<Mesh name="torso_geom_22">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>hat_skull.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>4.2778</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.016066733194191503 0.05850891278809623 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.011490919358968635 0.0083147028646661601 0.012530257217051907 0 0 0</inertia>
				</Body>
				<Body name="Abdomen">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects>
							<WrapEllipsoid name="Abdomen">
								<!--Whether or not the WrapObject is considered active in computing paths-->
								<active>true</active>
								<!--Body-fixed Euler angle sequence for the orientation of the WrapObject-->
								<xyz_body_rotation>0 0 0</xyz_body_rotation>
								<!--Translation of the WrapObject.-->
								<translation>0 0 0</translation>
								<!--The name of quadrant over which the wrap object is active. For example, '+x' or '-y' to set the sidedness of the wrapping.-->
								<quadrant>x</quadrant>
								<!--Default appearance for this Geometry-->
								<Appearance>
									<!--Flag indicating whether the associated Geometry is visible or hidden.-->
									<visible>false</visible>
									<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
									<opacity>1</opacity>
									<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
									<color>0 1 1</color>
									<!--Visuals applied to surfaces associated with this Appearance.-->
									<SurfaceProperties>
										<!--The representation (1:Points, 2:Wire, 3:Shaded) used to display the object.-->
										<representation>3</representation>
									</SurfaceProperties>
								</Appearance>
								<!--The length of the radii of the ellipsoid.-->
								<dimensions>0.01 0.080000000000000002 0.050000000000000003</dimensions>
							</WrapEllipsoid>
						</objects>
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.001</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0.0080000000000000002 0 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>1.7799999999999999e-06 5.7999999999999995e-07 1.3599999999999999e-06 0 0 0</inertia>
				</Body>
				<Body name="humerus_r">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="humerus_r_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
								<!--Visuals applied to surfaces associated with this Appearance.-->
								<SurfaceProperties>
									<!--The representation (1:Points, 2:Wire, 3:Shaded) used to display the object.-->
									<representation>3</representation>
								</SurfaceProperties>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>humerus_rv.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>2.0325000000000002</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.16450200000000001 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.011946 0.0041209999999999997 0.013409000000000001 0 0 0</inertia>
				</Body>
				<Body name="ulna_r">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="ulna_r_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>ulna_rv.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.60750000000000004</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.12052499999999999 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.0029619999999999998 0.00061799999999999995 0.0032130000000000001 0 0 0</inertia>
				</Body>
				<Body name="radius_r">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="radius_r_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>radius_rv.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.60750000000000004</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.12052499999999999 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.0029619999999999998 0.00061799999999999995 0.0032130000000000001 0 0 0</inertia>
				</Body>
				<Body name="hand_r">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="hand_r_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>pisiform_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_2">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>lunate_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_3">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>scaphoid_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_4">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>triquetrum_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_5">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>hamate_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_6">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>capitate_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_7">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>trapezoid_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_8">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>trapezium_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_9">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>metacarpal2_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_10">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>index_proximal_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_11">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>index_medial_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_12">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>index_distal_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_13">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>metacarpal3_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_14">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>middle_proximal_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_15">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>middle_medial_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_16">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>middle_distal_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_17">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>metacarpal4_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_18">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>ring_proximal_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_19">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>ring_medial_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_20">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>ring_distal_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_21">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>metacarpal5_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_22">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>little_proximal_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_23">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>little_medial_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_24">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>little_distal_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_25">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>metacarpal1_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_26">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>thumb_proximal_rvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_r_geom_27">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>thumb_distal_rvs.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.45750000000000002</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.068095000000000003 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.000892 0.00054699999999999996 0.00134 0 0 0</inertia>
				</Body>
				<Body name="humerus_l">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="humerus_l_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>humerus_lv.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>2.0325000000000002</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.16450200000000001 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.011946 0.0041209999999999997 0.013409000000000001 0 0 0</inertia>
				</Body>
				<Body name="ulna_l">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="ulna_l_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>ulna_lv.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.60750000000000004</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.12052499999999999 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.0029619999999999998 0.00061799999999999995 0.0032130000000000001 0 0 0</inertia>
				</Body>
				<Body name="radius_l">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="radius_l_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>1 1 1</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>radius_lv.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.60750000000000004</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.12052499999999999 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.0029619999999999998 0.00061799999999999995 0.0032130000000000001 0 0 0</inertia>
				</Body>
				<Body name="hand_l">
					<!--The geometry used to display the axes of this Frame.-->
					<FrameGeometry name="frame_geometry">
						<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
						<socket_frame>..</socket_frame>
						<!--Scale factors in X, Y, Z directions respectively.-->
						<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
					</FrameGeometry>
					<!--List of geometry attached to this Frame. Note, the geometry are treated as fixed to the frame and they share the transform of the frame when visualized-->
					<attached_geometry>
						<Mesh name="hand_l_geom_1">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>pisiform_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_2">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>lunate_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_3">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>scaphoid_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_4">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>triquetrum_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_5">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>hamate_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_6">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>capitate_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_7">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>trapezoid_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_8">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>trapezium_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_9">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>metacarpal2_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_10">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>index_proximal_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_11">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>index_medial_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_12">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>index_distal_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_13">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>metacarpal3_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_14">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>middle_proximal_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_15">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>middle_medial_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_16">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>middle_distal_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_17">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>metacarpal4_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_18">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>ring_proximal_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_19">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>ring_medial_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_20">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>ring_distal_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_21">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>metacarpal5_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_22">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>little_proximal_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_23">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>little_medial_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_24">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>little_distal_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_25">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>metacarpal1_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_26">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>thumb_proximal_lvs.vtp</mesh_file>
						</Mesh>
						<Mesh name="hand_l_geom_27">
							<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
							<socket_frame>..</socket_frame>
							<!--Scale factors in X, Y, Z directions respectively.-->
							<scale_factors>0.84999999999999998 0.84999999999999998 0.84999999999999998</scale_factors>
							<!--Default appearance attributes for this Geometry-->
							<Appearance>
								<!--Flag indicating whether the associated Geometry is visible or hidden.-->
								<visible>true</visible>
								<!--The opacity used to display the geometry between 0:transparent, 1:opaque.-->
								<opacity>1</opacity>
								<!--The color, (red, green, blue), [0, 1], used to display the geometry. -->
								<color>1 1 1</color>
							</Appearance>
							<!--Name of geometry file.-->
							<mesh_file>thumb_distal_lvs.vtp</mesh_file>
						</Mesh>
					</attached_geometry>
					<!--Set of wrap objects fixed to this body that GeometryPaths can wrap over.This property used to be a member of Body but was moved up with the introduction of Frames.-->
					<WrapObjectSet name="wrapobjectset">
						<objects />
						<groups />
					</WrapObjectSet>
					<!--The mass of the body (kg)-->
					<mass>0.45750000000000002</mass>
					<!--The location (Vec3) of the mass center in the body frame.-->
					<mass_center>0 -0.068095000000000003 0</mass_center>
					<!--The elements of the inertia tensor (Vec6) as [Ixx Iyy Izz Ixy Ixz Iyz] measured about the mass_center and not the body origin.-->
					<inertia>0.000892 0.00054699999999999996 0.00134 0 0 0</inertia>
				</Body>
			</objects>
			<groups />
		</BodySet>
		<!--List of joints that connect the bodies.-->
		<JointSet name="jointset">
			<objects>
				<CustomJoint name="ground_pelvis">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>ground_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>pelvis_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="pelvis_tilt">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.5707963300000001 1.5707963300000001</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="pelvis_list">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.5707963300000001 1.5707963300000001</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="pelvis_rotation">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.5707963300000001 1.5707963300000001</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="pelvis_tx">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-5 5</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="pelvis_ty">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0.93999999999999995</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1 2</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="pelvis_tz">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-3 3</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="ground_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/ground</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="pelvis_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/pelvis</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>pelvis_tilt</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>pelvis_list</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>pelvis_rotation</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>pelvis_tx</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>pelvis_ty</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>pelvis_tz</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<WeldJoint name="sacrumjnt">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>pelvis_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>sacrum_offset</socket_child_frame>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="pelvis_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/pelvis</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.01 -0.017000000000000001 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="sacrum_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/sacrum</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
				</WeldJoint>
				<CustomJoint name="hip_r">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>pelvis_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>femur_r_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="hip_flexion_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>1.5670050666942832e-09</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.87 2.6179899999999998</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="hip_adduction_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>-9.4634793044849591e-10</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.0471975511965976 0.52359878000000004</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="hip_rotation_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.2217304763960306 0.69813170000000002</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="pelvis_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/pelvis</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.070699999999999999 -0.066100000000000006 0.083500000000000005</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="femur_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/femur_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>hip_flexion_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>hip_adduction_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>hip_rotation_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="walker_knee_r">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>femur_r_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>tibia_r_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="knee_angle_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>7.6916171281619415e-05</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>0 2.705260340591211</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="femur_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/femur_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.00809 -0.40795999999999999 -0.0027499999999999998</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-1.64157 1.44618 1.5708</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="tibia_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/tibia_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.0080895399999999992 -0.0105348 -0.00148474</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-1.64157 1.44618 1.5708</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0 0.0126809 0.0226969 0.0296054 0.0332049 0.0335354 0.0308779 0.0257548 0.0189295 0.011407 0.00443314 -0.00050475 -0.0016782</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0 0.059461 0.109399 0.150618 0.18392 0.210107 0.229983 0.24435 0.254012 0.25977 0.262428 0.262788 0.261654</y>
							</SimmSpline>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0 0.000479 0.000835 0.001086 0.001251 0.001346 0.001391 0.001403 0.0014 0.0014 0.001421 0.001481 0.001599</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0 0.000988 0.001899 0.002734 0.003492 0.004173 0.004777 0.005305 0.005756 0.00613 0.006427 0.006648 0.006792</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="patellofemoral_r">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>femur_r_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>patella_r_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="knee_angle_r_beta">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>7.6916171281619415e-05</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="femur_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/femur_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.00809 -0.40795999999999999 -0.0027499999999999998</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="patella_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/patella_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_r_beta</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0.00113686 -0.00629212 -0.105582 -0.253683 -0.414245 -0.579047 -0.747244 -0.91799 -1.09044 -1.26379 -1.43763 -1.61186 -1.78634</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_r_beta</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0.0524 0.0488 0.0437 0.0371 0.0296 0.0216 0.0136 0.0057 -0.0019 -0.0088 -0.0148 -0.0196 -0.0227</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_r_beta</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> -0.0108 -0.019 -0.0263 -0.0322 -0.0367 -0.0395 -0.0408 -0.0404 -0.0384 -0.0349 -0.0301 -0.0245 -0.0187</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0.0027499999999999998</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="ankle_r">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>tibia_r_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>talus_r_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="ankle_angle_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.0471975511965976 1.0471975511965976</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="tibia_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/tibia_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 -0.42999999999999999 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="talus_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/talus_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>ankle_angle_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>-0.10501355 -0.17402245 0.97912631999999999</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0.97912631999999999 -0 0.10501355</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<PinJoint name="subtalar_r">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>talus_r_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>calcn_r_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="subtalar_angle_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.34906585000000001 0.34906585000000001</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="talus_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/talus_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.048770000000000001 -0.041950000000000001 0.00792</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-1.7681899999999999 0.906223 1.8196000000000001</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="calcn_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/calcn_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-1.7681899999999999 0.906223 1.8196000000000001</orientation>
						</PhysicalOffsetFrame>
					</frames>
				</PinJoint>
				<PinJoint name="mtp_r">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>calcn_r_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>toes_r_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="mtp_angle_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.52359878000000004 0.52359878000000004</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="calcn_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/calcn_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.17879999999999999 -0.002 0.00108</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-3.1415899999999999 0.61990100000000004 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="toes_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/toes_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-3.1415899999999999 0.61990100000000004 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
				</PinJoint>
				<CustomJoint name="hip_l">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>pelvis_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>femur_l_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="hip_flexion_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>1.5670050666942832e-09</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.87 2.6179899999999998</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="hip_adduction_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>-9.4634793044849591e-10</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.0471975511965976 0.52359878000000004</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="hip_rotation_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.2217304763960306 0.69813170000000002</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="pelvis_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/pelvis</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.070699999999999999 -0.066100000000000006 -0.083500000000000005</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="femur_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/femur_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>hip_flexion_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>hip_adduction_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> -1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>hip_rotation_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> -1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="walker_knee_l">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>femur_l_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>tibia_l_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="knee_angle_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>7.6916171281619415e-05</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>0 2.705260340591211</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="femur_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/femur_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.00809 -0.40795999999999999 0.0027499999999999998</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>1.64157 -1.44618 1.5708</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="tibia_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/tibia_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.0080895399999999992 -0.0105348 0.00148474</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>1.64157 -1.44618 1.5708</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>-1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0 0.0126809 0.0226969 0.0296054 0.0332049 0.0335354 0.0308779 0.0257548 0.0189295 0.011407 0.00443314 -0.00050475 -0.0016782</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0 -0.059461 -0.109399 -0.150618 -0.18392 -0.210107 -0.229983 -0.24435 -0.254012 -0.25977 -0.262428 -0.262788 -0.261654</y>
							</SimmSpline>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0 0.000479 0.000835 0.001086 0.001251 0.001346 0.001391 0.001403 0.0014 0.0014 0.001421 0.001481 0.001599</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0 -0.000988 -0.001899 -0.002734 -0.003492 -0.004173 -0.004777 -0.005305 -0.005756 -0.00613 -0.006427 -0.006648 -0.006792</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="patellofemoral_l">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>femur_l_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>patella_l_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="knee_angle_l_beta">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>7.6916171281619415e-05</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="femur_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/femur_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.00809 -0.40795999999999999 0.0027499999999999998</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="patella_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/patella_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_l_beta</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0.00113686 -0.00629212 -0.105582 -0.253683 -0.414245 -0.579047 -0.747244 -0.91799 -1.09044 -1.26379 -1.43763 -1.61186 -1.78634</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_l_beta</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> 0.0524 0.0488 0.0437 0.0371 0.0296 0.0216 0.0136 0.0057 -0.0019 -0.0088 -0.0148 -0.0196 -0.0227</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>knee_angle_l_beta</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<SimmSpline name="function">
								<x> 0 0.174533 0.349066 0.523599 0.698132 0.872665 1.0472 1.22173 1.39626 1.5708 1.74533 1.91986 2.0944</x>
								<y> -0.0108 -0.019 -0.0263 -0.0322 -0.0367 -0.0395 -0.0408 -0.0404 -0.0384 -0.0349 -0.0301 -0.0245 -0.0187</y>
							</SimmSpline>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>-0.0027499999999999998</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="ankle_l">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>tibia_l_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>talus_l_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="ankle_angle_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.0471975499999999 1.0471975499999999</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="tibia_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/tibia_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 -0.42999999999999999 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="talus_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/talus_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>ankle_angle_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0.10501355 0.17402245 0.97912631999999999</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0.97912631999999999 0 -0.10501355</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<PinJoint name="subtalar_l">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>talus_l_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>calcn_l_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="subtalar_angle_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.34906585000000001 0.34906585000000001</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="talus_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/talus_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.048770000000000001 -0.041950000000000001 -0.00792</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>1.7681899999999999 -0.906223 1.8196000000000001</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="calcn_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/calcn_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>1.7681899999999999 -0.906223 1.8196000000000001</orientation>
						</PhysicalOffsetFrame>
					</frames>
				</PinJoint>
				<PinJoint name="mtp_l">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>calcn_l_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>toes_l_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="mtp_angle_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.52359878000000004 0.52359878000000004</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="calcn_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/calcn_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.17879999999999999 -0.002 -0.00108</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-3.1415899999999999 -0.61990100000000004 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="toes_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/toes_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-3.1415899999999999 -0.61990100000000004 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
				</PinJoint>
				<CustomJoint name="L5_S1_IVDjnt">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>sacrum_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>lumbar5_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="L5_S1_Flex_Ext">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.19547690000000001 0.06352998</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L5_S1_Lat_Bending">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.039269909999999998 0.039269909999999998</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L5_S1_axial_rotation">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.17592919000000001 0.17592919000000001</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="sacrum_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/sacrum</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.098000000000000004 0.070999999999999994 -0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="lumbar5_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/lumbar5</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L5_S1_Flex_Ext</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L5_S1_Lat_Bending</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L5_S1_axial_rotation</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="L4_L5_IVDjnt">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>lumbar5_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>lumbar4_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="L4_L5_Flex_Ext">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L4_L5_Lat_Bending">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L4_L5_axial_rotation">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="lumbar5_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/lumbar5</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.01 0.029600000000000001 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="lumbar4_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/lumbar4</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.001 -0.0025000000000000001 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L4_L5_Flex_Ext</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L4_L5_Lat_Bending</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L4_L5_axial_rotation</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="L3_L4_IVDjnt">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>lumbar4_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>lumbar3_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="L3_L4_Flex_Ext">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L3_L4_Lat_Bending">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L3_L4_axial_rotation">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="lumbar4_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/lumbar4</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0.032000000000000001 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="lumbar3_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/lumbar3</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.00059999999999999995 -0.0011999999999999999 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L3_L4_Flex_Ext</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L3_L4_Lat_Bending</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L3_L4_axial_rotation</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="L2_L3_IVD_jnt">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>lumbar3_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>lumbar2_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="L2_L3_Flex_Ext">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L2_L3_Lat_Bending">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L2_L3_axial_rotation">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="lumbar3_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/lumbar3</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.0080000000000000002 0.031199999999999999 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="lumbar2_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/lumbar2</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.0012999999999999999 -0.0038999999999999998 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L2_L3_Flex_Ext</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L2_L3_Lat_Bending</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L2_L3_axial_rotation</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="L1_L2_IVD_jnt">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>lumbar2_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>lumbar1_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="L1_L2_Flex_Ext">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L1_L2_Lat_Bending">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L1_L2_axial_rotation">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="lumbar2_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/lumbar2</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.01 0.025000000000000001 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="lumbar1_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/lumbar1</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0014 -0.0038 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L1_L2_Flex_Ext</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L1_L2_Lat_Bending</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L1_L2_axial_rotation</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="L1_T12_IVD_jnt">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>lumbar1_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>torso_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="L1_T12_Flex_Ext">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L1_T12_Lat_Bending">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="L1_T12_axial_rotation">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.1000000000000001 1.1000000000000001</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="lumbar1_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/lumbar1</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.0104 0.0275 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="torso_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/torso</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.0129 0.1154 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L1_T12_Flex_Ext</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L1_T12_Lat_Bending</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>L1_T12_axial_rotation</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="Abdjnt">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>sacrum_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>Abdomen_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="Abs_r3">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.7 1.7</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="Abs_r2">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.7 1.7</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="Abs_r1">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.7 1.7</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="Abs_t1">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>true</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="Abs_t2">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-99999.899999999994 99999.899999999994</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>true</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="sacrum_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/sacrum</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.098000000000000004 0.070999999999999994 -0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="Abdomen_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/Abdomen</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.089999999999999997 -0.089999999999999997 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>Abs_r3</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>Abs_r2</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>Abs_r1</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>Abs_t1</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>Abs_t2</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="head_torso">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>torso_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>head_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="neck_flexion">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>5.488090856396461e-11</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.5 0.5</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="neck_bending">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.5707963300000001 1.5707963300000001</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
						</Coordinate>
						<Coordinate name="neck_rotation">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>3.6589138998366536e-10</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.78539800000000004 0.78539800000000004</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="torso_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/torso</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0.54145811252102782 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="head_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/head</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0.54145799999999999 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>neck_flexion</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>neck_bending</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>neck_rotation</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<MultiplierFunction name="function">
								<function>
									<Constant name="function">
										<value>0</value>
									</Constant>
								</function>
								<scale>1.061682573570643</scale>
							</MultiplierFunction>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<MultiplierFunction name="function">
								<function>
									<Constant name="function">
										<value>0</value>
									</Constant>
								</function>
								<scale>1.061682573570643</scale>
							</MultiplierFunction>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<MultiplierFunction name="function">
								<function>
									<Constant name="function">
										<value>0</value>
									</Constant>
								</function>
								<scale>1.061682573570643</scale>
							</MultiplierFunction>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<CustomJoint name="acromial_r">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>torso_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>humerus_r_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="arm_flex_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.57 1.57</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="arm_add_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>-1.57</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-2.09 1.57</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="arm_rot_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.57 1.57</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="torso_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/torso</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0031549999999999998 0.3715 0.17000000000000001</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="humerus_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/humerus_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>arm_flex_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>arm_add_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>arm_rot_r</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<PinJoint name="elbow_r">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>humerus_r_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>ulna_r_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="elbow_flex_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>7.6915122188371947e-05</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>0 2.6179999999999999</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="humerus_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/humerus_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.013143999999999999 -0.286273 -0.0095949999999999994</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0.0228627 0.228018 0.0051688999999999997</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="ulna_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/ulna_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-0.0228627 0.228018 0.0051688999999999997</orientation>
						</PhysicalOffsetFrame>
					</frames>
				</PinJoint>
				<PinJoint name="radioulnar_r">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>ulna_r_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>radius_r_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="pro_sup_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>1.57</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>0.0 1.5707963300000001</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="ulna_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/ulna_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.0067270000000000003 -0.013006999999999999 0.026082999999999999</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-1.56884 0.056427999999999999 1.5361400000000001</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="radius_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/radius_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-1.56884 0.056427999999999999 1.5361400000000001</orientation>
						</PhysicalOffsetFrame>
					</frames>
				</PinJoint>
				<UniversalJoint name="radius_hand_r">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>radius_r_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>hand_r_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="wrist_flex_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.92 1.92</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="wrist_dev_r">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.43633231 0.61086523999999998</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="radius_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/radius_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.0087969999999999993 -0.235841 0.013610000000000001</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-1.5708 0 -1.5708</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="hand_r_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/hand_r</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>-1.5708 0 -1.5708</orientation>
						</PhysicalOffsetFrame>
					</frames>
				</UniversalJoint>
				<CustomJoint name="acromial_l">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>torso_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>humerus_l_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="arm_flex_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.57 1.57</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="arm_add_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>-1.57</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-2.09 1.57</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="arm_rot_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.57 1.57</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>false</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="torso_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/torso</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.0031549999999999998 0.3715 -0.17000000000000001</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="humerus_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/humerus_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0 0 0</orientation>
						</PhysicalOffsetFrame>
					</frames>
					<!--Defines how the child body moves with respect to the parent as a function of the generalized coordinates.-->
					<SpatialTransform>
						<!--3 Axes for rotations are listed first.-->
						<TransformAxis name="rotation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>arm_flex_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>arm_add_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>-1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<TransformAxis name="rotation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates>arm_rot_l</coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 -1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<LinearFunction name="function">
								<coefficients> 1 0</coefficients>
							</LinearFunction>
						</TransformAxis>
						<!--3 Axes for translations are listed next.-->
						<TransformAxis name="translation1">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>1 0 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation2">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 1 0</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
						<TransformAxis name="translation3">
							<!--Names of the coordinates that serve as the independent variables         of the transform function.-->
							<coordinates></coordinates>
							<!--Rotation or translation axis for the transform.-->
							<axis>0 0 1</axis>
							<!--Transform function of the generalized coordinates used to        represent the amount of displacement along a specified axis.-->
							<Constant name="function">
								<value>0</value>
							</Constant>
						</TransformAxis>
					</SpatialTransform>
				</CustomJoint>
				<PinJoint name="elbow_l">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>humerus_l_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>ulna_l_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="elbow_flex_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>7.6915122188371947e-05</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>0 2.6179999999999999</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="humerus_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/humerus_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0.013143999999999999 -0.286273 0.0095949999999999994</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0.0228627 -0.228018 0.0051688999999999997</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="ulna_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/ulna_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>0.0228627 -0.228018 0.0051688999999999997</orientation>
						</PhysicalOffsetFrame>
					</frames>
				</PinJoint>
				<PinJoint name="radioulnar_l">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>ulna_l_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>radius_l_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="pro_sup_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>1.57</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>0.0 1.5707963300000001</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="ulna_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/ulna_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.0067270000000000003 -0.013006999999999999 -0.026082999999999999</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>1.56884 -0.056427999999999999 1.5361400000000001</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="radius_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/radius_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>1.56884 -0.056427999999999999 1.5361400000000001</orientation>
						</PhysicalOffsetFrame>
					</frames>
				</PinJoint>
				<UniversalJoint name="radius_hand_l">
					<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The parent frame for the joint.).-->
					<socket_parent_frame>radius_l_offset</socket_parent_frame>
					<!--Path to a Component that satisfies the Socket 'child_frame' of type PhysicalFrame (description: The child frame for the joint.).-->
					<socket_child_frame>hand_l_offset</socket_child_frame>
					<!--List containing the generalized coordinates (q's) that parameterize this joint.-->
					<coordinates>
						<Coordinate name="wrist_flex_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-1.92 1.92</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
						<Coordinate name="wrist_dev_l">
							<!--The value of this coordinate before any value has been set. Rotational coordinate value is in radians and Translational in meters.-->
							<default_value>0</default_value>
							<!--The speed value of this coordinate before any value has been set. Rotational coordinate value is in rad/s and Translational in m/s.-->
							<default_speed_value>0</default_speed_value>
							<!--The minimum and maximum values that the coordinate can range between. Rotational coordinate range in radians and Translational in meters.-->
							<range>-0.43633231 0.61086523999999998</range>
							<!--Flag indicating whether or not the values of the coordinates should be limited to the range, above.-->
							<clamped>true</clamped>
							<!--Flag indicating whether or not the values of the coordinates should be constrained to the current (e.g. default) value, above.-->
							<locked>false</locked>
							<!--If specified, the coordinate can be prescribed by a function of time. It can be any OpenSim Function with valid second order derivatives.-->
							<prescribed_function />
							<!--Flag indicating whether or not the values of the coordinates should be prescribed according to the function above. It is ignored if the no prescribed function is specified.-->
							<prescribed>false</prescribed>
						</Coordinate>
					</coordinates>
					<!--Physical offset frames owned by the Joint that are typically used to satisfy the owning Joint's parent and child frame connections (sockets). PhysicalOffsetFrames are often used to describe the fixed transformation from a Body's origin to another location of interest on the Body (e.g., the joint center). When the joint is deleted, so are the PhysicalOffsetFrame components in this list.-->
					<frames>
						<PhysicalOffsetFrame name="radius_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/radius_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>-0.0087969999999999993 -0.235841 -0.013610000000000001</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>1.5708 0 1.5708</orientation>
						</PhysicalOffsetFrame>
						<PhysicalOffsetFrame name="hand_l_offset">
							<!--The geometry used to display the axes of this Frame.-->
							<FrameGeometry name="frame_geometry">
								<!--Path to a Component that satisfies the Socket 'frame' of type Frame.-->
								<socket_frame>..</socket_frame>
								<!--Scale factors in X, Y, Z directions respectively.-->
								<scale_factors>0.20000000000000001 0.20000000000000001 0.20000000000000001</scale_factors>
							</FrameGeometry>
							<!--Path to a Component that satisfies the Socket 'parent' of type C (description: The parent frame to this frame.).-->
							<socket_parent>/bodyset/hand_l</socket_parent>
							<!--Translational offset (in meters) of this frame's origin from the parent frame's origin, expressed in the parent frame.-->
							<translation>0 0 0</translation>
							<!--Orientation offset (in radians) of this frame in its parent frame, expressed as a frame-fixed x-y-z rotation sequence.-->
							<orientation>1.5708 0 1.5708</orientation>
						</PhysicalOffsetFrame>
					</frames>
				</UniversalJoint>
			</objects>
			<groups />
		</JointSet>
		<!--Controllers that provide the control inputs for Actuators.-->
		<ControllerSet name="controllerset">
			<objects />
			<groups />
		</ControllerSet>
		<!--Constraints in the model.-->
		<ConstraintSet name="constraintset">
			<objects>
				<CoordinateCouplerConstraint name="Abs_r3_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 7.1429 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Flex_Ext</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>Abs_r3</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="Abs_r1_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 5.5617 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_axial_rotation</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>Abs_r1</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="Abs_r2_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 11.3122 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Lat_Bending</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>Abs_r2</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L4_L5_Flex_Ext_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 1.8571 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Flex_Ext</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L4_L5_Flex_Ext</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L4_L5_axial_rotation_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 1.0625 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_axial_rotation</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L4_L5_axial_rotation</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L4_L5_Lat_Bending_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 1.33 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Lat_Bending</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L4_L5_Lat_Bending</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L3_L4_Flex_Ext_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 1.6429 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Flex_Ext</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L3_L4_Flex_Ext</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L3_L4_axial_rotation_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 1.0625 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_axial_rotation</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L3_L4_axial_rotation</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L3_L4_Lat_Bending_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 2.4314 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Lat_Bending</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L3_L4_Lat_Bending</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L2_L3_Flex_Ext_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 1.1429 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Flex_Ext</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L2_L3_Flex_Ext</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L2_L3_axial_rotation_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 0.875 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_axial_rotation</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L2_L3_axial_rotation</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L2_L3_Lat_Bending_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 2.4314 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Lat_Bending</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L2_L3_Lat_Bending</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L1_L2_Flex_Ext_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 0.9286 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Flex_Ext</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L1_L2_Flex_Ext</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L1_L2_axial_rotation_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 0.8125 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_axial_rotation</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L1_L2_axial_rotation</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L1_L2_Lat_Bending_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 2.0392 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Lat_Bending</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L1_L2_Lat_Bending</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L1_T12_axial_rotation">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 0.75 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_axial_rotation</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L1_T12_axial_rotation</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L1_T12_Flex_Ext_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 0.5714 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Flex_Ext</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L1_T12_Flex_Ext</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="L1_T12_Lat_Bending_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 1.549 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>L5_S1_Lat_Bending</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>L1_T12_Lat_Bending</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="patellofemoral_knee_angle_r_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 1 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>knee_angle_r</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>knee_angle_r_beta</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
				<CoordinateCouplerConstraint name="patellofemoral_knee_angle_l_con">
					<!--Flag indicating whether the constraint is enforced or not.Enforced means that the constraint is active in subsequent dynamics realizations. NOTE: Prior to OpenSim 4.0, this behavior was controlled by the 'isDisabled' property, where 'true' meant the constraint was not being enforced. Thus, if 'isDisabled' is'true', then 'isEnforced' is false.-->
					<isEnforced>true</isEnforced>
					<!--Constraint function of generalized coordinates (to be specified) used to evaluate the constraint errors and their derivatives, and must valid to at least 2nd order. Constraint function must evaluate to zero when coordinates satisfy the constraint.-->
					<coupled_coordinates_function>
						<LinearFunction>
							<coefficients> 1 0</coefficients>
						</LinearFunction>
					</coupled_coordinates_function>
					<!--List of names of the right hand side (independent) coordinates. Note the constraint function above, must be able to handle multiple coordinate values if more than one coordinate name is provided.-->
					<independent_coordinate_names>knee_angle_l</independent_coordinate_names>
					<!--Name of the left-hand side (dependent) coordinate of the constraint coupling function.-->
					<dependent_coordinate_name>knee_angle_l_beta</dependent_coordinate_name>
					<!--Scale factor for the coupling function.-->
					<scale_factor>1</scale_factor>
				</CoordinateCouplerConstraint>
			</objects>
			<groups />
		</ConstraintSet>
		<!--Forces in the model (includes Actuators).-->
		<ForceSet name="forceset">
			<objects />
			<groups />
		</ForceSet>
		<!--Markers in the model.-->
		<MarkerSet name="markerset">
			<objects />
			<groups />
		</MarkerSet>
		<!--Geometry to be used in contact forces.-->
		<ContactGeometrySet name="contactgeometryset">
			<objects />
			<groups />
		</ContactGeometrySet>
	</Model>
</OpenSimDocument>
