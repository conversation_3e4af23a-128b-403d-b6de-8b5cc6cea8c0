<?xml version="1.0" encoding="UTF-8" ?>
<OpenSimDocument Version="40000">
	<!-- AlphaPose Halpe68 or Halpe136 markers for Model_Pose2Sim.osim.-->
	<MarkerSet name="markerset">
		<objects>
			<Marker name="Nose">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/head</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.107623784338859 0.54290490722614126 0.0001528854696219647</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="Head">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type Physical<PERSON>rame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/head</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.065509255082141254 0.66792445678962864 -0.00064753098215644418</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="Neck">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.00081306591809742206 0.44689687437791942 0.00018485623598098755</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LShoulder">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.012751615047454833 0.36630706509036348 -0.20157419735588852</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RShoulder">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/torso</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.012751615047454833 0.36630706509036348 0.20157419735588852</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LElbow">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.025000000000000001 -0.29795454784749797 0.0087376529932870692</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RElbow">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/humerus_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.025000000000000001 -0.29795454784749797 -0.0087376529932870692</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LWrist">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/radius_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.00017358908136026123 -0.23509597713454577 -0.0097437949950177141</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RWrist">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/radius_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.00017358908136026123 -0.23509597713454577 0.0097437949950177141</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="Hip">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/pelvis</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.063927399999999995 0 0</location>
			</Marker>
			<Marker name="LHip">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/pelvis</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.063927444458007812 -0.081343112945556642 -0.10540640790244724</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RHip">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/pelvis</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.063927444458007812 -0.081343112945556642 0.10540640790244724</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LKnee">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.0054104845447782643 -0.38613215736805462 -0.0051106969426969557</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RKnee">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/femur_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.0054104845447782643 -0.38613215736805462 0.0051106969426969557</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LAnkle">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.00028630976101194031 -0.37683977416422021 -0.014959548491229383</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RAnkle">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/tibia_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>-0.00028630976101194031 -0.37683977416422021 0.014959548491229383</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LBigToe">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/toes_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.064664297458672193 0.0098693355454698862 0.014580254493150785</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LSmallToe">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/toes_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.027116648721891835 0.0095716438829001865 -0.028780161392620865</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="LHeel">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.013586029137032551 0.023304361603446754 0.0018867170601417895</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RBigToe">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/toes_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.064664297458672193 0.0098693355454698862 -0.014580254493150785</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RSmallToe">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/toes_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.027116648721891835 0.0095716438829001865 0.028780161392620865</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RHeel">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/calcn_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.013586029137032551 0.023304361603446754 -0.0018867170601417895</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>true</fixed>
			</Marker>
			<Marker name="RThumb">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.015900600000000001 -0.053530099999999997 0.045219599999999999</location>
			</Marker>
			<Marker name="RIndex">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0031182900000000001 -0.082610900000000001 0.0160293</location>
			</Marker>
			<Marker name="RPinky">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_r</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.00311312 -0.073810299999999995 -0.0325446</location>
			</Marker>
			<Marker name="LThumb">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.015900595366914239 -0.053530050744837965 -0.045219604622807212</location>
			</Marker>
			<Marker name="LIndex">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0031182929587494357 -0.082610892492083754 -0.016029259621825068</location>
			</Marker>
			<Marker name="LPinky">
				<!--Path to a Component that satisfies the Socket 'parent_frame' of type PhysicalFrame (description: The frame to which this station is fixed.).-->
				<socket_parent_frame>/bodyset/hand_l</socket_parent_frame>
				<!--The fixed location of the station expressed in its parent frame.-->
				<location>0.0031131181410346542 -0.073810317144973658 0.032544599634169884</location>
				<!--Flag (true or false) specifying whether the marker is fixed in its parent frame during the marker placement step of scaling.  If false, the marker is free to move within its parent Frame to match its experimental counterpart.-->
				<fixed>false</fixed>
			</Marker>
		</objects>
		<groups />
	</MarkerSet>
</OpenSimDocument>
