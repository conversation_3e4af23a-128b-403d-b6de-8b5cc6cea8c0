<?xml version="1.0" encoding="UTF-8" ?>
<OpenSimDocument Version="40000">
	<InverseKinematicsTool>
		<!--Name of the directory where results are written. Be default this is the directory in which the setup file is be  executed.-->
		<results_directory>./</results_directory>
		<!--Name/path to the xml .osim file.-->
		<model_file>../S00_Demo_Session/S00_P00_SingleParticipant/S00_P00_OpenSim/Model_Pose2Sim_S00_P00_LSTM_scaled.osim</model_file>
		<!--The relative weighting of kinematic constraint errors. By default this is Infinity, which means constraints are strictly enforced as part of the optimization and are not appended to the objective (cost) function. Any other non-zero positive scalar is the penalty factor for constraint violations.-->
		<constraint_weight>Inf</constraint_weight>
		<!--The accuracy of the solution in absolute terms, i.e. the number of significant digits to which the solution can be trusted. Default 1e-5.-->
		<accuracy>1.0000000000000001e-05</accuracy>
		<!--The time range for the study.-->
		<time_range>0.016666666666666666 1.6666666666666667</time_range>
		<!--Name of the resulting inverse kinematics motion (.mot) file.-->
		<output_motion_file>../S00_Demo_Session/S00_P00_SingleParticipant/S00_P00_OpenSim/S00_P00_T01_BalancingTrial_filt_butterworth_0-100_LSTM.mot</output_motion_file>
		<!--Flag (true or false) indicating whether or not to report errors from the inverse kinematics solution. Default is true.-->
		<report_errors>true</report_errors>
		<!--Markers and coordinates to be considered (tasks) and their weightings. The sum of weighted-squared task errors composes the cost function.-->
		<IKTaskSet>
			<objects>
				<IKMarkerTask name="C7_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>5</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_shoulder_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>5</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_shoulder_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>5</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r.ASIS_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>25</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L.ASIS_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>25</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r.PSIS_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>25</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L.PSIS_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>25</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_knee_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_mknee_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_ankle_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_mankle_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_toe_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_5meta_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_calc_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>60</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_knee_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_mknee_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_ankle_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_mankle_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_toe_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_calc_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>60</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_5meta_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>30</weight>
				</IKMarkerTask>                
                <IKMarkerTask name="r_lelbow_study">
                    <!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
                    <apply>true</apply>
                    <!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
                    <weight>5</weight>
                </IKMarkerTask>
                <IKMarkerTask name="L_lelbow_study">
                    <!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
                    <apply>true</apply>
                    <!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
                    <weight>5</weight>
                </IKMarkerTask>
                <IKMarkerTask name="r_melbow_study">
                    <!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
                    <apply>true</apply>
                    <!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
                    <weight>5</weight>
                </IKMarkerTask>
                <IKMarkerTask name="L_melbow_study">
                    <!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
                    <apply>true</apply>
                    <!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
                    <weight>5</weight>
                </IKMarkerTask>                    
                <IKMarkerTask name="r_lwrist_study">
                    <!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
                    <apply>true</apply>
                    <!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
                    <weight>5</weight>
                </IKMarkerTask>
                <IKMarkerTask name="L_lwrist_study">
                    <!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
                    <apply>true</apply>
                    <!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
                    <weight>5</weight>
                </IKMarkerTask>
                <IKMarkerTask name="r_mwrist_study">
                    <!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
                    <apply>true</apply>
                    <!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
                    <weight>5</weight>
                </IKMarkerTask>
                <IKMarkerTask name="L_mwrist_study">
                    <!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
                    <apply>true</apply>
                    <!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
                    <weight>5</weight>
                </IKMarkerTask>   
				<IKMarkerTask name="r_thigh1_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_thigh2_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_thigh3_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>				
				<IKMarkerTask name="L_thigh1_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_thigh2_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_thigh3_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>				
				<IKMarkerTask name="r_sh1_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_sh2_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>
				<IKMarkerTask name="r_sh3_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>				
				<IKMarkerTask name="L_sh1_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_sh2_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>
				<IKMarkerTask name="L_sh3_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>4</weight>
				</IKMarkerTask>
				<IKMarkerTask name="RHJC_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>25</weight>
				</IKMarkerTask>
				<IKMarkerTask name="LHJC_study">
					<!--Whether or not this task will be used during inverse kinematics solve.-->
					<apply>true</apply>
					<!--Weight given to a marker or coordinate for solving inverse kinematics problems.-->
					<weight>25</weight>
				</IKMarkerTask>
				<IKCoordinateTask name="L5_S1_Flex_Ext">
					<!--Whether or not this task will be used during inverse kinematics solve, default is true.-->
					<apply>true</apply>
					<!--Weight given to the task when solving inverse kinematics problems, default is 0.-->
					<weight>0.1</weight>
					<!--Indicates the source of the coordinate value for this task.  Possible values are default_value (use default value of coordinate, as specified in the model file, as the fixed target value), manual_value (use the value specified in the value property of this task as the fixed target value), or from_file (use the coordinate values from the coordinate data specified by the coordinates_file property).-->
					<value_type>default_value</value_type>
					<!--This value will be used as the desired (or prescribed) coordinate value if value_type is set to manual_value.-->
					<value>0</value>
				</IKCoordinateTask>
			</objects>
			<groups />
		</IKTaskSet>
		<!--TRC file (.trc) containing the time history of observations of marker positions obtained during a motion capture experiment. Markers in this file that have a corresponding task and model marker are included.-->
		<marker_file>../S00_Demo_Session/S00_P00_SingleParticipant/S00_P00_T01_BalancingTrial/pose-3d/S00_P00_T01_BalancingTrial_0-100_filt_butterworth_LSTM.trc</marker_file>
		<!--The name of the storage (.sto or .mot) file containing the time history of coordinate observations. Coordinate values from this file are included if there is a corresponding model coordinate and task. -->
		<coordinate_file>Unassigned</coordinate_file>
		<!--Flag indicating whether or not to report model marker locations. Note, model marker locations are expressed in Ground.-->
		<report_marker_locations>false</report_marker_locations>
	</InverseKinematicsTool>
</OpenSimDocument>
