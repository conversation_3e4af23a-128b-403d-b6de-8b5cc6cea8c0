#!/usr/bin/env python
# -*- coding: utf-8 -*-


'''
    ##################################################
    ## Build csv from mot and osim files            ##
    ##################################################
    
    Build a csv file which stores locations and orientations of all bodies
    calculated from a .mot motion file and a .osim model file.
    
    Equivalent to OpenSim Analysis -> BodyKinematics but without the bugs in 
    orientations due to their use of Euler angle instead of homography matrices
    Angles are unwrapped (np.unwrap) with an assumed period of 2*pi/360.

    Transforms from OpenSim's yup to <PERSON><PERSON><PERSON>'s zup unless you set direction = 'yup'
    An optional arugment may be passed by the user to maintain the opensim convention ('yup') 

    Beware, it can be quite slow depending on the complexity 
    of the model and on the number of frames.
    
    Usage: 
    from Pose2Sim.Utilities import bodykin_from_mot_osim; bodykin_from_mot_osim.bodykin_from_mot_osim_func(r'<input_mot_file>', r'<output_osim_file>', r'<output_csv_file>')
    bodykin_from_mot_osim -m input_mot_file -o input_osim_file
    bodykin_from_mot_osim -m input_mot_file -o input_osim_file -c output_csv_file -d direction
'''

## INIT
import os
import numpy as np
import opensim as osim
import argparse
from scipy import signal

#direction = 'zup' # 'zup' or 'yup'

## AUTHORSHIP INFORMATION
__author__ = "David Pagnon, Jonathan Camargo"
__copyright__ = "Copyright 2023, BlendOSim & Sim2Blend"
__credits__ = ["David Pagnon", "Jonathan Camargo"]
__license__ = "MIT License"
from importlib.metadata import version
__version__ = version('pose2sim')
__maintainer__ = "David Pagnon"
__email__ = "<EMAIL>"
__status__ = "Development"


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('-m', '--input_mot_file', required=True, help='input .mot file')
    parser.add_argument('-o', '--input_osim_file', required=True, help='input .osim file')
    parser.add_argument('-c', '--csv_output_file', required=False, help='output csv file')
    parser.add_argument('-d', '--direction', required=False, help='vertical axis direction')
    parser.add_argument('-v', '--calculate_com_vel', required=False, help='calculate center of mass velocity and return it')
    parser.add_argument('-s', '--save_com_file', required=False, help='save a file that contains both CoM position and velocity')
    parser.add_argument('-p', '--com_output_file', required=False, help='output CoM file path')
    args = vars(parser.parse_args())
    
    bodykin_from_mot_osim_func(args)

def bodykin_from_mot_osim_func(*args):
    '''
    Build a csv file which stores locations and orientations of all bodies
    calculated from a .mot motion file and a .osim model file.
    
    Equivalent to OpenSim Analysis -> BodyKinematics but without the bugs in 
    orientations due to their use of Euler angle instead of homography matrices
    
    Usage: 
    from Pose2Sim.Utilities import bodykin_from_mot_osim; bodykin_from_mot_osim.bodykin_from_mot_osim_func(r'<input_mot_file>', r'<output_osim_file>', r'<output_csv_file>','<direction>')
    bodykin_from_mot_osim -m input_mot_file -o input_osim_file
    bodykin_from_mot_osim -m input_mot_file -o input_osim_file -t output_csv_file -d direction
    '''
    
    try:
        motion_path = args[0]['input_mot_file'] # invoked with argparse
        osim_path = args[0]['input_osim_file']
        calculate_com_vel = args[0]['calculate_com_vel']
        save_com_file = args[0]['save_com_file']
        if args[0]['csv_output_file'] == None:
            output_csv_file = motion_path.replace('.mot', '.csv')
        else:
            output_csv_file = args[0]['csv_output_file']
        if args[0]['direction'] == None:
            direction = 'zup'
        else:
            direction = args[0]['direction']
    except:
        motion_path = args[0] # invoked as a function
        osim_path = args[1]
        try:
            output_csv_file = args[2]
        except:
            output_csv_file = motion_path.replace('.mot', '.csv')
        try:
            direction = args[3]
        except:
            direction = 'zup'
        try:
            calculate_com_vel = args[4]
        except:
            calculate_com_vel = False
        try:
            save_com_file = args[5]
        except:
            save_com_file = False
        try:
            com_output_file = args[6]
        except:
            com_output_file = motion_path.replace('.mot', '_com.csv')

    # if a user has requested to save the CoM data, without specifying to calculate it we can change that variable
    if save_com_file == True and calculate_com_vel == False: calculate_com_vel = True

    # pre-define the order and cut-off values for CoM filtering (we could give this as another argument if we think it's useful?)
    CoM_order = 4
    CoM_cutoff = 6.0

    # Read model and motion files
    model = osim.Model(osim_path)
    motion_data = osim.TimeSeriesTable(motion_path)
    
    # # Degrees or radians
    # with open(motion_path) as m_p:
        # while True:
            # line =  m_p.readline()
            # if 'inDegrees' in line:
                # break
    # if 'yes' in line:
        # in_degrees = True
    # else:
        # in_degrees = False

    # Model: get model coordinates and bodies
    model_coordSet = model.getCoordinateSet()
    # coordinates = [model_coordSet.get(i) for i in range(model_coordSet.getSize())]
    # coordinates = [c for c in coordinates if '_beta' not in c.getName()]
    # coordinateNames = [c.getName() for c in coordinates]
    coordinateNames = motion_data.getColumnLabels()
    model_bodySet = model.getBodySet()
    bodies = [model_bodySet.get(i) for i in range(model_bodySet.getSize())]
    bodyNames = [b.getName() for b in bodies]

    # Motion: read coordinates and convert to radians
    times = motion_data.getIndependentColumn()
    motion_data_np = motion_data.getMatrix().to_numpy()

    # pre allocate an array to store the CoM position at each frame
    if calculate_com_vel: CoM_pos_array = np.zeros((motion_data_np.shape[0], 3))

    for i, c in enumerate(coordinateNames):
        if model_coordSet.get(c).getMotionType() == 1: # 1: rotation, 2: translation, 3: coupled
            if  motion_data.getTableMetaDataAsString('inDegrees') == 'yes':
    # if in_degrees:
        # for i in range(len(coordinates)):
            # if coordinates[i].getMotionType() == 1: # 1: rotation, 2: translation, 3: coupled
                motion_data_np[:,i] = motion_data_np[:,i] * np.pi/180 # if rotation, convert to radians

    # Animate model
    state = model.initSystem()
    loc_rot_frame_all = []
    H_zup = np.array([[1,0,0,0], [0,0,-1,0], [0,1,0,0], [0,0,0,1]])
    print('Time frame:')
    for n in range(motion_data.getNumRows()):
        print(times[n], 's')
        # Set model struct in each time state
        for c, coord in enumerate(coordinateNames): ## PROBLEME QUAND HEADERS DE MOTION_DATA_NP ET COORDINATENAMES SONT PAS DANS LE MEME ORDRE
            try:
                model.getCoordinateSet().get(coord).setValue(state, motion_data_np[n,c], enforceContraints=False)
            except:
                pass
        # model.assemble(state)
        model.realizePosition(state) # much faster (IK already done, no need to compute it again)

        if calculate_com_vel:
            CoM_pos_array[n, :] = model.calcMassCenterPosition(state).to_numpy()  # add the CoM position for this frame

        # Use state of model to get body coordinates in ground
        loc_rot_frame = []
        for b in bodies:
            H_swig = b.getTransformInGround(state)
            T = H_swig.T().to_numpy()
            R_swig = H_swig.R()
            R = np.array([[R_swig.get(0,0), R_swig.get(0,1), R_swig.get(0,2)],
                [R_swig.get(1,0), R_swig.get(1,1), R_swig.get(1,2)],
                [R_swig.get(2,0), R_swig.get(2,1), R_swig.get(2,2)]])
            H = np.block([ [R,T.reshape(3,1)], [np.zeros(3), 1] ])
            
            # y-up to z-up
            if direction=='zup':
                H = H_zup @ H
            
            # Convert matrix to loc and rot, and export to csv
            loc_x, loc_y, loc_z = H[0:3,3]
            R_mat = H[0:3,0:3]
            sy = np.sqrt(R_mat[1,0]**2 +  R_mat[0,0]**2) # singularity when y angle is +/- pi/2
            if sy>1e-6:
                rot_x = np.arctan2(R_mat[2,1], R_mat[2,2])
                rot_y = np.arctan2(-R_mat[2,0], sy)
                rot_z = np.arctan2(R_mat[1,0], R_mat[0,0])
            else: # to be verified
                rot_x = np.arctan2(-R_mat[1,2], R_mat[1,1])
                rot_y = np.arctan2(-R[2,0], sy)
                rot_z = 0
            loc_rot_frame.extend([loc_x, loc_y, loc_z, rot_x, rot_y, rot_z])
        
        loc_rot_frame_all.append(loc_rot_frame)

    # Create arrays and headers
    loc_rot_frame_all_np = np.array(loc_rot_frame_all)
    loc_rot_frame_all_np = np.insert(loc_rot_frame_all_np, 0, times, axis=1) # insert time column
    bodyHeader = 'times, ' + ''.join([f'{b}_x, {b}_y, {b}_z, {b}_rotx, {b}_roty, {b}_rotz, ' for b in bodyNames])[:-2]

    # Unwrap angles
    for n,col in enumerate(bodyHeader.split(', ')):
        if '_rot' in col:
            loc_rot_frame_all_np[:,n] = np.unwrap(loc_rot_frame_all_np[:,n],period=2*np.pi)

    # calculate CoM velocity using the position and time data
    if calculate_com_vel:

        # filter the CoM position data before calculating the velocity as small noise can cause large spikes in the velocity
        framerate = round(1 / np.diff(np.array(times)).mean(), 0)
        b, a = signal.butter(CoM_order/2, CoM_cutoff/(0.5*framerate), 'low', analog=False)
        CoM_pos_array = signal.filtfilt(b, a, CoM_pos_array, axis=0)

        # calculate the velocity of the CoM using the numpy gradient function
        CoM_vel_array = np.gradient(CoM_pos_array, np.array(times), axis=0)

        if save_com_file:
            # save the CoM position, velocity, and time to a file
            com_data = np.hstack((np.array(times).reshape(-1, 1), CoM_pos_array, CoM_vel_array))
            np.savetxt(com_output_file, com_data, delimiter=',', header='time,CoMx,CoMy,CoMz,CoMxVel,CoMyVel,CoMzVel')
            print(f'CoM file saved to {com_output_file}.\n')

    # Export to csv
    np.savetxt(os.path.splitext(output_csv_file)[0]+'.csv', loc_rot_frame_all_np, delimiter=',', header=bodyHeader)
    print(f'CSV file generated at {os.path.splitext(output_csv_file)[0]+".csv"}.\n')

    if calculate_com_vel:
        return CoM_pos_array, CoM_vel_array
    
if __name__ == '__main__':
    main()
